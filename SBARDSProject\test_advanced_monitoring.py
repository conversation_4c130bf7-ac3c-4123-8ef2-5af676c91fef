#!/usr/bin/env python3
"""
SBARDS Advanced Monitoring System Test
Comprehensive test of the advanced API hooking and monitoring capabilities

This script tests:
- Advanced Monitoring Engine
- System Call Monitoring
- File Access Tracking
- Network Analysis
- Configuration Monitoring
- Real-time Threat Detection
"""

import os
import sys
import json
import asyncio
import tempfile
import logging
import time
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger("SBARDS.AdvancedMonitoringTest")

def load_test_config():
    """Load test configuration"""
    config_path = project_root / "config_advanced_monitoring.json"
    
    if config_path.exists():
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        # Fallback configuration
        config = {
            "dynamic_analysis": {
                "enabled": True,
                "monitoring": {
                    "api_hooking": {"enabled": True, "hook_level": "user"},
                    "file_system": {"enabled": True},
                    "network": {"enabled": True},
                    "registry": {"enabled": True}
                },
                "behavioral_analysis": {"enabled": True, "ml_enabled": False},
                "cpp_components": {"enabled": True}
            }
        }
    
    # Adjust for testing
    config["dynamic_analysis"]["monitoring"]["api_hooking"]["hook_level"] = "user"
    config["dynamic_analysis"]["behavioral_analysis"]["ml_enabled"] = False
    config["dynamic_analysis"]["cuckoo"] = {"enabled": False}
    
    return config

def create_test_files():
    """Create test files for monitoring"""
    test_dir = tempfile.mkdtemp(prefix="sbards_test_")
    
    # Create various test files
    test_files = {
        "normal.txt": "This is a normal text file for testing.",
        "executable.exe": b"\x4d\x5a\x90\x00",  # PE header
        "script.bat": "@echo off\necho Test script\npause",
        "config.ini": "[Settings]\ntest=value\n",
        "sensitive.key": "-----BEGIN PRIVATE KEY-----\ntest\n-----END PRIVATE KEY-----"
    }
    
    created_files = []
    for filename, content in test_files.items():
        file_path = Path(test_dir) / filename
        
        if isinstance(content, str):
            file_path.write_text(content)
        else:
            file_path.write_bytes(content)
        
        created_files.append(str(file_path))
    
    return test_dir, created_files

async def test_monitoring_engine():
    """Test the Advanced Monitoring Engine"""
    logger.info("Testing Advanced Monitoring Engine...")
    
    try:
        from phases.dynamic_analysis.advanced_monitoring_engine import AdvancedMonitoringEngine
        
        config = load_test_config()
        engine = AdvancedMonitoringEngine(config)
        
        # Test initialization
        if not engine.is_monitoring_active():
            logger.info("✓ Engine initialized successfully")
        else:
            logger.error("✗ Engine should not be active initially")
            return False
        
        # Test starting monitoring
        success = await engine.start_monitoring()
        if success and engine.is_monitoring_active():
            logger.info("✓ Monitoring started successfully")
        else:
            logger.error("✗ Failed to start monitoring")
            return False
        
        # Let monitoring run for a short time
        await asyncio.sleep(5)
        
        # Test getting results
        results = await engine.get_monitoring_results()
        if "monitoring_active" in results and results["monitoring_active"]:
            logger.info("✓ Monitoring results retrieved successfully")
        else:
            logger.error("✗ Failed to get monitoring results")
            return False
        
        # Test threat analysis
        threat_analysis = await engine.get_threat_analysis()
        if "overall_threat_level" in threat_analysis:
            logger.info(f"✓ Threat analysis completed: {threat_analysis['overall_threat_level']}")
        else:
            logger.error("✗ Failed to get threat analysis")
            return False
        
        # Test performance metrics
        metrics = engine.get_performance_metrics()
        if "monitoring_active" in metrics:
            logger.info("✓ Performance metrics retrieved")
        else:
            logger.error("✗ Failed to get performance metrics")
            return False
        
        # Test stopping monitoring
        await engine.stop_monitoring()
        if not engine.is_monitoring_active():
            logger.info("✓ Monitoring stopped successfully")
        else:
            logger.error("✗ Failed to stop monitoring")
            return False
        
        return True
        
    except ImportError as e:
        logger.error(f"✗ Failed to import Advanced Monitoring Engine: {e}")
        return False
    except Exception as e:
        logger.error(f"✗ Advanced Monitoring Engine test failed: {e}")
        return False

async def test_file_monitoring():
    """Test file access monitoring"""
    logger.info("Testing File Access Monitoring...")
    
    try:
        from phases.dynamic_analysis.advanced_monitoring_engine import AdvancedMonitoringEngine
        
        config = load_test_config()
        engine = AdvancedMonitoringEngine(config)
        
        # Start monitoring
        await engine.start_monitoring()
        
        # Create test files and perform operations
        test_dir, test_files = create_test_files()
        
        # Perform file operations
        for file_path in test_files:
            try:
                # Read file
                with open(file_path, 'rb') as f:
                    content = f.read()
                
                # Write to file
                with open(file_path, 'ab') as f:
                    f.write(b"\nTest append")
                
                # Rename file
                new_path = file_path + ".bak"
                os.rename(file_path, new_path)
                os.rename(new_path, file_path)
                
            except Exception as e:
                logger.warning(f"File operation failed: {e}")
        
        # Wait for events to be processed
        await asyncio.sleep(3)
        
        # Check for file events
        results = await engine.get_monitoring_results()
        file_events = results.get("file_events", [])
        
        if len(file_events) > 0:
            logger.info(f"✓ File monitoring detected {len(file_events)} events")
            
            # Check for different operation types
            operations = set(event.get("operation", "") for event in file_events)
            logger.info(f"  Operations detected: {operations}")
            
        else:
            logger.warning("⚠ No file events detected (may be expected in test environment)")
        
        # Cleanup
        await engine.stop_monitoring()
        import shutil
        shutil.rmtree(test_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        logger.error(f"✗ File monitoring test failed: {e}")
        return False

async def test_network_monitoring():
    """Test network monitoring"""
    logger.info("Testing Network Monitoring...")
    
    try:
        from phases.dynamic_analysis.advanced_monitoring_engine import AdvancedMonitoringEngine
        
        config = load_test_config()
        engine = AdvancedMonitoringEngine(config)
        
        # Start monitoring
        await engine.start_monitoring()
        
        # Simulate network activity
        import socket
        import threading
        
        def create_connections():
            try:
                # Create some test connections
                for port in [80, 443, 8080]:
                    try:
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(1)
                        # Try to connect to common services (will likely fail, but creates events)
                        sock.connect_ex(("127.0.0.1", port))
                        sock.close()
                    except:
                        pass
                
                # Create UDP socket
                try:
                    udp_sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    udp_sock.sendto(b"test", ("127.0.0.1", 53))
                    udp_sock.close()
                except:
                    pass
                    
            except Exception as e:
                logger.debug(f"Network test connection failed: {e}")
        
        # Run network activity in background
        network_thread = threading.Thread(target=create_connections)
        network_thread.start()
        network_thread.join()
        
        # Wait for events to be processed
        await asyncio.sleep(3)
        
        # Check for network events
        results = await engine.get_monitoring_results()
        network_events = results.get("network_events", [])
        
        if len(network_events) > 0:
            logger.info(f"✓ Network monitoring detected {len(network_events)} events")
            
            # Check for different protocols
            protocols = set(event.get("protocol", "") for event in network_events)
            logger.info(f"  Protocols detected: {protocols}")
            
        else:
            logger.warning("⚠ No network events detected (may be expected in test environment)")
        
        # Test C2 indicators
        c2_indicators = engine.get_c2_indicators()
        logger.info(f"  C2 indicators: {len(c2_indicators)}")
        
        await engine.stop_monitoring()
        return True
        
    except Exception as e:
        logger.error(f"✗ Network monitoring test failed: {e}")
        return False

async def test_threat_detection():
    """Test threat detection capabilities"""
    logger.info("Testing Threat Detection...")
    
    try:
        from phases.dynamic_analysis.advanced_monitoring_engine import (
            AdvancedMonitoringEngine, SystemCallEvent, FileAccessEvent, 
            NetworkEvent, ConfigurationEvent
        )
        
        config = load_test_config()
        engine = AdvancedMonitoringEngine(config)
        
        # Start monitoring
        await engine.start_monitoring()
        
        # Simulate suspicious activities by directly adding events
        # (In real scenario, these would come from C++ components)
        
        # Simulate ransomware-like file activity
        for i in range(15):
            file_event = FileAccessEvent(
                timestamp=datetime.now(),
                operation="write",
                file_path=f"/tmp/test_file_{i}.encrypted",
                process_name="suspicious.exe",
                process_id=1234,
                access_mode=0,
                bytes_transferred=1024*1024,  # 1MB
                is_sensitive_file=False,
                access_pattern="bulk",
                encryption_detected=True,
                threat_level="high",
                indicators=["File encryption detected", "Suspicious extension: .encrypted"]
            )
            
            with engine.event_lock:
                engine.file_events.append(file_event)
        
        # Simulate C2 network activity
        for i in range(10):
            network_event = NetworkEvent(
                timestamp=datetime.now(),
                protocol="TCP",
                local_address="*************",
                local_port=12345,
                remote_address="suspicious-c2.com",
                remote_port=4444,
                connection_state="ESTABLISHED",
                bytes_sent=1024,
                bytes_received=512,
                is_encrypted=True,
                is_suspicious=True,
                c2_indicators=["Suspicious port: 4444", "Regular beacon pattern"],
                dns_queries=["suspicious-c2.com"],
                http_requests=[],
                threat_level="high"
            )
            
            with engine.event_lock:
                engine.network_events.append(network_event)
        
        # Simulate persistence mechanism
        config_event = ConfigurationEvent(
            timestamp=datetime.now(),
            change_type="registry",
            key_path="HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
            value_name="Malware",
            old_value="",
            new_value="C:\\malware\\evil.exe",
            process_name="malware.exe",
            process_id=5678,
            is_security_related=False,
            is_persistence_mechanism=True,
            threat_level="high",
            indicators=["Persistence mechanism", "Startup modification"]
        )
        
        with engine.event_lock:
            engine.config_events.append(config_event)
        
        # Wait for analysis
        await asyncio.sleep(2)
        
        # Get threat analysis
        threat_analysis = await engine.get_threat_analysis()
        
        logger.info(f"✓ Overall threat level: {threat_analysis['overall_threat_level']}")
        
        threat_categories = threat_analysis.get("threat_categories", {})
        logger.info(f"  Ransomware indicators: {threat_categories.get('ransomware_indicators', 0)}")
        logger.info(f"  C2 communication: {threat_categories.get('c2_communication', 0)}")
        logger.info(f"  Persistence mechanisms: {threat_categories.get('persistence_mechanisms', 0)}")
        
        recommendations = threat_analysis.get("recommendations", [])
        logger.info(f"  Recommendations: {len(recommendations)}")
        for rec in recommendations[:3]:  # Show first 3
            logger.info(f"    - {rec}")
        
        # Check if threats were detected
        if threat_analysis['overall_threat_level'] in ['high', 'critical']:
            logger.info("✓ High-level threats correctly detected")
        else:
            logger.warning(f"⚠ Expected high threat level, got: {threat_analysis['overall_threat_level']}")
        
        await engine.stop_monitoring()
        return True
        
    except Exception as e:
        logger.error(f"✗ Threat detection test failed: {e}")
        return False

async def test_data_export():
    """Test data export functionality"""
    logger.info("Testing Data Export...")
    
    try:
        from phases.dynamic_analysis.advanced_monitoring_engine import AdvancedMonitoringEngine
        
        config = load_test_config()
        engine = AdvancedMonitoringEngine(config)
        
        # Start monitoring briefly to generate some data
        await engine.start_monitoring()
        await asyncio.sleep(1)
        
        # Test JSON export
        json_file = tempfile.mktemp(suffix=".json")
        success = await engine.export_monitoring_data(json_file, "json")
        
        if success and os.path.exists(json_file):
            logger.info("✓ JSON export successful")
            
            # Verify JSON content
            with open(json_file, 'r') as f:
                data = json.load(f)
                if "monitoring_active" in data:
                    logger.info("  JSON data structure valid")
                else:
                    logger.warning("  JSON data structure invalid")
            
            os.unlink(json_file)
        else:
            logger.error("✗ JSON export failed")
            return False
        
        # Test CSV export
        csv_file = tempfile.mktemp(suffix=".csv")
        success = await engine.export_monitoring_data(csv_file, "csv")
        
        if success and os.path.exists(csv_file):
            logger.info("✓ CSV export successful")
            os.unlink(csv_file)
        else:
            logger.error("✗ CSV export failed")
            return False
        
        await engine.stop_monitoring()
        return True
        
    except Exception as e:
        logger.error(f"✗ Data export test failed: {e}")
        return False

async def test_performance():
    """Test performance under load"""
    logger.info("Testing Performance Under Load...")
    
    try:
        from phases.dynamic_analysis.advanced_monitoring_engine import AdvancedMonitoringEngine
        
        config = load_test_config()
        engine = AdvancedMonitoringEngine(config)
        
        # Start monitoring
        start_time = time.time()
        await engine.start_monitoring()
        
        # Generate load by creating many events
        for i in range(100):
            # Create test files
            test_file = tempfile.mktemp()
            with open(test_file, 'w') as f:
                f.write(f"Test content {i}")
            
            # Read the file
            with open(test_file, 'r') as f:
                content = f.read()
            
            # Delete the file
            os.unlink(test_file)
            
            if i % 20 == 0:
                await asyncio.sleep(0.1)  # Brief pause
        
        # Wait for processing
        await asyncio.sleep(2)
        
        # Check performance metrics
        metrics = engine.get_performance_metrics()
        
        logger.info(f"✓ Performance test completed")
        logger.info(f"  Memory usage: {metrics.get('memory_usage_mb', 0):.1f} MB")
        logger.info(f"  CPU usage: {metrics.get('cpu_usage_percent', 0):.1f}%")
        logger.info(f"  Active threads: {metrics.get('active_threads', 0)}")
        
        event_counts = metrics.get('event_counts', {})
        total_events = sum(event_counts.values())
        logger.info(f"  Total events processed: {total_events}")
        
        elapsed_time = time.time() - start_time
        if total_events > 0:
            events_per_second = total_events / elapsed_time
            logger.info(f"  Events per second: {events_per_second:.1f}")
        
        await engine.stop_monitoring()
        return True
        
    except Exception as e:
        logger.error(f"✗ Performance test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("SBARDS Advanced Monitoring System Test")
    logger.info("=" * 60)
    
    tests = [
        ("Advanced Monitoring Engine", test_monitoring_engine),
        ("File Access Monitoring", test_file_monitoring),
        ("Network Monitoring", test_network_monitoring),
        ("Threat Detection", test_threat_detection),
        ("Data Export", test_data_export),
        ("Performance Under Load", test_performance),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Testing {test_name} ---")
        try:
            result = await test_func()
            results[test_name] = result
            if result:
                logger.info(f"✓ {test_name}: PASSED")
            else:
                logger.error(f"✗ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"✗ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name:.<40} {status}")
    
    logger.info("-" * 60)
    logger.info(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed!")
        return 0
    else:
        logger.warning(f"⚠ {total - passed} test(s) failed")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test suite failed: {e}")
        sys.exit(1)
