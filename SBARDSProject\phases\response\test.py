print("SBARDS Response System Test")
import response
print("Response module loaded!")

# Test ResponseLayer
from response import ResponseLayer

config = {"response": {"quarantine_directory": "test_q", "honeypot_directory": "test_h", "notification_methods": ["log"]}}
rs = ResponseLayer(config)
print("ResponseLayer created successfully!")

# Test alert
result = rs.send_alerts({"workflow_id": "test", "final_decision": {"decision": "ALLOWED", "reason": "test"}})
print(f"Alert result: {result}")

print("✅ SBARDS Response System is working!")
