/**
 * SBARDS Advanced Network Monitor Implementation
 * Deep packet analysis, protocol inspection, C2 detection
 */

#include "api_hooking.hpp"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <regex>
#include <thread>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #include <iphlpapi.h>
    #include <wininet.h>
    #pragma comment(lib, "ws2_32.lib")
    #pragma comment(lib, "iphlpapi.lib")
    #pragma comment(lib, "wininet.lib")
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <netinet/ip.h>
    #include <netinet/tcp.h>
    #include <netinet/udp.h>
    #include <arpa/inet.h>
    #include <netdb.h>
    #include <pcap.h>
#endif

namespace sbards {
namespace hooking {

// NetworkMonitor Implementation
NetworkMonitor::NetworkMonitor(const HookConfig& config)
    : config_(config), monitoring_(false) {
    
    // Initialize suspicious domains
    for (const auto& domain : config_.suspicious_domains) {
        suspicious_domains_.insert(domain);
    }
    
    // Add default suspicious patterns
    suspicious_domains_.insert("*.bit");
    suspicious_domains_.insert("*.onion");
    suspicious_domains_.insert("tempuri.org");
    suspicious_domains_.insert("example.com");
    
    // Initialize blocked IPs
    for (const auto& ip : config_.blocked_ips) {
        blocked_ips_.insert(ip);
    }
}

NetworkMonitor::~NetworkMonitor() {
    stop_monitoring();
}

bool NetworkMonitor::start_monitoring() {
    if (monitoring_) {
        return true;
    }
    
    try {
#ifdef _WIN32
        monitor_windows_network();
#else
        monitor_linux_network();
#endif
        monitoring_ = true;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to start network monitoring: " << e.what() << std::endl;
        return false;
    }
}

void NetworkMonitor::stop_monitoring() {
    monitoring_ = false;
}

bool NetworkMonitor::is_monitoring() const {
    return monitoring_;
}

std::vector<NetworkConnectionInfo> NetworkMonitor::get_network_events() const {
    std::lock_guard<std::mutex> lock(events_mutex_);
    return events_;
}

void NetworkMonitor::set_event_callback(std::function<void(const NetworkConnectionInfo&)> callback) {
    callback_ = callback;
}

bool NetworkMonitor::analyze_dns_traffic(const std::vector<uint8_t>& packet_data) {
    if (packet_data.size() < 12) return false; // Minimum DNS header size
    
    // Parse DNS header
    uint16_t transaction_id = (packet_data[0] << 8) | packet_data[1];
    uint16_t flags = (packet_data[2] << 8) | packet_data[3];
    uint16_t questions = (packet_data[4] << 8) | packet_data[5];
    uint16_t answers = (packet_data[6] << 8) | packet_data[7];
    
    // Check for suspicious DNS patterns
    bool is_response = (flags & 0x8000) != 0;
    bool is_recursive = (flags & 0x0100) != 0;
    
    // Analyze DNS queries for suspicious domains
    if (!is_response && questions > 0) {
        // Parse DNS question section (simplified)
        size_t offset = 12;
        std::string domain_name;
        
        // Extract domain name (simplified parsing)
        while (offset < packet_data.size() && packet_data[offset] != 0) {
            uint8_t label_length = packet_data[offset++];
            if (label_length > 63) break; // Invalid label length
            
            for (int i = 0; i < label_length && offset < packet_data.size(); ++i) {
                domain_name += static_cast<char>(packet_data[offset++]);
            }
            
            if (offset < packet_data.size() && packet_data[offset] != 0) {
                domain_name += ".";
            }
        }
        
        // Check if domain is suspicious
        return is_suspicious_domain(domain_name);
    }
    
    return false;
}

bool NetworkMonitor::analyze_http_traffic(const std::vector<uint8_t>& packet_data) {
    std::string http_data(packet_data.begin(), packet_data.end());
    
    // Check for HTTP methods
    std::vector<std::string> http_methods = {"GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS"};
    bool is_http = false;
    
    for (const auto& method : http_methods) {
        if (http_data.find(method + " ") == 0) {
            is_http = true;
            break;
        }
    }
    
    if (!is_http) return false;
    
    // Analyze HTTP headers for suspicious patterns
    std::vector<std::string> suspicious_patterns = {
        "User-Agent: curl",
        "User-Agent: wget",
        "User-Agent: python",
        "X-Forwarded-For:",
        "X-Real-IP:",
        "Authorization: Bearer",
        "Content-Type: application/octet-stream"
    };
    
    for (const auto& pattern : suspicious_patterns) {
        if (http_data.find(pattern) != std::string::npos) {
            return true;
        }
    }
    
    // Check for base64 encoded content (potential data exfiltration)
    std::regex base64_pattern(R"([A-Za-z0-9+/]{20,}={0,2})");
    if (std::regex_search(http_data, base64_pattern)) {
        return true;
    }
    
    return false;
}

bool NetworkMonitor::analyze_tls_traffic(const std::vector<uint8_t>& packet_data) {
    if (packet_data.size() < 5) return false;
    
    // Check TLS record header
    uint8_t content_type = packet_data[0];
    uint16_t version = (packet_data[1] << 8) | packet_data[2];
    uint16_t length = (packet_data[3] << 8) | packet_data[4];
    
    // Valid TLS content types
    std::vector<uint8_t> valid_types = {20, 21, 22, 23}; // change_cipher_spec, alert, handshake, application_data
    
    bool is_tls = std::find(valid_types.begin(), valid_types.end(), content_type) != valid_types.end();
    
    if (!is_tls) return false;
    
    // Analyze TLS handshake for suspicious patterns
    if (content_type == 22 && packet_data.size() > 9) { // Handshake
        uint8_t handshake_type = packet_data[5];
        
        if (handshake_type == 1) { // Client Hello
            // Analyze SNI (Server Name Indication) for suspicious domains
            // This is a simplified implementation
            size_t offset = 43; // Skip to extensions
            
            if (offset < packet_data.size()) {
                // Look for SNI extension (simplified)
                std::string sni_data(packet_data.begin() + offset, packet_data.end());
                
                for (const auto& domain : suspicious_domains_) {
                    if (sni_data.find(domain) != std::string::npos) {
                        return true;
                    }
                }
            }
        }
    }
    
    return false;
}

bool NetworkMonitor::detect_c2_communication(const NetworkConnectionInfo& connection) {
    // C2 detection heuristics
    
    // Check for suspicious ports
    std::vector<uint16_t> suspicious_ports = {4444, 5555, 6666, 7777, 8080, 9999};
    if (std::find(suspicious_ports.begin(), suspicious_ports.end(), connection.remote_port) != suspicious_ports.end()) {
        return true;
    }
    
    // Check for suspicious domains/IPs
    if (is_suspicious_domain(connection.remote_address) || 
        blocked_ips_.find(connection.remote_address) != blocked_ips_.end()) {
        return true;
    }
    
    // Check for encrypted connections to unusual ports
    if (connection.is_encrypted && connection.remote_port != 443 && connection.remote_port != 993 && connection.remote_port != 995) {
        return true;
    }
    
    // Check for regular beacon patterns
    auto it = connection_history_.find(connection.remote_address);
    if (it != connection_history_.end()) {
        const auto& history = it->second;
        
        if (history.size() > 5) {
            // Check for regular intervals (beacon behavior)
            std::vector<std::chrono::seconds> intervals;
            for (size_t i = 1; i < history.size(); ++i) {
                // In real implementation, calculate time intervals
                intervals.push_back(std::chrono::seconds(60)); // Placeholder
            }
            
            // Check if intervals are regular (within 10% variance)
            if (!intervals.empty()) {
                auto avg_interval = intervals[0];
                bool regular = true;
                
                for (const auto& interval : intervals) {
                    auto diff = std::abs((interval - avg_interval).count());
                    if (diff > avg_interval.count() * 0.1) {
                        regular = false;
                        break;
                    }
                }
                
                if (regular) {
                    return true; // Regular beacon detected
                }
            }
        }
    }
    
    return false;
}

std::vector<std::string> NetworkMonitor::extract_c2_indicators(const std::vector<NetworkConnectionInfo>& connections) {
    std::vector<std::string> indicators;
    std::unordered_map<std::string, int> domain_counts;
    std::unordered_map<std::string, int> ip_counts;
    
    for (const auto& conn : connections) {
        if (detect_c2_communication(conn)) {
            indicators.push_back("C2 communication detected to " + conn.remote_address + ":" + std::to_string(conn.remote_port));
        }
        
        // Count connections to domains/IPs
        domain_counts[conn.remote_address]++;
        ip_counts[conn.remote_address]++;
    }
    
    // Detect domains/IPs with high connection counts
    for (const auto& pair : domain_counts) {
        if (pair.second > 10) {
            indicators.push_back("High connection count to " + pair.first + " (" + std::to_string(pair.second) + " connections)");
        }
    }
    
    return indicators;
}

bool NetworkMonitor::is_suspicious_domain(const std::string& domain) {
    // Check exact matches
    if (suspicious_domains_.find(domain) != suspicious_domains_.end()) {
        return true;
    }
    
    // Check wildcard patterns
    for (const auto& pattern : suspicious_domains_) {
        if (pattern.front() == '*') {
            std::string suffix = pattern.substr(1);
            if (domain.size() >= suffix.size() && 
                domain.substr(domain.size() - suffix.size()) == suffix) {
                return true;
            }
        }
    }
    
    // Check for DGA (Domain Generation Algorithm) patterns
    std::regex dga_pattern(R"([a-z]{8,20}\.(com|net|org|info))");
    if (std::regex_match(domain, dga_pattern)) {
        // Additional heuristics for DGA detection
        int vowel_count = 0;
        int consonant_count = 0;
        
        for (char c : domain) {
            if (c == 'a' || c == 'e' || c == 'i' || c == 'o' || c == 'u') {
                vowel_count++;
            } else if (std::isalpha(c)) {
                consonant_count++;
            }
        }
        
        // Suspicious if too few vowels (typical of DGA domains)
        if (consonant_count > 0 && (double)vowel_count / consonant_count < 0.2) {
            return true;
        }
    }
    
    return false;
}

bool NetworkMonitor::is_encrypted_connection(const NetworkConnectionInfo& connection) {
    // Check for common encrypted ports
    std::vector<uint16_t> encrypted_ports = {443, 993, 995, 465, 587, 636, 989, 990};
    
    if (std::find(encrypted_ports.begin(), encrypted_ports.end(), connection.remote_port) != encrypted_ports.end()) {
        return true;
    }
    
    return connection.is_encrypted;
}

void NetworkMonitor::analyze_connection_patterns() {
    std::lock_guard<std::mutex> lock(events_mutex_);
    
    // Analyze patterns in connection history
    for (auto& pair : connection_history_) {
        const std::string& address = pair.first;
        std::vector<NetworkConnectionInfo>& connections = pair.second;
        
        if (connections.size() > 3) {
            // Check for beacon patterns
            bool potential_beacon = true;
            
            // Simple beacon detection: regular intervals
            for (size_t i = 1; i < connections.size(); ++i) {
                // In real implementation, check time intervals
                // For now, just check if connections are to the same port
                if (connections[i].remote_port != connections[i-1].remote_port) {
                    potential_beacon = false;
                    break;
                }
            }
            
            if (potential_beacon) {
                // Mark as suspicious
                for (auto& conn : connections) {
                    conn.is_suspicious = true;
                    conn.c2_indicators = "Regular beacon pattern detected";
                }
            }
        }
    }
}

#ifdef _WIN32
void NetworkMonitor::monitor_windows_network() {
    setup_winsock_hooks();
}

void NetworkMonitor::setup_winsock_hooks() {
    std::thread monitor_thread([this]() {
        while (monitoring_) {
            // Monitor network connections using Windows APIs
            // This is a simplified implementation
            
            // Get network table
            PMIB_TCPTABLE_OWNER_PID tcp_table = nullptr;
            DWORD size = 0;
            
            if (GetExtendedTcpTable(nullptr, &size, FALSE, AF_INET, TCP_TABLE_OWNER_PID_ALL, 0) == ERROR_INSUFFICIENT_BUFFER) {
                tcp_table = (PMIB_TCPTABLE_OWNER_PID)malloc(size);
                
                if (GetExtendedTcpTable(tcp_table, &size, FALSE, AF_INET, TCP_TABLE_OWNER_PID_ALL, 0) == NO_ERROR) {
                    for (DWORD i = 0; i < tcp_table->dwNumEntries; ++i) {
                        MIB_TCPROW_OWNER_PID& row = tcp_table->table[i];
                        
                        NetworkConnectionInfo conn_info;
                        conn_info.protocol = "TCP";
                        
                        struct in_addr local_addr, remote_addr;
                        local_addr.s_addr = row.dwLocalAddr;
                        remote_addr.s_addr = row.dwRemoteAddr;
                        
                        conn_info.local_address = inet_ntoa(local_addr);
                        conn_info.remote_address = inet_ntoa(remote_addr);
                        conn_info.local_port = ntohs((u_short)row.dwLocalPort);
                        conn_info.remote_port = ntohs((u_short)row.dwRemotePort);
                        
                        // Determine connection state
                        switch (row.dwState) {
                            case MIB_TCP_STATE_ESTAB:
                                conn_info.connection_state = "ESTABLISHED";
                                break;
                            case MIB_TCP_STATE_LISTEN:
                                conn_info.connection_state = "LISTENING";
                                break;
                            default:
                                conn_info.connection_state = "OTHER";
                                break;
                        }
                        
                        conn_info.is_encrypted = is_encrypted_connection(conn_info);
                        conn_info.is_suspicious = detect_c2_communication(conn_info);
                        
                        {
                            std::lock_guard<std::mutex> lock(events_mutex_);
                            events_.push_back(conn_info);
                            connection_history_[conn_info.remote_address].push_back(conn_info);
                            
                            if (events_.size() > 10000) {
                                events_.erase(events_.begin(), events_.begin() + 1000);
                            }
                        }
                        
                        if (callback_) {
                            callback_(conn_info);
                        }
                    }
                }
                
                free(tcp_table);
            }
            
            std::this_thread::sleep_for(std::chrono::seconds(5));
        }
    });
    
    monitor_thread.detach();
}
#else
void NetworkMonitor::monitor_linux_network() {
    setup_netfilter_hooks();
}

void NetworkMonitor::setup_netfilter_hooks() {
    std::thread monitor_thread([this]() {
        // Initialize packet capture
        char errbuf[PCAP_ERRBUF_SIZE];
        pcap_t* handle = pcap_open_live("any", BUFSIZ, 1, 1000, errbuf);
        
        if (handle == nullptr) {
            std::cerr << "Failed to open packet capture: " << errbuf << std::endl;
            return;
        }
        
        // Set filter for TCP/UDP traffic
        struct bpf_program fp;
        char filter_exp[] = "tcp or udp";
        
        if (pcap_compile(handle, &fp, filter_exp, 0, PCAP_NETMASK_UNKNOWN) == -1) {
            std::cerr << "Failed to compile filter" << std::endl;
            pcap_close(handle);
            return;
        }
        
        if (pcap_setfilter(handle, &fp) == -1) {
            std::cerr << "Failed to set filter" << std::endl;
            pcap_close(handle);
            return;
        }
        
        // Packet capture loop
        while (monitoring_) {
            struct pcap_pkthdr header;
            const u_char* packet = pcap_next(handle, &header);
            
            if (packet != nullptr) {
                process_packet(packet, header.len);
            }
        }
        
        pcap_close(handle);
    });
    
    monitor_thread.detach();
}
#endif

void NetworkMonitor::process_packet(const uint8_t* packet_data, size_t packet_size) {
    if (packet_size < 20) return; // Minimum IP header size
    
    // Parse IP header (simplified)
    uint8_t version = (packet_data[0] >> 4) & 0x0F;
    if (version != 4) return; // Only IPv4 for now
    
    uint8_t protocol = packet_data[9];
    uint32_t src_ip = *((uint32_t*)(packet_data + 12));
    uint32_t dst_ip = *((uint32_t*)(packet_data + 16));
    
    struct in_addr src_addr, dst_addr;
    src_addr.s_addr = src_ip;
    dst_addr.s_addr = dst_ip;
    
    NetworkConnectionInfo conn_info;
    conn_info.local_address = inet_ntoa(src_addr);
    conn_info.remote_address = inet_ntoa(dst_addr);
    
    if (protocol == IPPROTO_TCP) {
        conn_info.protocol = "TCP";
        // Parse TCP header for ports
        if (packet_size >= 34) {
            conn_info.local_port = ntohs(*((uint16_t*)(packet_data + 20)));
            conn_info.remote_port = ntohs(*((uint16_t*)(packet_data + 22)));
        }
    } else if (protocol == IPPROTO_UDP) {
        conn_info.protocol = "UDP";
        // Parse UDP header for ports
        if (packet_size >= 28) {
            conn_info.local_port = ntohs(*((uint16_t*)(packet_data + 20)));
            conn_info.remote_port = ntohs(*((uint16_t*)(packet_data + 22)));
        }
    }
    
    conn_info.bytes_sent = packet_size;
    conn_info.is_encrypted = is_encrypted_connection(conn_info);
    conn_info.is_suspicious = detect_c2_communication(conn_info);
    
    {
        std::lock_guard<std::mutex> lock(events_mutex_);
        events_.push_back(conn_info);
        connection_history_[conn_info.remote_address].push_back(conn_info);
        
        if (events_.size() > 10000) {
            events_.erase(events_.begin(), events_.begin() + 1000);
        }
    }
    
    if (callback_) {
        callback_(conn_info);
    }
}

} // namespace hooking
} // namespace sbards
