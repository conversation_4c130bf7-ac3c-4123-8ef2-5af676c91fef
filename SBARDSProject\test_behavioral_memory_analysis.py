#!/usr/bin/env python3
"""
SBARDS Behavioral and Memory Analysis Test
Comprehensive test of advanced behavioral pattern detection and memory forensics

This script tests:
- Resource usage monitoring and analysis
- Behavioral pattern detection (ransomware, APT, etc.)
- Process and service analysis
- Memory forensics and injection detection
- Volatility framework integration
- Real-time threat correlation
"""

import os
import sys
import json
import asyncio
import tempfile
import logging
import time
import subprocess
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger("SBARDS.BehavioralMemoryTest")

def load_test_config():
    """Load test configuration"""
    config = {
        "behavioral_analysis": {
            "enabled": True,
            "real_time_analysis": True,
            "cpu_threshold": 50.0,  # Lower for testing
            "memory_threshold_mb": 512.0,  # Lower for testing
            "io_threshold_mbps": 10.0,  # Lower for testing
            "network_threshold_mbps": 5.0,  # Lower for testing
            "memory_leak_threshold_mb": 50.0  # Lower for testing
        },
        "memory_analysis": {
            "enabled": True,
            "volatility_enabled": False,  # Disable for testing
            "volatility_path": "/usr/local/bin/vol.py",
            "volatility_profile": "Win10x64_19041"
        }
    }
    
    return config

async def test_behavioral_analyzer():
    """Test the Behavioral and Memory Analyzer"""
    logger.info("Testing Behavioral and Memory Analyzer...")
    
    try:
        from phases.dynamic_analysis.behavioral_memory_analyzer import BehavioralMemoryAnalyzer
        
        config = load_test_config()
        analyzer = BehavioralMemoryAnalyzer(config)
        
        # Test initialization
        if not analyzer.is_monitoring_active():
            logger.info("✓ Analyzer initialized successfully")
        else:
            logger.error("✗ Analyzer should not be active initially")
            return False
        
        # Test starting analysis
        success = await analyzer.start_analysis()
        if success and analyzer.is_monitoring_active():
            logger.info("✓ Analysis started successfully")
        else:
            logger.error("✗ Failed to start analysis")
            return False
        
        # Let analysis run for a short time
        await asyncio.sleep(10)
        
        # Test getting results
        results = await analyzer.get_analysis_results()
        if "monitoring_active" in results and results["monitoring_active"]:
            logger.info("✓ Analysis results retrieved successfully")
            logger.info(f"  Resource metrics: {len(results.get('resource_metrics', []))}")
            logger.info(f"  Behavioral patterns: {len(results.get('behavioral_patterns', []))}")
            logger.info(f"  Process analyses: {len(results.get('process_analyses', {}))}")
        else:
            logger.error("✗ Failed to get analysis results")
            return False
        
        # Test threat summary
        threat_summary = await analyzer.get_threat_summary()
        if "overall_threat_level" in threat_summary:
            logger.info(f"✓ Threat summary completed: {threat_summary['overall_threat_level']}")
            logger.info(f"  Total patterns: {threat_summary.get('total_patterns', 0)}")
            logger.info(f"  High threat patterns: {threat_summary.get('high_threat_patterns', 0)}")
        else:
            logger.error("✗ Failed to get threat summary")
            return False
        
        # Test performance metrics
        metrics = analyzer.get_performance_metrics()
        if "monitoring_active" in metrics:
            logger.info("✓ Performance metrics retrieved")
            logger.info(f"  Active threads: {metrics.get('active_threads', 0)}")
            logger.info(f"  C++ bridge available: {metrics.get('cpp_bridge_available', False)}")
        else:
            logger.error("✗ Failed to get performance metrics")
            return False
        
        # Test stopping analysis
        await analyzer.stop_analysis()
        if not analyzer.is_monitoring_active():
            logger.info("✓ Analysis stopped successfully")
        else:
            logger.error("✗ Failed to stop analysis")
            return False
        
        return True
        
    except ImportError as e:
        logger.error(f"✗ Failed to import Behavioral Memory Analyzer: {e}")
        return False
    except Exception as e:
        logger.error(f"✗ Behavioral Memory Analyzer test failed: {e}")
        return False

async def test_resource_monitoring():
    """Test resource usage monitoring"""
    logger.info("Testing Resource Usage Monitoring...")
    
    try:
        from phases.dynamic_analysis.behavioral_memory_analyzer import BehavioralMemoryAnalyzer
        
        config = load_test_config()
        analyzer = BehavioralMemoryAnalyzer(config)
        
        # Start analysis
        await analyzer.start_analysis()
        
        # Create some CPU load to trigger monitoring
        def cpu_intensive_task():
            """Create CPU load for testing"""
            end_time = time.time() + 5  # Run for 5 seconds
            while time.time() < end_time:
                # Simple CPU-intensive calculation
                sum(i * i for i in range(1000))
        
        import threading
        cpu_thread = threading.Thread(target=cpu_intensive_task)
        cpu_thread.start()
        
        # Wait for monitoring to detect activity
        await asyncio.sleep(8)
        
        cpu_thread.join()
        
        # Check for resource metrics
        results = await analyzer.get_analysis_results()
        resource_metrics = results.get("resource_metrics", [])
        
        if len(resource_metrics) > 0:
            logger.info(f"✓ Resource monitoring detected {len(resource_metrics)} metrics")
            
            # Check for high CPU usage detection
            high_cpu_metrics = [m for m in resource_metrics if m.get("cpu_usage_percent", 0) > 10]
            if high_cpu_metrics:
                logger.info(f"  High CPU usage detected: {len(high_cpu_metrics)} instances")
            
            # Check for behavioral patterns
            behavioral_patterns = results.get("behavioral_patterns", [])
            if behavioral_patterns:
                logger.info(f"  Behavioral patterns detected: {len(behavioral_patterns)}")
                for pattern in behavioral_patterns[:3]:  # Show first 3
                    logger.info(f"    - {pattern.get('pattern_type', 'Unknown')}: {pattern.get('description', 'No description')}")
            
        else:
            logger.warning("⚠ No resource metrics detected (may be expected in test environment)")
        
        await analyzer.stop_analysis()
        return True
        
    except Exception as e:
        logger.error(f"✗ Resource monitoring test failed: {e}")
        return False

async def test_behavioral_pattern_detection():
    """Test behavioral pattern detection"""
    logger.info("Testing Behavioral Pattern Detection...")
    
    try:
        from phases.dynamic_analysis.behavioral_memory_analyzer import BehavioralMemoryAnalyzer
        
        config = load_test_config()
        analyzer = BehavioralMemoryAnalyzer(config)
        
        # Start analysis
        await analyzer.start_analysis()
        
        # Simulate suspicious activities
        def simulate_file_operations():
            """Simulate file operations that might trigger encryption detection"""
            temp_dir = tempfile.mkdtemp(prefix="sbards_test_")
            
            try:
                # Create and write to many files (simulate encryption activity)
                for i in range(20):
                    file_path = os.path.join(temp_dir, f"test_file_{i}.txt")
                    with open(file_path, 'w') as f:
                        f.write("Test data " * 1000)  # Write some data
                    
                    # Rename to suspicious extension
                    encrypted_path = file_path + ".encrypted"
                    os.rename(file_path, encrypted_path)
                    
                    time.sleep(0.1)  # Small delay
                
                # Clean up
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
                
            except Exception as e:
                logger.warning(f"File operation simulation failed: {e}")
        
        def simulate_network_activity():
            """Simulate network activity"""
            try:
                import socket
                
                # Create multiple connections (simulate lateral movement)
                for port in [80, 443, 8080, 9999]:
                    try:
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(1)
                        sock.connect_ex(("127.0.0.1", port))
                        sock.close()
                    except:
                        pass
                    
            except Exception as e:
                logger.warning(f"Network simulation failed: {e}")
        
        # Run simulations
        import threading
        file_thread = threading.Thread(target=simulate_file_operations)
        network_thread = threading.Thread(target=simulate_network_activity)
        
        file_thread.start()
        network_thread.start()
        
        # Wait for analysis
        await asyncio.sleep(15)
        
        file_thread.join()
        network_thread.join()
        
        # Check for detected patterns
        results = await analyzer.get_analysis_results()
        behavioral_patterns = results.get("behavioral_patterns", [])
        
        if behavioral_patterns:
            logger.info(f"✓ Behavioral patterns detected: {len(behavioral_patterns)}")
            
            # Check for specific pattern types
            pattern_types = [p.get("pattern_type", "") for p in behavioral_patterns]
            
            if "high_io_activity" in pattern_types:
                logger.info("  ✓ High I/O activity pattern detected")
            
            if "potential_file_encryption" in pattern_types:
                logger.info("  ✓ Potential file encryption pattern detected")
            
            # Show pattern details
            for pattern in behavioral_patterns[:5]:  # Show first 5
                logger.info(f"    Pattern: {pattern.get('pattern_type', 'Unknown')}")
                logger.info(f"      Confidence: {pattern.get('confidence_score', 0):.2f}")
                logger.info(f"      Description: {pattern.get('description', 'No description')}")
                
        else:
            logger.warning("⚠ No behavioral patterns detected")
        
        # Check threat summary
        threat_summary = await analyzer.get_threat_summary()
        logger.info(f"  Overall threat level: {threat_summary.get('overall_threat_level', 'unknown')}")
        
        recommendations = threat_summary.get("recommendations", [])
        if recommendations:
            logger.info(f"  Recommendations: {len(recommendations)}")
            for rec in recommendations[:3]:  # Show first 3
                logger.info(f"    - {rec}")
        
        await analyzer.stop_analysis()
        return True
        
    except Exception as e:
        logger.error(f"✗ Behavioral pattern detection test failed: {e}")
        return False

async def test_memory_analysis():
    """Test memory analysis capabilities"""
    logger.info("Testing Memory Analysis...")
    
    try:
        from phases.dynamic_analysis.behavioral_memory_analyzer import BehavioralMemoryAnalyzer
        
        config = load_test_config()
        config["memory_analysis"]["enabled"] = True
        analyzer = BehavioralMemoryAnalyzer(config)
        
        # Start analysis
        await analyzer.start_analysis()
        
        # Get current process for testing
        import os
        current_process_id = os.getpid()
        
        # Simulate memory analysis by creating some patterns first
        # This would trigger memory analysis of suspicious processes
        analyzer._create_behavioral_pattern(
            "test_pattern",
            "Test pattern for memory analysis",
            0.8,
            ["Test indicator"],
            {"process_id": current_process_id}
        )
        
        # Wait for memory analysis to run
        await asyncio.sleep(10)
        
        # Check for memory analysis results
        results = await analyzer.get_analysis_results()
        memory_analyses = results.get("memory_analyses", [])
        
        if memory_analyses:
            logger.info(f"✓ Memory analysis completed: {len(memory_analyses)} analyses")
            
            for analysis in memory_analyses[:3]:  # Show first 3
                logger.info(f"  Process {analysis.get('process_id', 'Unknown')}:")
                logger.info(f"    Dump size: {analysis.get('dump_size', 0)} bytes")
                logger.info(f"    Injection detected: {analysis.get('injection_detected', False)}")
                logger.info(f"    Keys extracted: {len(analysis.get('extracted_keys', []))}")
                logger.info(f"    Certificates: {len(analysis.get('extracted_certificates', []))}")
                
        else:
            logger.warning("⚠ No memory analyses completed (may be expected in test environment)")
        
        # Test memory dump creation
        dump_result = analyzer._create_memory_dump(current_process_id)
        if dump_result and dump_result.get("dump_path"):
            logger.info(f"✓ Memory dump created: {dump_result['dump_path']}")
            logger.info(f"  Dump size: {dump_result.get('size', 0)} bytes")
            
            # Clean up dump file
            try:
                os.unlink(dump_result["dump_path"])
            except:
                pass
        else:
            logger.warning("⚠ Memory dump creation failed (may be expected without C++ components)")
        
        # Test injection detection
        injection_detected = analyzer._detect_memory_injections(current_process_id)
        logger.info(f"  Injection detection result: {injection_detected}")
        
        await analyzer.stop_analysis()
        return True
        
    except Exception as e:
        logger.error(f"✗ Memory analysis test failed: {e}")
        return False

async def test_threat_correlation():
    """Test threat correlation and advanced detection"""
    logger.info("Testing Threat Correlation...")
    
    try:
        from phases.dynamic_analysis.behavioral_memory_analyzer import BehavioralMemoryAnalyzer
        
        config = load_test_config()
        analyzer = BehavioralMemoryAnalyzer(config)
        
        # Start analysis
        await analyzer.start_analysis()
        
        # Simulate multiple related threat patterns
        test_process_id = 12345
        
        # Create patterns that should correlate to ransomware
        analyzer._create_behavioral_pattern(
            "potential_file_encryption",
            "High file encryption activity detected",
            0.9,
            ["Bulk file writes", "Suspicious extensions"],
            {"process_id": test_process_id},
            files_encrypted=50
        )
        
        analyzer._create_behavioral_pattern(
            "shadow_copy_deletion",
            "Shadow copy deletion detected",
            0.95,
            ["vssadmin command", "Shadow copy manipulation"],
            {"process_id": test_process_id},
            shadow_copies_deleted=3
        )
        
        analyzer._create_behavioral_pattern(
            "security_tool_interference",
            "Security tools disabled",
            0.8,
            ["Antivirus termination", "Security process killing"],
            {"process_id": test_process_id},
            security_tools_disabled=["Windows Defender"]
        )
        
        # Wait for correlation analysis
        await asyncio.sleep(5)
        
        # Check for correlated threats
        results = await analyzer.get_analysis_results()
        behavioral_patterns = results.get("behavioral_patterns", [])
        
        # Look for high-level threat correlations
        ransomware_patterns = [p for p in behavioral_patterns if p.get("pattern_type") == "ransomware_activity"]
        apt_patterns = [p for p in behavioral_patterns if p.get("pattern_type") == "apt_activity"]
        
        if ransomware_patterns:
            logger.info(f"✓ Ransomware correlation detected: {len(ransomware_patterns)} instances")
            for pattern in ransomware_patterns:
                logger.info(f"  Confidence: {pattern.get('confidence_score', 0):.2f}")
                logger.info(f"  Description: {pattern.get('description', 'No description')}")
        
        if apt_patterns:
            logger.info(f"✓ APT correlation detected: {len(apt_patterns)} instances")
        
        # Check threat summary
        threat_summary = await analyzer.get_threat_summary()
        logger.info(f"✓ Threat correlation analysis completed")
        logger.info(f"  Overall threat level: {threat_summary.get('overall_threat_level', 'unknown')}")
        logger.info(f"  Total patterns: {threat_summary.get('total_patterns', 0)}")
        logger.info(f"  High threat patterns: {threat_summary.get('high_threat_patterns', 0)}")
        
        # Check recommendations
        recommendations = threat_summary.get("recommendations", [])
        if recommendations:
            logger.info(f"  Generated {len(recommendations)} recommendations:")
            for rec in recommendations:
                logger.info(f"    - {rec}")
        
        await analyzer.stop_analysis()
        return True
        
    except Exception as e:
        logger.error(f"✗ Threat correlation test failed: {e}")
        return False

async def test_performance():
    """Test performance under load"""
    logger.info("Testing Performance Under Load...")
    
    try:
        from phases.dynamic_analysis.behavioral_memory_analyzer import BehavioralMemoryAnalyzer
        
        config = load_test_config()
        analyzer = BehavioralMemoryAnalyzer(config)
        
        # Start analysis
        start_time = time.time()
        await analyzer.start_analysis()
        
        # Generate load by creating many patterns
        for i in range(50):
            analyzer._create_behavioral_pattern(
                f"test_pattern_{i % 5}",
                f"Test pattern {i}",
                0.5 + (i % 5) * 0.1,
                [f"Test indicator {i}"],
                {"process_id": 1000 + i, "test_data": f"data_{i}"}
            )
            
            if i % 10 == 0:
                await asyncio.sleep(0.1)  # Brief pause
        
        # Wait for processing
        await asyncio.sleep(5)
        
        # Check performance metrics
        metrics = analyzer.get_performance_metrics()
        
        logger.info(f"✓ Performance test completed")
        logger.info(f"  Active threads: {metrics.get('active_threads', 0)}")
        logger.info(f"  C++ bridge available: {metrics.get('cpp_bridge_available', False)}")
        
        data_counts = metrics.get('data_counts', {})
        logger.info(f"  Resource metrics: {data_counts.get('resource_metrics', 0)}")
        logger.info(f"  Behavioral patterns: {data_counts.get('behavioral_patterns', 0)}")
        logger.info(f"  Process analyses: {data_counts.get('process_analyses', 0)}")
        
        performance_stats = metrics.get('performance_stats', {})
        logger.info(f"  Total processes analyzed: {performance_stats.get('total_processes_analyzed', 0)}")
        logger.info(f"  Patterns detected: {performance_stats.get('behavioral_patterns_detected', 0)}")
        
        elapsed_time = time.time() - start_time
        patterns_per_second = performance_stats.get('behavioral_patterns_detected', 0) / elapsed_time
        logger.info(f"  Patterns per second: {patterns_per_second:.1f}")
        
        await analyzer.stop_analysis()
        return True
        
    except Exception as e:
        logger.error(f"✗ Performance test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("SBARDS Behavioral and Memory Analysis Test")
    logger.info("=" * 60)
    
    tests = [
        ("Behavioral Memory Analyzer", test_behavioral_analyzer),
        ("Resource Usage Monitoring", test_resource_monitoring),
        ("Behavioral Pattern Detection", test_behavioral_pattern_detection),
        ("Memory Analysis", test_memory_analysis),
        ("Threat Correlation", test_threat_correlation),
        ("Performance Under Load", test_performance),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Testing {test_name} ---")
        try:
            result = await test_func()
            results[test_name] = result
            if result:
                logger.info(f"✓ {test_name}: PASSED")
            else:
                logger.error(f"✗ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"✗ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name:.<40} {status}")
    
    logger.info("-" * 60)
    logger.info(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed!")
        return 0
    else:
        logger.warning(f"⚠ {total - passed} test(s) failed")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test suite failed: {e}")
        sys.exit(1)
