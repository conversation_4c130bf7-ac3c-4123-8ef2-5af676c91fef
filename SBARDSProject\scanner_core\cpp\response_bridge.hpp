#pragma once

#include "response_engine.hpp"
#include <string>
#include <vector>
#include <map>
#include <memory>

// Python C API includes
#ifdef _WIN32
    #include <Python.h>
#else
    #include <python3.12/Python.h>
#endif

/**
 * @brief Response Bridge for Python Integration
 * 
 * This class provides a C interface for Python integration with the
 * SBARDS Response Engine. It allows Python code to interact with
 * the high-performance C++ response components.
 */
class ResponseBridge {
public:
    /**
     * @brief Constructor
     */
    ResponseBridge();
    
    /**
     * @brief Destructor
     */
    ~ResponseBridge();
    
    /**
     * @brief Initialize the response bridge
     * @param config_json JSON configuration string
     * @return true if initialization successful, false otherwise
     */
    bool initialize(const std::string& config_json);
    
    /**
     * @brief Shutdown the response bridge
     */
    void shutdown();
    
    /**
     * @brief Check if the bridge is running
     * @return true if running, false otherwise
     */
    bool is_running() const;
    
    /**
     * @brief Process analysis results from Python
     * @param results_json JSON string containing analysis results
     * @return JSON string containing response results
     */
    std::string process_analysis_results(const std::string& results_json);
    
    /**
     * @brief Execute specific response action
     * @param action_name Name of the action to execute
     * @param file_path Target file path
     * @param metadata_json JSON string containing metadata
     * @return JSON string containing response results
     */
    std::string execute_response_action(const std::string& action_name,
                                       const std::string& file_path,
                                       const std::string& metadata_json);
    
    /**
     * @brief Get response statistics
     * @return JSON string containing statistics
     */
    std::string get_response_statistics();
    
    /**
     * @brief Get active response sessions
     * @return JSON string containing active sessions
     */
    std::string get_active_sessions();
    
    /**
     * @brief Cancel response session
     * @param session_id Session ID to cancel
     * @return true if cancelled successfully, false otherwise
     */
    bool cancel_response_session(const std::string& session_id);
    
    /**
     * @brief Update configuration
     * @param config_json JSON configuration string
     * @return true if updated successfully, false otherwise
     */
    bool update_configuration(const std::string& config_json);

private:
    std::unique_ptr<ResponseEngine> response_engine_;
    bool initialized_;
    
    // JSON conversion methods
    ResponseConfig parse_config_from_json(const std::string& config_json);
    AnalysisResults parse_analysis_results_from_json(const std::string& results_json);
    std::map<std::string, std::string> parse_metadata_from_json(const std::string& metadata_json);
    ResponseAction parse_response_action_from_string(const std::string& action_name);
    
    std::string convert_response_result_to_json(const ResponseResult& result);
    std::string convert_statistics_to_json(const std::map<std::string, uint64_t>& stats);
    std::string convert_sessions_to_json(const std::vector<std::string>& sessions);
    
    // Error handling
    std::string create_error_response(const std::string& error_message);
};

// C interface functions for Python integration
extern "C" {
    /**
     * @brief Create response bridge instance
     * @return Pointer to ResponseBridge instance
     */
    ResponseBridge* create_response_bridge();
    
    /**
     * @brief Destroy response bridge instance
     * @param bridge Pointer to ResponseBridge instance
     */
    void destroy_response_bridge(ResponseBridge* bridge);
    
    /**
     * @brief Initialize response bridge
     * @param bridge Pointer to ResponseBridge instance
     * @param config_json JSON configuration string
     * @return 1 if successful, 0 if failed
     */
    int initialize_response_bridge(ResponseBridge* bridge, const char* config_json);
    
    /**
     * @brief Shutdown response bridge
     * @param bridge Pointer to ResponseBridge instance
     */
    void shutdown_response_bridge(ResponseBridge* bridge);
    
    /**
     * @brief Check if response bridge is running
     * @param bridge Pointer to ResponseBridge instance
     * @return 1 if running, 0 if not running
     */
    int is_response_bridge_running(ResponseBridge* bridge);
    
    /**
     * @brief Process analysis results
     * @param bridge Pointer to ResponseBridge instance
     * @param results_json JSON string containing analysis results
     * @return JSON string containing response results (caller must free)
     */
    char* process_analysis_results_bridge(ResponseBridge* bridge, const char* results_json);
    
    /**
     * @brief Execute response action
     * @param bridge Pointer to ResponseBridge instance
     * @param action_name Name of the action to execute
     * @param file_path Target file path
     * @param metadata_json JSON string containing metadata
     * @return JSON string containing response results (caller must free)
     */
    char* execute_response_action_bridge(ResponseBridge* bridge,
                                        const char* action_name,
                                        const char* file_path,
                                        const char* metadata_json);
    
    /**
     * @brief Get response statistics
     * @param bridge Pointer to ResponseBridge instance
     * @return JSON string containing statistics (caller must free)
     */
    char* get_response_statistics_bridge(ResponseBridge* bridge);
    
    /**
     * @brief Get active response sessions
     * @param bridge Pointer to ResponseBridge instance
     * @return JSON string containing active sessions (caller must free)
     */
    char* get_active_sessions_bridge(ResponseBridge* bridge);
    
    /**
     * @brief Cancel response session
     * @param bridge Pointer to ResponseBridge instance
     * @param session_id Session ID to cancel
     * @return 1 if cancelled successfully, 0 if failed
     */
    int cancel_response_session_bridge(ResponseBridge* bridge, const char* session_id);
    
    /**
     * @brief Update configuration
     * @param bridge Pointer to ResponseBridge instance
     * @param config_json JSON configuration string
     * @return 1 if updated successfully, 0 if failed
     */
    int update_configuration_bridge(ResponseBridge* bridge, const char* config_json);
    
    /**
     * @brief Free memory allocated by bridge functions
     * @param ptr Pointer to memory to free
     */
    void free_bridge_memory(char* ptr);
}

/**
 * @brief Python module initialization function
 */
extern "C" {
    PyObject* PyInit_sbards_response_engine();
}

// Python method definitions
static PyObject* py_create_response_bridge(PyObject* self, PyObject* args);
static PyObject* py_destroy_response_bridge(PyObject* self, PyObject* args);
static PyObject* py_initialize_response_bridge(PyObject* self, PyObject* args);
static PyObject* py_shutdown_response_bridge(PyObject* self, PyObject* args);
static PyObject* py_is_response_bridge_running(PyObject* self, PyObject* args);
static PyObject* py_process_analysis_results(PyObject* self, PyObject* args);
static PyObject* py_execute_response_action(PyObject* self, PyObject* args);
static PyObject* py_get_response_statistics(PyObject* self, PyObject* args);
static PyObject* py_get_active_sessions(PyObject* self, PyObject* args);
static PyObject* py_cancel_response_session(PyObject* self, PyObject* args);
static PyObject* py_update_configuration(PyObject* self, PyObject* args);

// Python method table
static PyMethodDef ResponseBridgeMethods[] = {
    {"create_response_bridge", py_create_response_bridge, METH_NOARGS, "Create response bridge instance"},
    {"destroy_response_bridge", py_destroy_response_bridge, METH_VARARGS, "Destroy response bridge instance"},
    {"initialize_response_bridge", py_initialize_response_bridge, METH_VARARGS, "Initialize response bridge"},
    {"shutdown_response_bridge", py_shutdown_response_bridge, METH_VARARGS, "Shutdown response bridge"},
    {"is_response_bridge_running", py_is_response_bridge_running, METH_VARARGS, "Check if response bridge is running"},
    {"process_analysis_results", py_process_analysis_results, METH_VARARGS, "Process analysis results"},
    {"execute_response_action", py_execute_response_action, METH_VARARGS, "Execute response action"},
    {"get_response_statistics", py_get_response_statistics, METH_VARARGS, "Get response statistics"},
    {"get_active_sessions", py_get_active_sessions, METH_VARARGS, "Get active response sessions"},
    {"cancel_response_session", py_cancel_response_session, METH_VARARGS, "Cancel response session"},
    {"update_configuration", py_update_configuration, METH_VARARGS, "Update configuration"},
    {NULL, NULL, 0, NULL}
};

// Python module definition
static struct PyModuleDef ResponseBridgeModule = {
    PyModuleDef_HEAD_INIT,
    "sbards_response_engine",
    "SBARDS Response Engine C++ Bridge",
    -1,
    ResponseBridgeMethods
};

/**
 * @brief Utility functions for JSON processing
 */
namespace ResponseBridgeUtils {
    /**
     * @brief Parse JSON string to extract configuration
     * @param json_str JSON string
     * @return ResponseConfig object
     */
    ResponseConfig parse_json_config(const std::string& json_str);
    
    /**
     * @brief Parse JSON string to extract analysis results
     * @param json_str JSON string
     * @return AnalysisResults object
     */
    AnalysisResults parse_json_analysis_results(const std::string& json_str);
    
    /**
     * @brief Convert ResponseResult to JSON string
     * @param result ResponseResult object
     * @return JSON string
     */
    std::string response_result_to_json(const ResponseResult& result);
    
    /**
     * @brief Convert statistics map to JSON string
     * @param stats Statistics map
     * @return JSON string
     */
    std::string statistics_to_json(const std::map<std::string, uint64_t>& stats);
    
    /**
     * @brief Convert string vector to JSON array
     * @param strings String vector
     * @return JSON string
     */
    std::string string_vector_to_json(const std::vector<std::string>& strings);
    
    /**
     * @brief Create error response JSON
     * @param error_message Error message
     * @return JSON error response
     */
    std::string create_json_error(const std::string& error_message);
    
    /**
     * @brief Validate JSON string
     * @param json_str JSON string to validate
     * @return true if valid, false otherwise
     */
    bool validate_json(const std::string& json_str);
}
