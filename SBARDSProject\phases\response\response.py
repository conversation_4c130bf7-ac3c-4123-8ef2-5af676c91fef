"""
SBARDS Unified Response Layer (طبقة الاستجابة الموحدة)

This module implements the unified response layer that handles:
- Integrated threat response with data management
- File quarantine with advanced security (العزل في Quarantine)
- Honeypot isolation with forensic capabilities (العزل في Honeypot)
- Alert notifications with real-time monitoring (التنبيهات)
- Permission modifications with access control (تعديل الصلاحيات)
- Automated response actions with C++ core integration
- Advanced data storage and retrieval
- Military-grade encryption and security
- Cross-platform compatibility
- Performance monitoring and optimization
"""

import os
import logging
import shutil
import json
import smtplib
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import stat

# Import unified response system
try:
    from .unified_response_system import UnifiedResponseSystem
    UNIFIED_SYSTEM_AVAILABLE = True
except ImportError:
    try:
        from unified_response_system import UnifiedResponseSystem
        UNIFIED_SYSTEM_AVAILABLE = True
    except ImportError:
        UnifiedResponseSystem = None
        UNIFIED_SYSTEM_AVAILABLE = False


class ResponseLayer:
    """
    SBARDS Response Layer

    Handles automated and manual response actions based on analysis results.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Response Layer.

        Args:
            config (Dict[str, Any]): Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.ResponseLayer")

        # Response configuration
        self.response_config = config.get("response", {})

        # Directories
        self.quarantine_dir = Path(self.response_config.get("quarantine_directory", "quarantine"))
        self.honeypot_dir = Path(self.response_config.get("honeypot_directory", "honeypot"))

        # Create directories
        self.quarantine_dir.mkdir(parents=True, exist_ok=True)
        self.honeypot_dir.mkdir(parents=True, exist_ok=True)

        # Response settings
        self.auto_quarantine = self.response_config.get("auto_quarantine", False)
        self.auto_block_process = self.response_config.get("auto_block_process", False)
        self.auto_block_network = self.response_config.get("auto_block_network", False)

        # Notification settings
        self.notification_methods = self.response_config.get("notification_methods", ["log"])
        self.email_settings = self.response_config.get("email_settings", {})

        # Response history
        self.response_history = []

    def quarantine_file(self, file_path: str, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Quarantine a malicious file.

        Args:
            file_path (str): Path to the file to quarantine
            analysis_results (Dict[str, Any]): Analysis results

        Returns:
            Dict[str, Any]: Quarantine operation result
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                return {"success": False, "error": "File not found"}

            # Generate quarantine ID
            quarantine_id = self._generate_quarantine_id()

            # Create quarantine subdirectory
            quarantine_subdir = self.quarantine_dir / datetime.now().strftime("%Y/%m/%d")
            quarantine_subdir.mkdir(parents=True, exist_ok=True)

            # Move file to quarantine
            quarantine_path = quarantine_subdir / f"{quarantine_id}_{file_path.name}"
            shutil.move(str(file_path), str(quarantine_path))

            # Set restrictive permissions
            os.chmod(quarantine_path, 0o000)  # No permissions

            # Create quarantine record
            quarantine_record = {
                "quarantine_id": quarantine_id,
                "original_path": str(file_path),
                "quarantine_path": str(quarantine_path),
                "timestamp": datetime.now().isoformat(),
                "reason": "Malicious file detected",
                "analysis_results": analysis_results,
                "status": "quarantined"
            }

            # Save quarantine record
            self._save_quarantine_record(quarantine_record)

            # Log response action
            self._log_response_action("quarantine", quarantine_record)

            self.logger.warning(f"File quarantined: {file_path} -> {quarantine_path}")

            return {
                "success": True,
                "quarantine_id": quarantine_id,
                "quarantine_path": str(quarantine_path),
                "record": quarantine_record
            }

        except Exception as e:
            self.logger.error(f"Error quarantining file {file_path}: {e}")
            return {"success": False, "error": str(e)}

    def isolate_in_honeypot(self, file_path: str, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Isolate a suspicious file in honeypot environment.

        Args:
            file_path (str): Path to the file to isolate
            analysis_results (Dict[str, Any]): Analysis results

        Returns:
            Dict[str, Any]: Isolation operation result
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                return {"success": False, "error": "File not found"}

            # Generate isolation ID
            isolation_id = self._generate_isolation_id()

            # Create honeypot subdirectory
            honeypot_subdir = self.honeypot_dir / datetime.now().strftime("%Y/%m/%d")
            honeypot_subdir.mkdir(parents=True, exist_ok=True)

            # Copy file to honeypot (don't move, keep original for further analysis)
            honeypot_path = honeypot_subdir / f"{isolation_id}_{file_path.name}"
            shutil.copy2(str(file_path), str(honeypot_path))

            # Set monitoring permissions
            os.chmod(honeypot_path, 0o444)  # Read-only

            # Create isolation record
            isolation_record = {
                "isolation_id": isolation_id,
                "original_path": str(file_path),
                "honeypot_path": str(honeypot_path),
                "timestamp": datetime.now().isoformat(),
                "reason": "Suspicious behavior detected",
                "analysis_results": analysis_results,
                "status": "isolated"
            }

            # Save isolation record
            self._save_isolation_record(isolation_record)

            # Log response action
            self._log_response_action("honeypot_isolation", isolation_record)

            self.logger.warning(f"File isolated in honeypot: {file_path} -> {honeypot_path}")

            return {
                "success": True,
                "isolation_id": isolation_id,
                "honeypot_path": str(honeypot_path),
                "record": isolation_record
            }

        except Exception as e:
            self.logger.error(f"Error isolating file {file_path}: {e}")
            return {"success": False, "error": str(e)}

    def send_alerts(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send alerts based on analysis results.

        Args:
            analysis_results (Dict[str, Any]): Analysis results

        Returns:
            Dict[str, Any]: Alert sending result
        """
        alert_results = {
            "alerts_sent": [],
            "errors": []
        }

        try:
            # Determine alert level
            alert_level = self._determine_alert_level(analysis_results)

            # Create alert message
            alert_message = self._create_alert_message(analysis_results, alert_level)

            # Send alerts via configured methods
            for method in self.notification_methods:
                try:
                    if method == "log":
                        self._send_log_alert(alert_message, alert_level)
                        alert_results["alerts_sent"].append("log")

                    elif method == "email":
                        self._send_email_alert(alert_message, alert_level)
                        alert_results["alerts_sent"].append("email")

                    elif method == "slack":
                        self._send_slack_alert(alert_message, alert_level)
                        alert_results["alerts_sent"].append("slack")

                except Exception as e:
                    error_msg = f"Error sending {method} alert: {e}"
                    self.logger.error(error_msg)
                    alert_results["errors"].append(error_msg)

            return alert_results

        except Exception as e:
            self.logger.error(f"Error sending alerts: {e}")
            return {"alerts_sent": [], "errors": [str(e)]}

    def update_permissions(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update file permissions based on analysis results.

        Args:
            analysis_results (Dict[str, Any]): Analysis results

        Returns:
            Dict[str, Any]: Permission update result
        """
        try:
            decision = analysis_results.get("final_decision", {}).get("decision", "UNKNOWN")

            if decision == "QUARANTINED":
                # Deny all access (chmod 000)
                return self._deny_all_access(analysis_results)

            elif decision == "ISOLATED":
                # Read-only access
                return self._set_readonly_access(analysis_results)

            elif decision == "ALLOWED":
                # Normal access
                return self._set_normal_access(analysis_results)

            else:
                return {"success": False, "error": f"Unknown decision: {decision}"}

        except Exception as e:
            self.logger.error(f"Error updating permissions: {e}")
            return {"success": False, "error": str(e)}

    def _generate_quarantine_id(self) -> str:
        """Generate unique quarantine ID."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        return f"quarantine_{timestamp}"

    def _generate_isolation_id(self) -> str:
        """Generate unique isolation ID."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        return f"isolation_{timestamp}"

    def _save_quarantine_record(self, record: Dict[str, Any]):
        """Save quarantine record."""
        records_dir = self.quarantine_dir / "records"
        records_dir.mkdir(exist_ok=True)

        record_file = records_dir / f"{record['quarantine_id']}.json"

        with open(record_file, 'w') as f:
            json.dump(record, f, indent=2, default=str)

    def _save_isolation_record(self, record: Dict[str, Any]):
        """Save isolation record."""
        records_dir = self.honeypot_dir / "records"
        records_dir.mkdir(exist_ok=True)

        record_file = records_dir / f"{record['isolation_id']}.json"

        with open(record_file, 'w') as f:
            json.dump(record, f, indent=2, default=str)

    def _log_response_action(self, action_type: str, record: Dict[str, Any]):
        """Log response action to history."""
        action_record = {
            "timestamp": datetime.now().isoformat(),
            "action_type": action_type,
            "record": record
        }

        self.response_history.append(action_record)

        # Keep only last 1000 actions
        if len(self.response_history) > 1000:
            self.response_history = self.response_history[-1000:]

    def _determine_alert_level(self, analysis_results: Dict[str, Any]) -> str:
        """Determine alert level based on analysis results."""
        decision = analysis_results.get("final_decision", {}).get("decision", "UNKNOWN")

        if decision == "QUARANTINED":
            return "CRITICAL"
        elif decision == "ISOLATED":
            return "HIGH"
        elif decision == "ALLOWED":
            return "INFO"
        else:
            return "MEDIUM"

    def _create_alert_message(self, analysis_results: Dict[str, Any], alert_level: str) -> str:
        """Create alert message."""
        workflow_id = analysis_results.get("workflow_id", "unknown")
        decision = analysis_results.get("final_decision", {})

        message = f"""
SBARDS Security Alert - {alert_level}

Workflow ID: {workflow_id}
Timestamp: {datetime.now().isoformat()}
Decision: {decision.get('decision', 'UNKNOWN')}
Reason: {decision.get('reason', 'No reason provided')}

File Information:
"""

        # Add file information if available
        capture_info = analysis_results.get("phases", {}).get("capture", {})
        if capture_info:
            file_info = capture_info.get("file_info", {})
            message += f"- Original Filename: {file_info.get('original_filename', 'unknown')}\n"
            message += f"- File Size: {file_info.get('file_size', 'unknown')} bytes\n"
            message += f"- File Hash: {file_info.get('file_hash', 'unknown')}\n"

        return message

    def _send_log_alert(self, message: str, alert_level: str):
        """Send alert to log."""
        if alert_level == "CRITICAL":
            self.logger.critical(message)
        elif alert_level == "HIGH":
            self.logger.error(message)
        elif alert_level == "MEDIUM":
            self.logger.warning(message)
        else:
            self.logger.info(message)

    def _send_email_alert(self, message: str, alert_level: str):
        """Send alert via email."""
        if not self.email_settings.get("smtp_server"):
            raise ValueError("Email settings not configured")

        # Create email
        msg = MIMEMultipart()
        msg['From'] = self.email_settings.get("username", "sbards@localhost")
        msg['To'] = ", ".join(self.email_settings.get("recipients", []))
        msg['Subject'] = f"SBARDS Alert - {alert_level}"

        msg.attach(MIMEText(message, 'plain'))

        # Send email
        server = smtplib.SMTP(
            self.email_settings["smtp_server"],
            self.email_settings.get("smtp_port", 587)
        )
        server.starttls()
        server.login(
            self.email_settings["username"],
            self.email_settings["password"]
        )

        text = msg.as_string()
        server.sendmail(
            self.email_settings["username"],
            self.email_settings["recipients"],
            text
        )
        server.quit()

    def _send_slack_alert(self, message: str, alert_level: str):
        """Send alert to Slack (placeholder)."""
        # This would integrate with Slack API
        self.logger.info(f"Slack alert ({alert_level}): {message}")

    def _deny_all_access(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Deny all access to file (chmod 000)."""
        # This is typically handled during quarantine
        return {"success": True, "action": "access_denied"}

    def _set_readonly_access(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Set read-only access to file."""
        # This is typically handled during honeypot isolation
        return {"success": True, "action": "readonly_access"}

    def _set_normal_access(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Set normal access to file."""
        return {"success": True, "action": "normal_access"}


class ResponseSystem:
    """
    Unified Response System - Main interface for threat response operations.

    This class provides a unified interface that integrates:
    - C++ Core Engine (for high-performance operations)
    - Python Integration Layer (for flexibility and configuration)
    - Advanced data management
    - Security features
    - Performance monitoring
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize Response System."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.ResponseSystem")

        # Initialize unified response system if available
        if UNIFIED_SYSTEM_AVAILABLE:
            try:
                self.unified_system = UnifiedResponseSystem(config)
                self.use_unified = True
                self.logger.info("Unified Response System initialized successfully")
            except Exception as e:
                self.logger.warning(f"Failed to initialize unified system: {e}")
                self.unified_system = None
                self.use_unified = False
        else:
            self.unified_system = None
            self.use_unified = False
            self.logger.warning("Unified Response System not available, using legacy ResponseLayer")

        # Initialize legacy response layer as fallback
        self.response_layer = ResponseLayer(config)

        # Performance tracking
        self.operation_count = 0
        self.start_time = datetime.now()

    def process_analysis_results(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process analysis results and execute appropriate response.

        Args:
            analysis_results (Dict[str, Any]): Analysis results from threat detection

        Returns:
            Dict[str, Any]: Response execution results
        """
        try:
            self.operation_count += 1
            self.logger.info(f"Processing analysis results (operation #{self.operation_count})")

            # Convert analysis results to threat assessment format
            threat_assessment = self._convert_to_threat_assessment(analysis_results)

            # Use unified system if available
            if self.use_unified and self.unified_system:
                try:
                    # Run async operation in sync context
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        result = loop.run_until_complete(
                            self.unified_system.process_threat(threat_assessment)
                        )
                        return result
                    finally:
                        loop.close()

                except Exception as e:
                    self.logger.warning(f"Unified system failed: {e}, falling back to legacy")

            # Fallback to legacy response layer
            return self._process_with_legacy_system(analysis_results)

        except Exception as e:
            self.logger.error(f"Error processing analysis results: {e}")
            return {"success": False, "error": str(e)}

    def _convert_to_threat_assessment(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Convert analysis results to threat assessment format."""
        try:
            # Extract threat information
            threat_assessment_data = analysis_results.get("threat_assessment", {})
            file_info = analysis_results.get("phases", {}).get("capture", {}).get("file_info", {})

            return {
                "threat_id": analysis_results.get("workflow_id", f"threat_{int(datetime.now().timestamp())}"),
                "file_path": analysis_results.get("file_path", file_info.get("file_path", "")),
                "threat_score": threat_assessment_data.get("threat_score", 0.0),
                "threat_level": threat_assessment_data.get("overall_threat_level", "unknown"),
                "detected_threats": analysis_results.get("detected_threats", []),
                "metadata": {
                    "workflow_id": analysis_results.get("workflow_id", ""),
                    "detection_time": datetime.now(timezone.utc).isoformat(),
                    "file_size": file_info.get("file_size", 0),
                    "file_hash": file_info.get("file_hash", ""),
                    "original_filename": file_info.get("original_filename", "")
                },
                "detection_time": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error converting to threat assessment: {e}")
            return {
                "threat_id": f"error_threat_{int(datetime.now().timestamp())}",
                "file_path": "",
                "threat_score": 0.0,
                "threat_level": "unknown",
                "detected_threats": [],
                "metadata": {},
                "detection_time": datetime.now(timezone.utc).isoformat()
            }

    def _process_with_legacy_system(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Process with legacy response layer."""
        try:
            # Determine appropriate response based on results
            decision = analysis_results.get("final_decision", {}).get("decision", "UNKNOWN")

            if decision == "QUARANTINED":
                file_path = analysis_results.get("phases", {}).get("capture", {}).get("file_path", "")
                return self.response_layer.quarantine_file(file_path, analysis_results)

            elif decision == "ISOLATED":
                file_path = analysis_results.get("phases", {}).get("capture", {}).get("file_path", "")
                return self.response_layer.isolate_in_honeypot(file_path, analysis_results)

            else:
                # Send alerts and update permissions
                alert_result = self.response_layer.send_alerts(analysis_results)
                perm_result = self.response_layer.update_permissions(analysis_results)

                return {
                    "success": True,
                    "legacy_system": True,
                    "alerts": alert_result,
                    "permissions": perm_result,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }

        except Exception as e:
            self.logger.error(f"Error in legacy system processing: {e}")
            return {"success": False, "error": str(e)}

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        try:
            base_metrics = {
                "operation_count": self.operation_count,
                "uptime_seconds": (datetime.now() - self.start_time).total_seconds(),
                "unified_system_available": self.use_unified,
                "legacy_fallback_available": True
            }

            # Get unified system metrics if available
            if self.use_unified and self.unified_system:
                try:
                    unified_metrics = self.unified_system.get_performance_metrics()
                    base_metrics.update(unified_metrics)
                except Exception as e:
                    self.logger.warning(f"Failed to get unified metrics: {e}")

            return base_metrics

        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {e}")
            return {"operation_count": self.operation_count, "error": str(e)}

    def get_response_history(self, threat_id: str = "") -> List[Dict[str, Any]]:
        """Get response history."""
        try:
            if self.use_unified and self.unified_system:
                return self.unified_system.get_response_history(threat_id)
            else:
                # Return basic history from legacy system
                return getattr(self.response_layer, 'response_history', [])

        except Exception as e:
            self.logger.error(f"Error getting response history: {e}")
            return []


def Response(analysis_results: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main response function for backward compatibility.

    Args:
        analysis_results (Dict[str, Any]): Analysis results
        config (Dict[str, Any]): Configuration dictionary

    Returns:
        Dict[str, Any]: Response results
    """
    # Use new ResponseSystem for enhanced functionality
    response_system = ResponseSystem(config)
    return response_system.process_analysis_results(analysis_results)