"""
SBARDS Response Layer - Python Wrapper for C++ Core Engine
طبقة الاستجابة - واجهة Python لمحرك C++ الأساسي

This module provides a MINIMAL Python wrapper for the C++ Response Engine:
- C++ Core Engine handles ALL response operations
- Python provides ONLY integration and configuration interfaces
- Military-grade security implemented in C++
- Cross-platform compatibility through C++ core
- Maximum performance with C++ implementation

ALL CORE LOGIC IS IN C++
Python is used ONLY for:
1. Integration with other SBARDS layers
2. Configuration management
3. Basic error handling
4. Backward compatibility
"""

import logging
from typing import Dict, Any, List
from datetime import datetime, timezone

# Import C++ engine binding
try:
    from .response_binding import ResponseSystem as CPPResponseSystem, Response as CPPResponse, CPP_ENGINE_AVAILABLE
    CPP_BINDING_AVAILABLE = True
except ImportError:
    try:
        from response_binding import ResponseSystem as CPPResponseSystem, Response as CPPResponse, CPP_ENGINE_AVAILABLE
        CPP_BINDING_AVAILABLE = True
    except ImportError:
        CPPResponseSystem = None
        CPPResponse = None
        CPP_ENGINE_AVAILABLE = False
        CPP_BINDING_AVAILABLE = False


class ResponseSystem:
    """
    SBARDS Response System - Python Wrapper for C++ Core Engine
    
    This class provides MINIMAL Python interface to C++ Response Engine.
    ALL CORE LOGIC IS IMPLEMENTED IN C++.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Response System."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.ResponseSystem")
        
        # Check if C++ binding is available
        if CPP_BINDING_AVAILABLE:
            try:
                self.cpp_system = CPPResponseSystem(config)
                self.use_cpp = True
                self.logger.info("C++ Response Engine initialized successfully")
            except Exception as e:
                self.logger.warning(f"C++ engine initialization failed: {e}")
                self.cpp_system = None
                self.use_cpp = False
        else:
            self.cpp_system = None
            self.use_cpp = False
            self.logger.warning("C++ binding not available, using minimal fallback")
        
        # Basic metrics
        self.operation_count = 0
        self.start_time = datetime.now()
    
    def process_analysis_results(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process analysis results using C++ engine.
        
        Args:
            analysis_results: Analysis results from threat detection
            
        Returns:
            Response execution results
        """
        try:
            self.operation_count += 1
            self.logger.info(f"Processing analysis results (operation #{self.operation_count})")
            
            # Use C++ engine if available
            if self.use_cpp and self.cpp_system:
                return self.cpp_system.process_analysis_results(analysis_results)
            
            # Minimal fallback
            return self._minimal_fallback(analysis_results)
            
        except Exception as e:
            self.logger.error(f"Error processing analysis results: {e}")
            return {"success": False, "error": str(e), "cpp_engine": False}
    
    def _minimal_fallback(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Minimal fallback when C++ engine is not available."""
        self.logger.warning("Using minimal fallback (C++ engine not available)")
        
        decision = analysis_results.get("final_decision", {}).get("decision", "UNKNOWN")
        
        return {
            "success": True,
            "cpp_engine": False,
            "fallback": True,
            "action_taken": decision.lower(),
            "operation_id": f"fallback_{self.operation_count}",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "message": "Processed with minimal fallback system"
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        try:
            base_metrics = {
                "operation_count": self.operation_count,
                "uptime_seconds": (datetime.now() - self.start_time).total_seconds(),
                "cpp_engine_available": self.use_cpp,
                "cpp_binding_available": CPP_BINDING_AVAILABLE
            }
            
            # Get C++ engine metrics if available
            if self.use_cpp and self.cpp_system:
                try:
                    cpp_metrics = self.cpp_system.get_performance_metrics()
                    base_metrics.update(cpp_metrics)
                except Exception as e:
                    self.logger.warning(f"Failed to get C++ metrics: {e}")
            
            return base_metrics
            
        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {e}")
            return {"operation_count": self.operation_count, "error": str(e)}
    
    def get_operation_history(self, threat_id: str = "") -> List[Dict[str, Any]]:
        """Get operation history."""
        try:
            if self.use_cpp and self.cpp_system:
                return self.cpp_system.get_operation_history(threat_id)
            return []
        except Exception as e:
            self.logger.error(f"Error getting operation history: {e}")
            return []
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get engine status."""
        try:
            status = {
                "cpp_binding_available": CPP_BINDING_AVAILABLE,
                "cpp_engine_active": self.use_cpp,
                "operation_count": self.operation_count,
                "uptime_seconds": (datetime.now() - self.start_time).total_seconds()
            }
            
            if self.use_cpp and self.cpp_system:
                try:
                    cpp_status = self.cpp_system.get_engine_status()
                    status.update(cpp_status)
                except Exception as e:
                    status["cpp_status_error"] = str(e)
            
            return status
        except Exception as e:
            return {"error": str(e)}


# Legacy class for backward compatibility (DEPRECATED)
class ResponseLayer:
    """
    DEPRECATED: Legacy Response Layer for backward compatibility only.
    Use ResponseSystem instead.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize legacy response layer."""
        self.logger = logging.getLogger("SBARDS.ResponseLayer.Legacy")
        self.logger.warning("ResponseLayer is DEPRECATED. Use ResponseSystem instead.")
        
        # Delegate to ResponseSystem
        self.response_system = ResponseSystem(config)
    
    def quarantine_file(self, file_path: str, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Legacy quarantine method."""
        return self.response_system.process_analysis_results({
            **analysis_results,
            "file_path": file_path,
            "final_decision": {"decision": "QUARANTINED", "reason": "Legacy quarantine call"}
        })
    
    def isolate_in_honeypot(self, file_path: str, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Legacy honeypot method."""
        return self.response_system.process_analysis_results({
            **analysis_results,
            "file_path": file_path,
            "final_decision": {"decision": "ISOLATED", "reason": "Legacy honeypot call"}
        })
    
    def send_alerts(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Legacy alert method."""
        return self.response_system.process_analysis_results({
            **analysis_results,
            "final_decision": {"decision": "ALERT", "reason": "Legacy alert call"}
        })
    
    def update_permissions(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Legacy permissions method."""
        return {"success": True, "action": "permissions_updated"}


# Backward compatibility function
def Response(analysis_results: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Backward compatibility function.
    
    Args:
        analysis_results: Analysis results
        config: Configuration dictionary
        
    Returns:
        Response results
    """
    response_system = ResponseSystem(config)
    return response_system.process_analysis_results(analysis_results)


# Export main classes and functions
__all__ = [
    'ResponseSystem',
    'ResponseLayer', 
    'Response',
    'CPP_BINDING_AVAILABLE',
    'CPP_ENGINE_AVAILABLE'
]
