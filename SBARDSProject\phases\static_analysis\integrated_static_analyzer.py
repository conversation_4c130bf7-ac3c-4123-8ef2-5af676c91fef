#!/usr/bin/env python3
"""
SBARDS Integrated Static Analysis Layer (طبقة التحليل الثابت المتكاملة)
Advanced static analysis with YARA rules, digital signatures, and permission verification

This module implements:
- YARA rule scanning (local and external)
- Hash verification against threat databases
- Digital signature validation
- File permission verification
- Comprehensive threat assessment
- Integration with capture layer
"""

import os
import sys
import json
import logging
import sqlite3
import hashlib
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import tempfile
import stat

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import YARA if available
try:
    import yara
    YARA_AVAILABLE = True
except ImportError:
    YARA_AVAILABLE = False
    logging.warning("YARA not available, using alternative scanning methods")

# Import cryptography for signature verification
try:
    from cryptography import x509
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False
    logging.warning("Cryptography library not available")

class IntegratedStaticAnalyzer:
    """
    Comprehensive Static Analysis System
    نظام التحليل الثابت الشامل
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the integrated static analyzer."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.IntegratedStaticAnalyzer")
        
        # Static analysis configuration
        self.static_config = config.get("static_analysis", {})
        
        # Initialize components
        self._initialize_yara_engine()
        self._initialize_threat_databases()
        self._initialize_signature_verification()
        self._initialize_permission_database()
        
        # Database connection
        self.db_path = Path(config.get("file_capture", {}).get("base_directory", "capture_data")) / "capture_database.db"
        
        self.logger.info("Integrated Static Analyzer initialized successfully")
    
    def _initialize_yara_engine(self):
        """Initialize YARA scanning engine with local and external rules."""
        self.yara_rules = {}
        
        if not YARA_AVAILABLE:
            self.logger.warning("YARA not available, static analysis will be limited")
            return
        
        try:
            # Load local YARA rules
            rules_dir = Path(self.static_config.get("yara_rules_directory", "rules"))
            if rules_dir.exists():
                self._load_local_yara_rules(rules_dir)
            
            # Load external YARA rules
            external_rules = self.static_config.get("external_yara_rules", [])
            for rule_source in external_rules:
                self._load_external_yara_rules(rule_source)
            
            self.logger.info(f"YARA engine initialized with {len(self.yara_rules)} rule sets")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize YARA engine: {e}")
    
    def _load_local_yara_rules(self, rules_dir: Path):
        """Load local YARA rules from directory."""
        try:
            # Ransomware detection rules
            ransomware_rules_path = rules_dir / "ransomware_detection.yar"
            if ransomware_rules_path.exists():
                self.yara_rules["ransomware"] = yara.compile(filepath=str(ransomware_rules_path))
            
            # Malware detection rules
            malware_rules_path = rules_dir / "malware_detection.yar"
            if malware_rules_path.exists():
                self.yara_rules["malware"] = yara.compile(filepath=str(malware_rules_path))
            
            # Script injection rules
            script_rules_path = rules_dir / "script_injection.yar"
            if script_rules_path.exists():
                self.yara_rules["script_injection"] = yara.compile(filepath=str(script_rules_path))
            
            # Privilege escalation rules
            privilege_rules_path = rules_dir / "privilege_escalation.yar"
            if privilege_rules_path.exists():
                self.yara_rules["privilege_escalation"] = yara.compile(filepath=str(privilege_rules_path))
            
            self.logger.info("Local YARA rules loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Error loading local YARA rules: {e}")
    
    def _load_external_yara_rules(self, rule_source: Dict[str, Any]):
        """Load external YARA rules from various sources."""
        try:
            source_type = rule_source.get("type", "")
            source_url = rule_source.get("url", "")
            
            if source_type == "github":
                # Download and compile GitHub YARA rules
                self._download_github_yara_rules(source_url, rule_source.get("name", "external"))
            elif source_type == "url":
                # Download from direct URL
                self._download_url_yara_rules(source_url, rule_source.get("name", "external"))
            
        except Exception as e:
            self.logger.error(f"Error loading external YARA rules from {rule_source}: {e}")
    
    def _initialize_threat_databases(self):
        """Initialize threat hash databases (local and external)."""
        self.threat_databases = {
            "local_malware_hashes": set(),
            "local_ransomware_hashes": set(),
            "external_threat_feeds": []
        }
        
        try:
            # Load local threat hashes
            local_threats_file = Path(self.static_config.get("local_threats_file", "threat_hashes.json"))
            if local_threats_file.exists():
                with open(local_threats_file, 'r') as f:
                    threat_data = json.load(f)
                    self.threat_databases["local_malware_hashes"].update(threat_data.get("malware_hashes", []))
                    self.threat_databases["local_ransomware_hashes"].update(threat_data.get("ransomware_hashes", []))
            
            # Configure external threat feeds
            external_feeds = self.static_config.get("external_threat_feeds", [])
            self.threat_databases["external_threat_feeds"] = external_feeds
            
            self.logger.info("Threat databases initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing threat databases: {e}")
    
    def _initialize_signature_verification(self):
        """Initialize digital signature verification system."""
        self.signature_verification_enabled = CRYPTO_AVAILABLE and self.static_config.get("verify_signatures", True)
        
        if self.signature_verification_enabled:
            # Load trusted certificate authorities
            self.trusted_cas = []
            ca_dir = Path(self.static_config.get("trusted_ca_directory", "certificates"))
            if ca_dir.exists():
                for ca_file in ca_dir.glob("*.pem"):
                    try:
                        with open(ca_file, 'rb') as f:
                            ca_cert = x509.load_pem_x509_certificate(f.read())
                            self.trusted_cas.append(ca_cert)
                    except Exception as e:
                        self.logger.warning(f"Failed to load CA certificate {ca_file}: {e}")
            
            self.logger.info(f"Signature verification initialized with {len(self.trusted_cas)} trusted CAs")
        else:
            self.logger.warning("Digital signature verification disabled")
    
    def _initialize_permission_database(self):
        """Initialize default file permissions database."""
        self.default_permissions = {
            # Executable files
            ".exe": {"windows": 0o755, "unix": 0o755},
            ".dll": {"windows": 0o644, "unix": 0o644},
            ".so": {"windows": 0o755, "unix": 0o755},
            
            # Document files
            ".pdf": {"windows": 0o644, "unix": 0o644},
            ".doc": {"windows": 0o644, "unix": 0o644},
            ".docx": {"windows": 0o644, "unix": 0o644},
            ".xls": {"windows": 0o644, "unix": 0o644},
            ".xlsx": {"windows": 0o644, "unix": 0o644},
            
            # Script files
            ".py": {"windows": 0o644, "unix": 0o755},
            ".sh": {"windows": 0o644, "unix": 0o755},
            ".bat": {"windows": 0o755, "unix": 0o644},
            ".ps1": {"windows": 0o644, "unix": 0o644},
            
            # Archive files
            ".zip": {"windows": 0o644, "unix": 0o644},
            ".rar": {"windows": 0o644, "unix": 0o644},
            ".7z": {"windows": 0o644, "unix": 0o644},
            
            # Media files
            ".jpg": {"windows": 0o644, "unix": 0o644},
            ".png": {"windows": 0o644, "unix": 0o644},
            ".mp4": {"windows": 0o644, "unix": 0o644},
            ".mp3": {"windows": 0o644, "unix": 0o644},
            
            # Default for unknown extensions
            "default": {"windows": 0o644, "unix": 0o644}
        }
        
        self.logger.info("Default permissions database initialized")
    
    def analyze_file(self, file_hash: str) -> Dict[str, Any]:
        """
        Perform comprehensive static analysis on a captured file.
        
        Args:
            file_hash: SHA256 hash of the file to analyze
            
        Returns:
            Dict containing complete static analysis results
        """
        try:
            self.logger.info(f"Starting static analysis for file hash: {file_hash}")
            
            # Get file information from capture database
            file_info = self._get_file_info_from_database(file_hash)
            if not file_info:
                return {"success": False, "error": "File not found in capture database"}
            
            file_path = file_info["temp_path"]
            if not os.path.exists(file_path):
                return {"success": False, "error": "File not found in temporary storage"}
            
            # Initialize analysis results
            analysis_results = {
                "analysis_id": f"static_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file_hash[:8]}",
                "file_hash": file_hash,
                "file_path": file_path,
                "file_info": file_info,
                "timestamp": datetime.now().isoformat(),
                "analysis_stages": {}
            }
            
            # Stage 1: YARA Rule Scanning
            yara_results = self._perform_yara_scanning(file_path)
            analysis_results["analysis_stages"]["yara_scanning"] = yara_results
            
            # Stage 2: Hash Verification Against Threat Databases
            hash_verification_results = self._verify_against_threat_databases(file_hash)
            analysis_results["analysis_stages"]["hash_verification"] = hash_verification_results
            
            # Stage 3: Digital Signature Verification
            signature_results = self._verify_digital_signature(file_path, file_info)
            analysis_results["analysis_stages"]["signature_verification"] = signature_results
            
            # Stage 4: File Permission Verification
            permission_results = self._verify_file_permissions(file_path, file_info)
            analysis_results["analysis_stages"]["permission_verification"] = permission_results
            
            # Stage 5: Comprehensive Risk Assessment
            risk_assessment = self._assess_static_risk(analysis_results)
            analysis_results["risk_assessment"] = risk_assessment
            
            # Stage 6: Generate Final Report
            final_report = self._generate_static_analysis_report(analysis_results)
            analysis_results["final_report"] = final_report
            
            # Stage 7: Update Database with Results
            self._update_database_with_results(file_hash, analysis_results)
            
            self.logger.info(f"Static analysis completed for {file_hash}: {final_report['classification']}")
            return {"success": True, "results": analysis_results}
            
        except Exception as e:
            self.logger.error(f"Error in static analysis for {file_hash}: {e}")
            return {"success": False, "error": str(e)}
    
    def _get_file_info_from_database(self, file_hash: str) -> Optional[Dict[str, Any]]:
        """Get file information from capture database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT original_path, temp_path, file_name, file_size, file_extension, 
                       mime_type, file_permissions, metadata_json, capture_timestamp
                FROM captured_files 
                WHERE file_hash_sha256 = ?
            """, (file_hash,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return {
                    "original_path": row[0],
                    "temp_path": row[1],
                    "file_name": row[2],
                    "file_size": row[3],
                    "file_extension": row[4],
                    "mime_type": row[5],
                    "file_permissions": row[6],
                    "metadata": json.loads(row[7]) if row[7] else {},
                    "capture_timestamp": row[8]
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting file info from database: {e}")
            return None
    
    def _perform_yara_scanning(self, file_path: str) -> Dict[str, Any]:
        """Perform comprehensive YARA rule scanning."""
        results = {
            "scan_timestamp": datetime.now().isoformat(),
            "rules_applied": [],
            "matches": [],
            "threat_indicators": [],
            "risk_score": 0
        }
        
        if not YARA_AVAILABLE or not self.yara_rules:
            results["error"] = "YARA scanning not available"
            return results
        
        try:
            for rule_name, rule_set in self.yara_rules.items():
                results["rules_applied"].append(rule_name)
                
                try:
                    matches = rule_set.match(file_path)
                    for match in matches:
                        match_info = {
                            "rule_name": match.rule,
                            "rule_set": rule_name,
                            "tags": list(match.tags),
                            "strings": [{"identifier": s.identifier, "instances": len(s.instances)} for s in match.strings],
                            "meta": dict(match.meta) if hasattr(match, 'meta') else {}
                        }
                        results["matches"].append(match_info)
                        
                        # Extract threat indicators
                        if rule_name == "ransomware":
                            results["threat_indicators"].append("ransomware_signature_detected")
                            results["risk_score"] += 50
                        elif rule_name == "malware":
                            results["threat_indicators"].append("malware_signature_detected")
                            results["risk_score"] += 40
                        elif rule_name == "script_injection":
                            results["threat_indicators"].append("script_injection_detected")
                            results["risk_score"] += 30
                        elif rule_name == "privilege_escalation":
                            results["threat_indicators"].append("privilege_escalation_detected")
                            results["risk_score"] += 35
                
                except Exception as e:
                    self.logger.warning(f"Error scanning with {rule_name} rules: {e}")
            
            results["total_matches"] = len(results["matches"])
            self.logger.info(f"YARA scanning completed: {results['total_matches']} matches found")
            
        except Exception as e:
            self.logger.error(f"Error in YARA scanning: {e}")
            results["error"] = str(e)
        
        return results
