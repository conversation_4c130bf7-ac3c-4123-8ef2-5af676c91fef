#pragma once

#include "response_engine.hpp"
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>
#include <filesystem>

#ifdef _WIN32
    #include <windows.h>
    #include <vss.h>
    #include <vswriter.h>
    #include <vsbackup.h>
#else
    #include <sys/stat.h>
    #include <sys/types.h>
    #include <fcntl.h>
    #include <unistd.h>
#endif

/**
 * @brief Recovery operation types
 */
enum class RecoveryType {
    FILE_RESTORATION = 0,
    SYSTEM_ROLLBACK = 1,
    CONFIGURATION_RESTORE = 2,
    REGISTRY_RESTORE = 3,
    PERMISSION_RESTORE = 4,
    NETWORK_RESTORE = 5,
    SERVICE_RESTORE = 6,
    FULL_SYSTEM_RESTORE = 7
};

/**
 * @brief Recovery status
 */
enum class RecoveryStatus {
    PENDING = 0,
    IN_PROGRESS = 1,
    COMPLETED = 2,
    FAILED = 3,
    CANCELLED = 4,
    PARTIAL = 5
};

/**
 * @brief Backup types
 */
enum class BackupType {
    INCREMENTAL = 0,
    DIFFERENTIAL = 1,
    FULL = 2,
    FORENSIC = 3,
    SNAPSHOT = 4
};

/**
 * @brief Recovery operation information
 */
struct RecoveryOperation {
    std::string operation_id;
    RecoveryType recovery_type;
    RecoveryStatus status;
    std::string target_path;
    std::string backup_source;
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    double progress_percentage;
    std::string error_message;
    std::vector<std::string> affected_files;
    std::vector<std::string> restored_files;
    std::map<std::string, std::string> metadata;
    std::string initiated_by;
    std::string reason;
};

/**
 * @brief Backup entry information
 */
struct BackupEntry {
    std::string backup_id;
    BackupType backup_type;
    std::string source_path;
    std::string backup_path;
    uint64_t backup_size;
    std::chrono::system_clock::time_point created_time;
    std::chrono::system_clock::time_point expiry_time;
    std::string checksum;
    bool encrypted;
    bool compressed;
    std::vector<std::string> included_files;
    std::vector<std::string> excluded_files;
    std::map<std::string, std::string> metadata;
    bool integrity_verified;
};

/**
 * @brief Forensic collection information
 */
struct ForensicCollection {
    std::string collection_id;
    std::string incident_id;
    std::string target_system;
    std::vector<std::string> collected_artifacts;
    std::chrono::system_clock::time_point collection_time;
    std::string collection_method;
    std::string chain_of_custody;
    std::map<std::string, std::string> evidence_hashes;
    bool integrity_preserved;
    std::string storage_location;
    std::map<std::string, std::string> metadata;
};

/**
 * @brief System snapshot information
 */
struct SystemSnapshot {
    std::string snapshot_id;
    std::string snapshot_name;
    std::chrono::system_clock::time_point created_time;
    std::string system_state;
    std::vector<std::string> included_volumes;
    std::map<std::string, std::string> system_configuration;
    std::vector<std::string> running_processes;
    std::vector<std::string> network_connections;
    std::map<std::string, std::string> registry_keys;
    uint64_t snapshot_size;
    std::string storage_path;
    bool bootable;
};

/**
 * @brief Advanced Recovery System
 * 
 * This class provides comprehensive recovery and restoration capabilities including:
 * - Automated backup creation and management
 * - File and system restoration from backups
 * - Forensic evidence collection and preservation
 * - System snapshot creation and rollback
 * - Configuration and registry restoration
 * - Network and service recovery
 * - Incident response automation
 * - Chain of custody maintenance
 */
class RecoverySystem {
public:
    /**
     * @brief Constructor
     * @param config Response configuration
     */
    explicit RecoverySystem(const ResponseConfig& config);
    
    /**
     * @brief Destructor
     */
    ~RecoverySystem();
    
    /**
     * @brief Initialize the recovery system
     * @return true if initialization successful, false otherwise
     */
    bool initialize();
    
    /**
     * @brief Shutdown the recovery system
     */
    void shutdown();
    
    /**
     * @brief Check if the system is running
     * @return true if running, false otherwise
     */
    bool is_running() const;
    
    // Backup operations
    ResponseResult create_forensic_backup(const AnalysisResults& results);
    ResponseResult create_system_backup(BackupType backup_type,
                                      const std::vector<std::string>& target_paths,
                                      const std::map<std::string, std::string>& metadata);
    
    ResponseResult create_file_backup(const std::string& file_path,
                                    const std::map<std::string, std::string>& metadata);
    
    // Recovery operations
    ResponseResult initiate_emergency_recovery(const AnalysisResults& results);
    ResponseResult initiate_incident_response(const AnalysisResults& results);
    
    ResponseResult restore_from_backup(const std::string& backup_id,
                                     const std::string& destination_path,
                                     bool verify_integrity = true);
    
    ResponseResult restore_file(const std::string& file_path,
                              const std::chrono::system_clock::time_point& restore_point);
    
    ResponseResult restore_system_configuration(const std::string& snapshot_id);
    
    // Forensic operations
    ResponseResult comprehensive_forensic_collection(const AnalysisResults& results);
    ResponseResult collect_system_artifacts(const std::string& incident_id,
                                           const std::vector<std::string>& artifact_types);
    
    ResponseResult preserve_evidence(const std::string& evidence_path,
                                   const std::string& incident_id,
                                   const std::map<std::string, std::string>& metadata);
    
    // System snapshot operations
    std::string create_system_snapshot(const std::string& snapshot_name,
                                      const std::vector<std::string>& included_volumes);
    
    bool restore_from_snapshot(const std::string& snapshot_id);
    bool delete_snapshot(const std::string& snapshot_id);
    std::vector<SystemSnapshot> list_snapshots() const;
    
    // Backup management
    std::vector<BackupEntry> list_backups(BackupType type = BackupType::FULL) const;
    BackupEntry get_backup_info(const std::string& backup_id) const;
    bool delete_backup(const std::string& backup_id);
    bool verify_backup_integrity(const std::string& backup_id);
    
    // Recovery operation management
    std::vector<RecoveryOperation> get_active_operations() const;
    RecoveryOperation get_operation_status(const std::string& operation_id) const;
    bool cancel_recovery_operation(const std::string& operation_id);
    
    // Forensic collection management
    std::vector<ForensicCollection> list_forensic_collections() const;
    ForensicCollection get_forensic_collection(const std::string& collection_id) const;
    bool verify_evidence_integrity(const std::string& collection_id);
    
    // Statistics and monitoring
    std::map<std::string, uint64_t> get_recovery_statistics() const;
    uint64_t get_total_backup_size() const;
    std::vector<std::string> get_recent_recovery_activity(int count = 100) const;
    
    // Maintenance operations
    bool cleanup_expired_backups();
    bool optimize_backup_storage();
    bool verify_all_backups();

private:
    // Configuration and state
    ResponseConfig config_;
    std::atomic<bool> running_;
    
    // Recovery operations management
    mutable std::mutex operations_mutex_;
    std::map<std::string, RecoveryOperation> active_operations_;
    
    // Backup management
    mutable std::mutex backups_mutex_;
    std::map<std::string, BackupEntry> backup_entries_;
    
    // Forensic collections
    mutable std::mutex forensics_mutex_;
    std::map<std::string, ForensicCollection> forensic_collections_;
    
    // System snapshots
    mutable std::mutex snapshots_mutex_;
    std::map<std::string, SystemSnapshot> system_snapshots_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    std::map<std::string, uint64_t> recovery_statistics_;
    
    // Worker threads
    std::vector<std::thread> worker_threads_;
    
    // Private methods
    std::string generate_operation_id() const;
    std::string generate_backup_id() const;
    std::string generate_collection_id() const;
    std::string generate_snapshot_id() const;
    void update_statistics(const std::string& metric, uint64_t value = 1);
    
    // Backup operations
    bool create_backup_archive(const std::vector<std::string>& source_paths,
                             const std::string& backup_path,
                             BackupType backup_type,
                             bool encrypt = true,
                             bool compress = true);
    
    bool extract_backup_archive(const std::string& backup_path,
                              const std::string& destination_path,
                              bool verify_integrity = true);
    
    // Forensic operations
    bool collect_memory_dump(const std::string& collection_id);
    bool collect_disk_image(const std::string& collection_id, const std::string& drive_path);
    bool collect_network_artifacts(const std::string& collection_id);
    bool collect_process_artifacts(const std::string& collection_id);
    bool collect_registry_artifacts(const std::string& collection_id);
    
    // System state operations
    bool capture_system_state(SystemSnapshot& snapshot);
    bool restore_system_state(const SystemSnapshot& snapshot);
    
    // File operations
    bool copy_file_with_metadata(const std::string& source_path,
                                const std::string& destination_path);
    
    bool calculate_file_checksum(const std::string& file_path, std::string& checksum);
    bool verify_file_integrity(const std::string& file_path, const std::string& expected_checksum);
    
    // Platform-specific implementations
#ifdef _WIN32
    bool initialize_windows_recovery();
    void cleanup_windows_recovery();
    
    bool create_windows_system_restore_point(const std::string& description);
    bool restore_windows_system_restore_point(const std::string& restore_point_id);
    
    bool create_windows_vss_snapshot(const std::vector<std::string>& volumes, std::string& snapshot_id);
    bool delete_windows_vss_snapshot(const std::string& snapshot_id);
    
    bool collect_windows_memory_dump(const std::string& output_path);
    bool collect_windows_registry_hives(const std::string& output_directory);
    
#else
    bool initialize_linux_recovery();
    void cleanup_linux_recovery();
    
    bool create_linux_lvm_snapshot(const std::string& volume_path, std::string& snapshot_path);
    bool delete_linux_lvm_snapshot(const std::string& snapshot_path);
    
    bool collect_linux_memory_dump(const std::string& output_path);
    bool collect_linux_system_files(const std::string& output_directory);
    
#endif
    
    // Utility methods
    std::string get_backup_storage_path() const;
    std::string get_forensic_storage_path() const;
    std::string get_snapshot_storage_path() const;
    
    bool is_backup_expired(const BackupEntry& backup) const;
    bool ensure_storage_directory_exists(const std::string& path);
    
    // Logging and reporting
    void log_recovery_event(const std::string& event_type,
                           const std::string& operation_id,
                           const std::string& details);
    
    void generate_forensic_report(const std::string& collection_id);
    void update_chain_of_custody(const std::string& collection_id,
                                const std::string& action,
                                const std::string& user);
};
