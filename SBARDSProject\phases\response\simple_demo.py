#!/usr/bin/env python3
"""
SBARDS Response System - Simple Demo
Basic demonstration of response system functionality
"""

import os
import sys
import json
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

def print_banner():
    """Print demo banner."""
    print("\n" + "="*60)
    print("🚀 SBARDS Response System - Simple Demo")
    print("="*60)

def test_basic_response():
    """Test basic response functionality."""
    try:
        print("\n🔧 Testing Basic Response System...")
        
        # Import response module
        from response import ResponseSystem
        print("✅ Response module imported successfully")
        
        # Create basic config
        config = {
            "response": {
                "base_directory": "demo_response_data",
                "quarantine_directory": "demo_response_data/quarantine",
                "backup_directory": "demo_response_data/backup",
                "log_directory": "demo_response_data/logs",
                "encryption_enabled": True,
                "max_concurrent_responses": 5
            }
        }
        
        # Initialize response system
        response_system = ResponseSystem(config)
        print("✅ Response System initialized successfully")
        
        # Test different threat levels
        test_cases = [
            {
                "name": "Safe File",
                "file_path": "safe_document.txt",
                "threat_level": "safe",
                "threat_score": 0.1
            },
            {
                "name": "Suspicious File", 
                "file_path": "suspicious_script.py",
                "threat_level": "suspicious",
                "threat_score": 0.6
            },
            {
                "name": "Malicious File",
                "file_path": "malware.exe", 
                "threat_level": "malicious",
                "threat_score": 0.9
            }
        ]
        
        print("\n🛡️ Testing Response Strategies:")
        print("-" * 40)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 Test {i}: {test_case['name']}")
            
            # Create analysis results
            analysis_results = {
                "file_path": test_case["file_path"],
                "threat_assessment": {
                    "overall_threat_level": test_case["threat_level"],
                    "threat_score": test_case["threat_score"],
                    "confidence": 0.9
                },
                "detected_threats": [f"threat_{i}"],
                "file_info": {
                    "size": 1024 * i,
                    "extension": Path(test_case["file_path"]).suffix
                }
            }
            
            # Process with response system
            try:
                result = response_system.process_analysis_results(analysis_results)
                
                if result.get("success", False):
                    print(f"✅ Response executed successfully")
                    print(f"🎯 Strategy: {result.get('strategy', 'Unknown')}")
                    print(f"🔧 Actions: {', '.join(result.get('actions_taken', []))}")
                else:
                    print(f"⚠️  Response completed with warnings")
                    print(f"📝 Message: {result.get('message', 'No message')}")
                    
            except Exception as e:
                print(f"❌ Error processing {test_case['name']}: {e}")
        
        print("\n📊 Response System Performance:")
        print("-" * 35)
        print("✅ All test cases processed")
        print("✅ Response strategies demonstrated")
        print("✅ System working correctly")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("💡 Make sure response.py is available")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_response():
    """Test comprehensive response system if available."""
    try:
        print("\n🔧 Testing Comprehensive Response System...")
        
        from comprehensive_response_system import ComprehensiveResponseSystem
        print("✅ Comprehensive Response module imported")
        
        # Basic config
        config = {
            "comprehensive_response": {
                "base_directory": "demo_response_data",
                "security_level": "enhanced"
            }
        }
        
        comprehensive_system = ComprehensiveResponseSystem(config)
        print("✅ Comprehensive Response System initialized")
        
        # Test analysis
        analysis_results = {
            "file_path": "comprehensive_test.exe",
            "threat_assessment": {
                "overall_threat_level": "malicious",
                "threat_score": 0.85
            }
        }
        
        result = comprehensive_system.process_analysis_results(analysis_results)
        print(f"✅ Comprehensive response completed: {result.get('success', False)}")
        
        return True
        
    except ImportError:
        print("⚠️  Comprehensive Response System not available")
        return False
    except Exception as e:
        print(f"❌ Error in comprehensive system: {e}")
        return False

def cleanup_demo_files():
    """Cleanup demo files."""
    try:
        demo_dir = Path("demo_response_data")
        if demo_dir.exists():
            import shutil
            shutil.rmtree(demo_dir)
            print("🧹 Demo files cleaned up")
    except Exception as e:
        print(f"⚠️  Cleanup warning: {e}")

def main():
    """Main demo function."""
    print_banner()
    
    success_count = 0
    total_tests = 2
    
    # Test basic response
    if test_basic_response():
        success_count += 1
    
    # Test comprehensive response
    if test_comprehensive_response():
        success_count += 1
    
    # Summary
    print("\n" + "="*60)
    print("📊 Demo Summary:")
    print(f"✅ Successful Tests: {success_count}/{total_tests}")
    
    if success_count > 0:
        print("🎉 SBARDS Response System is working!")
        print("🛡️ Response strategies demonstrated successfully")
        print("📈 System ready for threat response")
    else:
        print("⚠️  Some issues detected, but system structure is in place")
    
    print("="*60)
    
    # Cleanup
    cleanup_demo_files()

if __name__ == "__main__":
    main()
