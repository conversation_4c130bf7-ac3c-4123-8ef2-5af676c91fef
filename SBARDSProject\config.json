{"scanner": {"target_directory": "samples", "recursive": true, "max_depth": 5, "exclude_dirs": [], "exclude_extensions": [], "max_file_size_mb": 100}, "rules": {"rule_files": ["rules/custom_rules.yar"], "enable_categories": ["all"]}, "output": {"log_directory": "logs", "output_directory": "output", "json_output": true, "csv_output": false, "html_report": false, "log_level": "info"}, "performance": {"threads": 1, "batch_size": 10, "timeout_seconds": 30}}