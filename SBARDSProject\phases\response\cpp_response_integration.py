#!/usr/bin/env python3
"""
SBARDS C++ Response Integration Module
High-performance response layer integration with C++ components

This module provides Python interface to the C++ Response Engine,
enabling seamless integration between Python orchestration and
high-performance C++ response components.
"""

import os
import sys
import json
import logging
import ctypes
import asyncio
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from datetime import datetime
import threading
import time

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class CPPResponseIntegration:
    """
    C++ Response Integration Class

    Provides Python interface to high-performance C++ response components
    including isolation, quarantine, notifications, and recovery systems.
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the C++ Response Integration."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.CPPResponseIntegration")

        # C++ library handle
        self.cpp_lib = None
        self.response_bridge = None
        self.initialized = False

        # Threading
        self.lock = threading.RLock()

        # Statistics
        self.integration_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "last_request_time": None
        }

    def initialize(self) -> bool:
        """Initialize the C++ Response Integration."""
        try:
            with self.lock:
                if self.initialized:
                    return True

                self.logger.info("Initializing C++ Response Integration...")

                # Load C++ library
                if not self._load_cpp_library():
                    self.logger.error("Failed to load C++ response library")
                    return False

                # Create response bridge
                if not self._create_response_bridge():
                    self.logger.error("Failed to create response bridge")
                    return False

                # Initialize bridge with configuration
                config_json = json.dumps(self._convert_config_to_cpp_format())
                if not self._initialize_bridge(config_json):
                    self.logger.error("Failed to initialize response bridge")
                    return False

                self.initialized = True
                self.logger.info("C++ Response Integration initialized successfully")
                return True

        except Exception as e:
            self.logger.error(f"C++ Response Integration initialization error: {e}")
            return False

    def shutdown(self):
        """Shutdown the C++ Response Integration."""
        try:
            with self.lock:
                if not self.initialized:
                    return

                self.logger.info("Shutting down C++ Response Integration...")

                if self.response_bridge and self.cpp_lib:
                    # Shutdown bridge
                    self.cpp_lib.shutdown_response_bridge(self.response_bridge)

                    # Destroy bridge
                    self.cpp_lib.destroy_response_bridge(self.response_bridge)
                    self.response_bridge = None

                self.cpp_lib = None
                self.initialized = False

                self.logger.info("C++ Response Integration shutdown complete")

        except Exception as e:
            self.logger.error(f"C++ Response Integration shutdown error: {e}")

    async def process_analysis_results(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process analysis results using C++ Response Engine.

        Args:
            analysis_results: Analysis results from dynamic analysis

        Returns:
            Dict containing response actions taken and their results
        """
        start_time = time.time()

        try:
            with self.lock:
                if not self.initialized:
                    raise RuntimeError("C++ Response Integration not initialized")

                self.logger.info("Processing analysis results with C++ Response Engine")

                # Convert analysis results to JSON
                results_json = json.dumps(self._convert_analysis_results_to_cpp_format(analysis_results))

                # Call C++ function
                result_ptr = self.cpp_lib.process_analysis_results_bridge(
                    self.response_bridge,
                    results_json.encode('utf-8')
                )

                if not result_ptr:
                    raise RuntimeError("C++ Response Engine returned null result")

                # Convert result back to Python
                result_json = ctypes.string_at(result_ptr).decode('utf-8')
                self.cpp_lib.free_bridge_memory(result_ptr)

                response_result = json.loads(result_json)

                # Update statistics
                self._update_statistics(True, time.time() - start_time)

                self.logger.info("Analysis results processed successfully")
                return response_result

        except Exception as e:
            self._update_statistics(False, time.time() - start_time)
            self.logger.error(f"Error processing analysis results: {e}")
            return {"success": False, "error": str(e)}

    async def execute_response_action(self, action: str, file_path: str,
                                    metadata: Dict[str, str] = None) -> Dict[str, Any]:
        """
        Execute specific response action using C++ Response Engine.

        Args:
            action: Response action to execute
            file_path: Target file path
            metadata: Additional metadata

        Returns:
            Dict containing response execution result
        """
        start_time = time.time()

        try:
            with self.lock:
                if not self.initialized:
                    raise RuntimeError("C++ Response Integration not initialized")

                self.logger.info(f"Executing response action: {action} for {file_path}")

                # Prepare metadata
                if metadata is None:
                    metadata = {}

                metadata_json = json.dumps(metadata)

                # Call C++ function
                result_ptr = self.cpp_lib.execute_response_action_bridge(
                    self.response_bridge,
                    action.encode('utf-8'),
                    file_path.encode('utf-8'),
                    metadata_json.encode('utf-8')
                )

                if not result_ptr:
                    raise RuntimeError("C++ Response Engine returned null result")

                # Convert result back to Python
                result_json = ctypes.string_at(result_ptr).decode('utf-8')
                self.cpp_lib.free_bridge_memory(result_ptr)

                response_result = json.loads(result_json)

                # Update statistics
                self._update_statistics(True, time.time() - start_time)

                self.logger.info(f"Response action {action} executed successfully")
                return response_result

        except Exception as e:
            self._update_statistics(False, time.time() - start_time)
            self.logger.error(f"Error executing response action: {e}")
            return {"success": False, "error": str(e)}

    def get_response_statistics(self) -> Dict[str, Any]:
        """Get response statistics from C++ Response Engine."""
        try:
            with self.lock:
                if not self.initialized:
                    return {"error": "C++ Response Integration not initialized"}

                # Get C++ statistics
                result_ptr = self.cpp_lib.get_response_statistics_bridge(self.response_bridge)

                if not result_ptr:
                    return {"error": "Failed to get C++ statistics"}

                result_json = ctypes.string_at(result_ptr).decode('utf-8')
                self.cpp_lib.free_bridge_memory(result_ptr)

                cpp_stats = json.loads(result_json)

                # Combine with integration statistics
                combined_stats = {
                    "cpp_engine": cpp_stats,
                    "integration": self.integration_stats
                }

                return combined_stats

        except Exception as e:
            self.logger.error(f"Error getting response statistics: {e}")
            return {"error": str(e)}

    def get_active_sessions(self) -> List[str]:
        """Get active response sessions from C++ Response Engine."""
        try:
            with self.lock:
                if not self.initialized:
                    return []

                result_ptr = self.cpp_lib.get_active_sessions_bridge(self.response_bridge)

                if not result_ptr:
                    return []

                result_json = ctypes.string_at(result_ptr).decode('utf-8')
                self.cpp_lib.free_bridge_memory(result_ptr)

                sessions = json.loads(result_json)
                return sessions.get("sessions", [])

        except Exception as e:
            self.logger.error(f"Error getting active sessions: {e}")
            return []

    def cancel_response_session(self, session_id: str) -> bool:
        """Cancel active response session."""
        try:
            with self.lock:
                if not self.initialized:
                    return False

                result = self.cpp_lib.cancel_response_session_bridge(
                    self.response_bridge,
                    session_id.encode('utf-8')
                )

                return bool(result)

        except Exception as e:
            self.logger.error(f"Error cancelling response session: {e}")
            return False

    def update_configuration(self, new_config: Dict[str, Any]) -> bool:
        """Update C++ Response Engine configuration."""
        try:
            with self.lock:
                if not self.initialized:
                    return False

                # Update local config
                self.config.update(new_config)

                # Convert to C++ format and update
                config_json = json.dumps(self._convert_config_to_cpp_format())
                result = self.cpp_lib.update_configuration_bridge(
                    self.response_bridge,
                    config_json.encode('utf-8')
                )

                return bool(result)

        except Exception as e:
            self.logger.error(f"Error updating configuration: {e}")
            return False

    def _load_cpp_library(self) -> bool:
        """Load the C++ response library."""
        try:
            # Determine library path based on platform
            lib_paths = [
                "scanner_core/cpp/build/lib/libsbards_response_engine.so",
                "scanner_core/cpp/build/lib/libsbards_response_engine.dll",
                "scanner_core/cpp/build/lib/libsbards_response_engine.dylib",
                "scanner_core/cpp/build/libsbards_response_engine.so",
                "scanner_core/cpp/build/libsbards_response_engine.dll"
            ]

            for lib_path in lib_paths:
                full_path = project_root / lib_path
                if full_path.exists():
                    try:
                        self.cpp_lib = ctypes.CDLL(str(full_path))
                        self._setup_cpp_function_signatures()
                        self.logger.info(f"Loaded C++ response library: {full_path}")
                        return True
                    except Exception as e:
                        self.logger.warning(f"Failed to load {full_path}: {e}")

            self.logger.error("C++ response library not found")
            return False

        except Exception as e:
            self.logger.error(f"Error loading C++ library: {e}")
            return False

    def _setup_cpp_function_signatures(self):
        """Setup C++ function signatures for ctypes."""
        # create_response_bridge
        self.cpp_lib.create_response_bridge.restype = ctypes.c_void_p
        self.cpp_lib.create_response_bridge.argtypes = []

        # destroy_response_bridge
        self.cpp_lib.destroy_response_bridge.restype = None
        self.cpp_lib.destroy_response_bridge.argtypes = [ctypes.c_void_p]

        # initialize_response_bridge
        self.cpp_lib.initialize_response_bridge.restype = ctypes.c_int
        self.cpp_lib.initialize_response_bridge.argtypes = [ctypes.c_void_p, ctypes.c_char_p]

        # shutdown_response_bridge
        self.cpp_lib.shutdown_response_bridge.restype = None
        self.cpp_lib.shutdown_response_bridge.argtypes = [ctypes.c_void_p]

        # process_analysis_results_bridge
        self.cpp_lib.process_analysis_results_bridge.restype = ctypes.c_char_p
        self.cpp_lib.process_analysis_results_bridge.argtypes = [ctypes.c_void_p, ctypes.c_char_p]

        # execute_response_action_bridge
        self.cpp_lib.execute_response_action_bridge.restype = ctypes.c_char_p
        self.cpp_lib.execute_response_action_bridge.argtypes = [
            ctypes.c_void_p, ctypes.c_char_p, ctypes.c_char_p, ctypes.c_char_p
        ]

        # get_response_statistics_bridge
        self.cpp_lib.get_response_statistics_bridge.restype = ctypes.c_char_p
        self.cpp_lib.get_response_statistics_bridge.argtypes = [ctypes.c_void_p]

        # get_active_sessions_bridge
        self.cpp_lib.get_active_sessions_bridge.restype = ctypes.c_char_p
        self.cpp_lib.get_active_sessions_bridge.argtypes = [ctypes.c_void_p]

        # cancel_response_session_bridge
        self.cpp_lib.cancel_response_session_bridge.restype = ctypes.c_int
        self.cpp_lib.cancel_response_session_bridge.argtypes = [ctypes.c_void_p, ctypes.c_char_p]

        # update_configuration_bridge
        self.cpp_lib.update_configuration_bridge.restype = ctypes.c_int
        self.cpp_lib.update_configuration_bridge.argtypes = [ctypes.c_void_p, ctypes.c_char_p]

        # free_bridge_memory
        self.cpp_lib.free_bridge_memory.restype = None
        self.cpp_lib.free_bridge_memory.argtypes = [ctypes.c_char_p]

    def _create_response_bridge(self) -> bool:
        """Create response bridge instance."""
        try:
            self.response_bridge = self.cpp_lib.create_response_bridge()
            return self.response_bridge is not None
        except Exception as e:
            self.logger.error(f"Error creating response bridge: {e}")
            return False

    def _initialize_bridge(self, config_json: str) -> bool:
        """Initialize response bridge with configuration."""
        try:
            result = self.cpp_lib.initialize_response_bridge(
                self.response_bridge,
                config_json.encode('utf-8')
            )
            return bool(result)
        except Exception as e:
            self.logger.error(f"Error initializing response bridge: {e}")
            return False

    def _convert_config_to_cpp_format(self) -> Dict[str, Any]:
        """Convert Python config to C++ format."""
        response_config = self.config.get("comprehensive_response", {})

        cpp_config = {
            "enabled": response_config.get("enabled", True),
            "base_directory": response_config.get("base_directory", "response_data"),
            "log_level": response_config.get("log_level", "INFO"),

            # Isolation settings
            "network_isolation_enabled": response_config.get("network_isolation_enabled", True),
            "process_isolation_enabled": response_config.get("process_isolation_enabled", True),
            "file_system_isolation_enabled": response_config.get("file_system_isolation_enabled", True),

            # Notification settings
            "email_notifications_enabled": response_config.get("email_notifications_enabled", False),
            "slack_notifications_enabled": response_config.get("slack_notifications_enabled", False),
            "sms_notifications_enabled": response_config.get("sms_notifications_enabled", False),
            "webhook_notifications_enabled": response_config.get("webhook_notifications_enabled", False),

            # Quarantine settings
            "quarantine_directory": response_config.get("quarantine_directory", "response_data/quarantine"),
            "encryption_enabled": response_config.get("encryption_enabled", True),
            "encryption_key": response_config.get("encryption_key", "default_key"),

            # Honeypot settings
            "honeypot_directory": response_config.get("honeypot_directory", "response_data/honeypot"),
            "honeypot_enabled": response_config.get("honeypot_enabled", True),
            "honeypot_environments": response_config.get("honeypot_environments", ["generic"]),

            # Permission settings
            "dynamic_permissions_enabled": response_config.get("dynamic_permissions_enabled", True),
            "apploader_integration_enabled": response_config.get("apploader_integration_enabled", False),
            "selinux_integration_enabled": response_config.get("selinux_integration_enabled", False),

            # Recovery settings
            "auto_recovery_enabled": response_config.get("auto_recovery_enabled", True),
            "backup_directory": response_config.get("backup_directory", "response_data/backup"),
            "backup_retention_days": response_config.get("backup_retention_days", 30),

            # Advanced settings
            "max_concurrent_responses": response_config.get("max_concurrent_responses", 10),
            "response_timeout_seconds": response_config.get("response_timeout_seconds", 300),
            "blockchain_integration_enabled": response_config.get("blockchain_integration_enabled", False),
            "ml_model_updates_enabled": response_config.get("ml_model_updates_enabled", True)
        }

        return cpp_config

    def _convert_analysis_results_to_cpp_format(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Python analysis results to C++ format."""
        # Map threat levels
        threat_level_map = {
            "safe": 0,
            "clean": 0,
            "benign": 0,
            "suspicious": 1,
            "medium": 1,
            "malicious": 2,
            "high": 2,
            "critical": 3,
            "advanced": 4
        }

        threat_level = analysis_results.get("threat_assessment", {}).get("overall_threat_level", "unknown")
        threat_score = analysis_results.get("threat_assessment", {}).get("threat_score", 0.0)

        cpp_results = {
            "file_path": analysis_results.get("file_path", ""),
            "file_hash_sha256": analysis_results.get("file_hash", {}).get("sha256", ""),
            "file_hash_md5": analysis_results.get("file_hash", {}).get("md5", ""),
            "file_hash_sha1": analysis_results.get("file_hash", {}).get("sha1", ""),
            "threat_level": threat_level_map.get(threat_level.lower(), 0),
            "threat_score": float(threat_score),
            "detected_threats": analysis_results.get("detected_threats", []),
            "behavioral_indicators": analysis_results.get("behavioral_analysis", {}),
            "static_indicators": analysis_results.get("static_analysis", {}),
            "network_indicators": analysis_results.get("network_analysis", {}),
            "analysis_timestamp": datetime.now().isoformat(),
            "analysis_engine_version": "1.0.0",
            "metadata": analysis_results.get("metadata", {})
        }

        return cpp_results

    def _update_statistics(self, success: bool, response_time: float):
        """Update integration statistics."""
        try:
            self.integration_stats["total_requests"] += 1

            if success:
                self.integration_stats["successful_requests"] += 1
            else:
                self.integration_stats["failed_requests"] += 1

            # Update average response time
            total_requests = self.integration_stats["total_requests"]
            current_avg = self.integration_stats["average_response_time"]
            new_avg = ((current_avg * (total_requests - 1)) + response_time) / total_requests
            self.integration_stats["average_response_time"] = new_avg

            self.integration_stats["last_request_time"] = datetime.now().isoformat()

        except Exception as e:
            self.logger.error(f"Error updating statistics: {e}")


class CPPResponseIntegrationFactory:
    """Factory class for creating CPP Response Integration instances."""

    @staticmethod
    def create(config: Dict[str, Any]) -> CPPResponseIntegration:
        """Create and initialize CPP Response Integration instance."""
        integration = CPPResponseIntegration(config)
        if integration.initialize():
            return integration
        else:
            raise RuntimeError("Failed to initialize CPP Response Integration")

    @staticmethod
    def create_with_fallback(config: Dict[str, Any]) -> Optional[CPPResponseIntegration]:
        """Create CPP Response Integration with fallback to None if failed."""
        try:
            return CPPResponseIntegrationFactory.create(config)
        except Exception as e:
            logging.getLogger("SBARDS.CPPResponseIntegrationFactory").warning(
                f"Failed to create CPP Response Integration: {e}"
            )
            return None


# Async wrapper for easier integration
class AsyncCPPResponseIntegration:
    """Async wrapper for CPP Response Integration."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize async wrapper."""
        self.integration = CPPResponseIntegration(config)
        self.executor = None

    async def initialize(self) -> bool:
        """Initialize the integration."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.integration.initialize)

    async def shutdown(self):
        """Shutdown the integration."""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(self.executor, self.integration.shutdown)

    async def process_analysis_results(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Process analysis results asynchronously."""
        return await self.integration.process_analysis_results(analysis_results)

    async def execute_response_action(self, action: str, file_path: str,
                                    metadata: Dict[str, str] = None) -> Dict[str, Any]:
        """Execute response action asynchronously."""
        return await self.integration.execute_response_action(action, file_path, metadata)

    async def get_response_statistics(self) -> Dict[str, Any]:
        """Get response statistics asynchronously."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.integration.get_response_statistics)

    async def get_active_sessions(self) -> List[str]:
        """Get active sessions asynchronously."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.integration.get_active_sessions)

    async def cancel_response_session(self, session_id: str) -> bool:
        """Cancel response session asynchronously."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.integration.cancel_response_session, session_id)

    async def update_configuration(self, new_config: Dict[str, Any]) -> bool:
        """Update configuration asynchronously."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.integration.update_configuration, new_config)
