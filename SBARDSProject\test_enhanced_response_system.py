#!/usr/bin/env python3
"""
SBARDS Enhanced Response System Tests
Comprehensive Testing Suite for Enhanced ML-Driven Response

This test suite covers:
- ML models functionality
- Enhanced threat assessment
- Adaptive thresholds
- Cross-layer integration
- Response optimization
- Performance benchmarks
"""

import os
import sys
import json
import logging
import asyncio
import unittest
import time
from pathlib import Path
from datetime import datetime, timezone
import hashlib

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import test modules
from phases.response.enhanced_response_system import EnhancedResponseSystem
from phases.response.ml_models_manager import AdvancedMLModelsManager

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("SBARDS.EnhancedTests")

class TestEnhancedResponseSystem(unittest.TestCase):
    """Test cases for Enhanced Response System."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        cls.config = {
            "enhanced_response": {
                "adaptive_thresholds_enabled": True,
                "cross_layer_integration": True,
                "real_time_learning": True
            },
            "ml_models": {
                "auto_retrain_enabled": False,  # Disable for testing
                "ensemble_enabled": True,
                "ml_models_directory": str(project_root / "test_ml_models")
            },
            "comprehensive_response": {
                "base_directory": str(project_root / "test_response_data"),
                "blockchain_enabled": False,  # Disable for testing
                "cpp_integration_enabled": False,
                "notification_enabled": False
            }
        }
        
        # Create test directories
        test_dirs = [
            project_root / "test_ml_models",
            project_root / "test_response_data"
        ]
        
        for test_dir in test_dirs:
            test_dir.mkdir(exist_ok=True)
    
    def setUp(self):
        """Set up each test."""
        self.enhanced_response = EnhancedResponseSystem(self.config)
    
    def tearDown(self):
        """Clean up after each test."""
        if hasattr(self, 'enhanced_response'):
            self.enhanced_response.shutdown()
    
    def create_test_analysis_results(self, threat_level: str = "suspicious") -> dict:
        """Create test analysis results."""
        sample_content = f"test_file_{threat_level}_{time.time()}"
        file_hash = hashlib.sha256(sample_content.encode()).hexdigest()
        
        return {
            "file_path": f"C:\\test\\{threat_level}_file.exe",
            "file_hash": {"sha256": file_hash},
            "file_info": {
                "size": 1024 * 100,
                "mime_type": "application/x-executable",
                "extension": ".exe"
            },
            "static_analysis": {
                "entropy": 7.5 if threat_level == "malicious" else 5.0,
                "packed": threat_level == "malicious",
                "pe_analysis": {
                    "sections": [{"name": ".text", "entropy": 6.0}],
                    "imports": ["kernel32.dll!CreateProcessA"]
                },
                "strings": ["test string"],
                "suspicious_strings": ["CreateRemoteThread"] if threat_level != "safe" else []
            },
            "dynamic_analysis": {
                "api_calls": [{"api_name": "CreateFileA", "timestamp": 1000}],
                "network_connections": [] if threat_level == "safe" else [
                    {"ip": "***********", "port": 80, "protocol": "TCP"}
                ],
                "file_operations": [{"operation": "write", "file_path": "test.txt"}],
                "registry_changes": [] if threat_level == "safe" else [
                    {"key": "HKEY_CURRENT_USER\\Run", "suspicious": True}
                ],
                "process_info": {"main": {"pid": 1234, "suspicious": threat_level != "safe"}}
            },
            "yara_analysis": {
                "matches": [] if threat_level == "safe" else [
                    {"rule_name": "test_rule", "family": "trojan", "confidence": 0.8}
                ]
            },
            "network_analysis": {"connections": [], "suspicious_domains": []},
            "threat_intelligence": {"iocs": [], "attribution": {}},
            "threat_assessment": {
                "overall_threat_level": threat_level,
                "threat_score": {"safe": 0.1, "suspicious": 0.6, "malicious": 0.9}.get(threat_level, 0.5),
                "confidence": 0.8
            }
        }
    
    async def test_enhanced_threat_assessment(self):
        """Test enhanced threat assessment functionality."""
        print("🧪 Testing Enhanced Threat Assessment...")
        
        test_cases = ["safe", "suspicious", "malicious"]
        
        for threat_level in test_cases:
            with self.subTest(threat_level=threat_level):
                analysis_results = self.create_test_analysis_results(threat_level)
                
                # Test enhanced assessment
                enhanced_assessment = await self.enhanced_response._perform_enhanced_threat_assessment(analysis_results)
                
                # Verify assessment structure
                self.assertIsNotNone(enhanced_assessment)
                self.assertEqual(enhanced_assessment.original_threat_level, threat_level)
                self.assertIsInstance(enhanced_assessment.ml_confidence, float)
                self.assertIsInstance(enhanced_assessment.malware_family, str)
                self.assertIsInstance(enhanced_assessment.evasion_techniques, list)
                self.assertIsInstance(enhanced_assessment.behavioral_score, float)
                self.assertIsInstance(enhanced_assessment.anomaly_detected, bool)
                self.assertIsInstance(enhanced_assessment.optimal_response, str)
                self.assertIsInstance(enhanced_assessment.risk_factors, list)
                self.assertIsInstance(enhanced_assessment.mitigation_strategies, list)
                
                print(f"  ✅ {threat_level.capitalize()} assessment: ML confidence {enhanced_assessment.ml_confidence:.2f}")
    
    async def test_adaptive_thresholds(self):
        """Test adaptive threshold adjustment."""
        print("🧪 Testing Adaptive Thresholds...")
        
        # Get initial thresholds
        initial_thresholds = self.enhanced_response.adaptive_thresholds.copy()
        
        # Create high-confidence assessment
        analysis_results = self.create_test_analysis_results("suspicious")
        enhanced_assessment = await self.enhanced_response._perform_enhanced_threat_assessment(analysis_results)
        enhanced_assessment.ml_confidence = 0.95  # High confidence
        
        # Adjust thresholds
        await self.enhanced_response._adjust_adaptive_thresholds(enhanced_assessment, analysis_results)
        
        # Verify thresholds were adjusted
        new_thresholds = self.enhanced_response.adaptive_thresholds
        
        # At least one threshold should have changed
        threshold_changed = any(
            initial_thresholds[key] != new_thresholds[key] 
            for key in initial_thresholds.keys()
        )
        
        self.assertTrue(threshold_changed, "Adaptive thresholds should adjust based on ML confidence")
        print(f"  ✅ Thresholds adjusted: {new_thresholds}")
    
    async def test_cross_layer_integration(self):
        """Test cross-layer data integration."""
        print("🧪 Testing Cross-Layer Integration...")
        
        analysis_results = self.create_test_analysis_results("malicious")
        
        # Test cross-layer data integration
        cross_layer_data = await self.enhanced_response._integrate_cross_layer_data(analysis_results)
        
        # Verify cross-layer data structure
        self.assertIsInstance(cross_layer_data, dict)
        self.assertIn("static_analysis_correlation", cross_layer_data)
        self.assertIn("dynamic_analysis_correlation", cross_layer_data)
        self.assertIn("yara_rules_correlation", cross_layer_data)
        self.assertIn("network_analysis_correlation", cross_layer_data)
        self.assertIn("threat_intelligence_correlation", cross_layer_data)
        self.assertIn("historical_patterns", cross_layer_data)
        
        print(f"  ✅ Cross-layer integration successful")
    
    async def test_response_strategy_selection(self):
        """Test optimal response strategy selection."""
        print("🧪 Testing Response Strategy Selection...")
        
        test_cases = [
            ("safe", ["monitor", "quarantine"]),
            ("suspicious", ["quarantine", "monitor"]),
            ("malicious", ["block", "quarantine", "analyze"]),
            ("critical", ["analyze", "block"])
        ]
        
        for threat_level, expected_strategies in test_cases:
            with self.subTest(threat_level=threat_level):
                analysis_results = self.create_test_analysis_results(threat_level)
                enhanced_assessment = await self.enhanced_response._perform_enhanced_threat_assessment(analysis_results)
                
                # Test strategy selection
                strategy = await self.enhanced_response._select_optimal_response_strategy(
                    enhanced_assessment, analysis_results
                )
                
                self.assertIsInstance(strategy, str)
                self.assertIn(strategy, expected_strategies, 
                            f"Strategy '{strategy}' not in expected strategies for {threat_level}")
                
                print(f"  ✅ {threat_level.capitalize()}: Selected strategy '{strategy}'")
    
    async def test_ml_models_integration(self):
        """Test ML models integration."""
        print("🧪 Testing ML Models Integration...")
        
        ml_manager = self.enhanced_response.ml_manager
        
        # Test threat level prediction
        analysis_results = self.create_test_analysis_results("suspicious")
        threat_prediction = await ml_manager.predict_threat_level(analysis_results)
        
        self.assertIsInstance(threat_prediction, dict)
        self.assertIn("prediction", threat_prediction)
        self.assertIn("confidence", threat_prediction)
        
        # Test malware family detection
        family_detection = await ml_manager.detect_malware_family(analysis_results)
        self.assertIsInstance(family_detection, dict)
        
        # Test anomaly detection
        anomaly_detection = await ml_manager.detect_anomalies(analysis_results)
        self.assertIsInstance(anomaly_detection, dict)
        self.assertIn("is_anomaly", anomaly_detection)
        
        # Test behavioral analysis
        behavior_analysis = await ml_manager.analyze_behavior(analysis_results)
        self.assertIsInstance(behavior_analysis, dict)
        self.assertIn("behavior_score", behavior_analysis)
        
        print(f"  ✅ ML models integration successful")
    
    async def test_enhanced_response_execution(self):
        """Test enhanced response execution."""
        print("🧪 Testing Enhanced Response Execution...")
        
        analysis_results = self.create_test_analysis_results("suspicious")
        
        # Test full enhanced response processing
        response_result = await self.enhanced_response.process_enhanced_analysis_results(analysis_results)
        
        # Verify response structure
        self.assertIsInstance(response_result, dict)
        self.assertIn("enhanced_assessment", response_result)
        self.assertIn("response_result", response_result)
        self.assertIn("ml_integration", response_result)
        self.assertTrue(response_result.get("ml_integration", False))
        
        # Verify enhanced assessment
        enhanced_assessment = response_result["enhanced_assessment"]
        self.assertIn("original_threat_level", enhanced_assessment)
        self.assertIn("ml_threat_prediction", enhanced_assessment)
        self.assertIn("ml_confidence", enhanced_assessment)
        
        print(f"  ✅ Enhanced response execution successful")
    
    async def test_performance_metrics(self):
        """Test performance metrics tracking."""
        print("🧪 Testing Performance Metrics...")
        
        # Process multiple files to generate metrics
        for i in range(3):
            threat_level = ["safe", "suspicious", "malicious"][i]
            analysis_results = self.create_test_analysis_results(threat_level)
            await self.enhanced_response.process_enhanced_analysis_results(analysis_results)
        
        # Get statistics
        stats = await self.enhanced_response.get_enhanced_statistics()
        
        # Verify statistics structure
        self.assertIsInstance(stats, dict)
        self.assertIn("enhanced_response_metrics", stats)
        self.assertIn("adaptive_thresholds", stats)
        self.assertIn("ml_model_stats", stats)
        
        # Verify metrics
        metrics = stats["enhanced_response_metrics"]
        self.assertGreater(metrics.get("total_responses", 0), 0)
        
        print(f"  ✅ Performance metrics: {metrics.get('total_responses', 0)} responses processed")

class TestMLModelsManager(unittest.TestCase):
    """Test cases for ML Models Manager."""
    
    def setUp(self):
        """Set up ML models manager test."""
        config = {
            "ml_models_directory": str(project_root / "test_ml_models"),
            "auto_retrain_enabled": False,
            "ensemble_enabled": True
        }
        self.ml_manager = AdvancedMLModelsManager(config)
    
    def tearDown(self):
        """Clean up ML models manager."""
        self.ml_manager.shutdown()
    
    async def test_model_initialization(self):
        """Test ML models initialization."""
        print("🧪 Testing ML Models Initialization...")
        
        # Check if models are loaded
        self.assertIsInstance(self.ml_manager.models, dict)
        self.assertGreater(len(self.ml_manager.models), 0)
        
        # Check model statistics
        stats = await self.ml_manager.get_model_statistics()
        self.assertIsInstance(stats, dict)
        self.assertIn("models_loaded", stats)
        self.assertIn("models_available", stats)
        
        print(f"  ✅ {stats.get('models_loaded', 0)} models initialized")
    
    async def test_model_predictions(self):
        """Test model predictions."""
        print("🧪 Testing Model Predictions...")
        
        # Create test features
        test_features = {
            "file_info": {"size": 1024},
            "static_analysis": {"entropy": 6.0},
            "dynamic_analysis": {
                "api_calls": [{"api_name": "CreateFileA"}],
                "network_connections": [],
                "file_operations": [],
                "registry_changes": [],
                "process_info": {}
            }
        }
        
        # Test threat level prediction
        threat_result = await self.ml_manager.predict_threat_level(test_features)
        self.assertIsInstance(threat_result, dict)
        self.assertNotIn("error", threat_result)
        
        print(f"  ✅ Model predictions working")

async def run_all_tests():
    """Run all test suites."""
    print("🚀 SBARDS Enhanced Response System Tests")
    print("=" * 60)
    
    # Test Enhanced Response System
    print("\n📊 Testing Enhanced Response System...")
    enhanced_tests = TestEnhancedResponseSystem()
    enhanced_tests.setUpClass()
    
    try:
        enhanced_tests.setUp()
        
        await enhanced_tests.test_enhanced_threat_assessment()
        await enhanced_tests.test_adaptive_thresholds()
        await enhanced_tests.test_cross_layer_integration()
        await enhanced_tests.test_response_strategy_selection()
        await enhanced_tests.test_ml_models_integration()
        await enhanced_tests.test_enhanced_response_execution()
        await enhanced_tests.test_performance_metrics()
        
        enhanced_tests.tearDown()
        print("✅ Enhanced Response System tests passed!")
        
    except Exception as e:
        print(f"❌ Enhanced Response System tests failed: {e}")
        logger.error(f"Enhanced Response System tests failed: {e}")
    
    # Test ML Models Manager
    print("\n🧠 Testing ML Models Manager...")
    ml_tests = TestMLModelsManager()
    
    try:
        ml_tests.setUp()
        
        await ml_tests.test_model_initialization()
        await ml_tests.test_model_predictions()
        
        ml_tests.tearDown()
        print("✅ ML Models Manager tests passed!")
        
    except Exception as e:
        print(f"❌ ML Models Manager tests failed: {e}")
        logger.error(f"ML Models Manager tests failed: {e}")
    
    print("\n✅ All Enhanced Response System Tests Complete!")
    print("=" * 60)

def main():
    """Main test function."""
    try:
        asyncio.run(run_all_tests())
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Tests failed: {e}")
        logger.error(f"Tests failed: {e}")

if __name__ == "__main__":
    main()
