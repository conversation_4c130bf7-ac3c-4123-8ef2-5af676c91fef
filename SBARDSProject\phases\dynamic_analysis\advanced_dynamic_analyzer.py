"""
SBARDS Advanced Dynamic Analysis Layer
Comprehensive dynamic analysis with C++ integration and advanced techniques

This module implements:
- Advanced sandbox orchestration (Docker, VM, Hybrid)
- Cuckoo Sandbox integration
- User environment simulation
- Behavioral analysis with ML
- C++ component integration
- Real-time monitoring and analysis
"""

import os
import sys
import json
import time
import asyncio
import logging
import threading
import subprocess
import tempfile
import shutil
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional, Set, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import ctypes
from ctypes import cdll, Structure, c_char_p, c_int, c_bool, c_void_p

# Third-party imports
import docker
import psutil
import numpy as np
import pandas as pd
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import yara
import pefile
import magic
import hashlib
import requests
import aiohttp
import asyncio
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# SBARDS imports
from core.logging import get_logger
from core.config import Config
from core.utils import SecurityUtils, PerformanceUtils
from phases.dynamic_analysis.sandbox_orchestrator import SandboxOrchestrator
from phases.dynamic_analysis.cuckoo_integration import CuckooIntegration
from phases.dynamic_analysis.user_simulator import UserSimulator
from phases.dynamic_analysis.behavioral_analyzer import BehavioralAnalyzer
from phases.dynamic_analysis.cpp_bridge import CPPBridge

@dataclass
class AnalysisRequest:
    """Dynamic analysis request structure"""
    file_path: str
    analysis_id: str
    priority: int = 1
    timeout_seconds: int = 300
    sandbox_types: List[str] = None
    enable_user_simulation: bool = True
    enable_network_monitoring: bool = True
    enable_memory_analysis: bool = True
    enable_api_hooking: bool = True
    static_analysis_results: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = None

@dataclass
class AnalysisResult:
    """Dynamic analysis result structure"""
    analysis_id: str
    file_path: str
    success: bool
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    sandbox_results: Dict[str, Any]
    behavioral_analysis: Dict[str, Any]
    memory_analysis: Dict[str, Any]
    network_analysis: Dict[str, Any]
    api_monitoring: Dict[str, Any]
    user_simulation: Dict[str, Any]
    threat_score: float
    threat_classification: str
    indicators_of_compromise: List[str]
    recommendations: List[str]
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

class AdvancedDynamicAnalyzer:
    """
    Advanced Dynamic Analysis Engine

    Orchestrates comprehensive dynamic analysis using multiple techniques:
    - Multi-environment sandboxing
    - Advanced behavioral analysis
    - Memory forensics
    - API monitoring
    - User interaction simulation
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Advanced Dynamic Analyzer

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = get_logger("SBARDS.AdvancedDynamicAnalyzer")

        # Dynamic analysis configuration
        self.dynamic_config = config.get("dynamic_analysis", {})

        # Analysis settings
        self.max_concurrent_analyses = self.dynamic_config.get("performance", {}).get("max_concurrent_analyses", 4)
        self.analysis_timeout = self.dynamic_config.get("analysis_timeout_seconds", 300)
        self.enable_ml_analysis = self.dynamic_config.get("behavioral_analysis", {}).get("ml_enabled", True)

        # Initialize components
        self.sandbox_orchestrator = SandboxOrchestrator(config)
        self.cuckoo_integration = CuckooIntegration(config)
        self.user_simulator = UserSimulator(config)
        self.behavioral_analyzer = BehavioralAnalyzer(config)
        self.cpp_bridge = CPPBridge(config)

        # Thread pools for concurrent analysis
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_concurrent_analyses)
        self.process_pool = ProcessPoolExecutor(max_workers=self.max_concurrent_analyses)

        # Analysis state
        self.active_analyses: Dict[str, AnalysisRequest] = {}
        self.completed_analyses: Dict[str, AnalysisResult] = {}
        self.analysis_lock = threading.Lock()

        # Performance monitoring
        self.performance_stats = {
            "total_analyses": 0,
            "successful_analyses": 0,
            "failed_analyses": 0,
            "average_duration": 0.0,
            "peak_memory_usage": 0,
            "cpu_usage_history": []
        }

        # Security features
        self.security_utils = SecurityUtils()
        self.performance_utils = PerformanceUtils()

        # Initialize ML models
        self._init_ml_models()

        # Initialize monitoring
        self._init_monitoring()

    def _init_ml_models(self):
        """Initialize machine learning models for behavioral analysis"""
        try:
            if self.enable_ml_analysis:
                # Initialize anomaly detection model
                self.anomaly_detector = IsolationForest(
                    contamination=0.1,
                    random_state=42,
                    n_estimators=100
                )

                # Initialize feature scaler
                self.feature_scaler = StandardScaler()

                # Load pre-trained models if available
                model_path = self.dynamic_config.get("behavioral_analysis", {}).get("ml_model_path")
                if model_path and os.path.exists(model_path):
                    self._load_pretrained_models(model_path)

                self.logger.info("ML models initialized successfully")
            else:
                self.logger.info("ML analysis disabled")

        except Exception as e:
            self.logger.error(f"Failed to initialize ML models: {e}")
            self.enable_ml_analysis = False

    def _init_monitoring(self):
        """Initialize system monitoring"""
        try:
            # Start performance monitoring thread
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self.monitoring_active = True
            self.monitoring_thread.start()

            self.logger.info("Monitoring initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize monitoring: {e}")

    async def analyze_file_async(self, request: AnalysisRequest) -> AnalysisResult:
        """
        Perform asynchronous dynamic analysis on a file

        Args:
            request: Analysis request

        Returns:
            Analysis result
        """
        start_time = datetime.now()

        try:
            # Validate request
            if not self._validate_analysis_request(request):
                return self._create_error_result(request, "Invalid analysis request")

            # Register analysis
            with self.analysis_lock:
                self.active_analyses[request.analysis_id] = request

            self.logger.info(f"Starting advanced dynamic analysis: {request.analysis_id}")

            # Initialize result
            result = AnalysisResult(
                analysis_id=request.analysis_id,
                file_path=request.file_path,
                success=False,
                start_time=start_time,
                end_time=start_time,
                duration_seconds=0.0,
                sandbox_results={},
                behavioral_analysis={},
                memory_analysis={},
                network_analysis={},
                api_monitoring={},
                user_simulation={},
                threat_score=0.0,
                threat_classification="UNKNOWN",
                indicators_of_compromise=[],
                recommendations=[],
                metadata=request.metadata or {}
            )

            # Perform multi-stage analysis
            analysis_tasks = []

            # Stage 1: Sandbox Analysis
            if self.dynamic_config.get("sandbox", {}).get("docker_enabled", True):
                analysis_tasks.append(self._analyze_in_sandbox(request))

            # Stage 2: Cuckoo Analysis (if enabled)
            if self.dynamic_config.get("cuckoo", {}).get("enabled", False):
                analysis_tasks.append(self._analyze_with_cuckoo(request))

            # Stage 3: Memory Analysis
            if request.enable_memory_analysis:
                analysis_tasks.append(self._analyze_memory(request))

            # Stage 4: API Monitoring
            if request.enable_api_hooking:
                analysis_tasks.append(self._monitor_api_calls(request))

            # Stage 5: User Simulation
            if request.enable_user_simulation:
                analysis_tasks.append(self._simulate_user_interaction(request))

            # Execute analysis tasks concurrently
            analysis_results = await asyncio.gather(*analysis_tasks, return_exceptions=True)

            # Process results
            for i, task_result in enumerate(analysis_results):
                if isinstance(task_result, Exception):
                    self.logger.error(f"Analysis task {i} failed: {task_result}")
                    continue

                # Merge results based on task type
                if i == 0:  # Sandbox results
                    result.sandbox_results = task_result
                elif i == 1:  # Cuckoo results
                    result.sandbox_results.update({"cuckoo": task_result})
                elif i == 2:  # Memory analysis
                    result.memory_analysis = task_result
                elif i == 3:  # API monitoring
                    result.api_monitoring = task_result
                elif i == 4:  # User simulation
                    result.user_simulation = task_result

            # Perform behavioral analysis
            result.behavioral_analysis = await self._perform_behavioral_analysis(result)

            # Calculate threat score
            result.threat_score = self._calculate_threat_score(result)
            result.threat_classification = self._classify_threat(result.threat_score)

            # Extract indicators of compromise
            result.indicators_of_compromise = self._extract_iocs(result)

            # Generate recommendations
            result.recommendations = self._generate_recommendations(result)

            # Finalize result
            end_time = datetime.now()
            result.end_time = end_time
            result.duration_seconds = (end_time - start_time).total_seconds()
            result.success = True

            # Store result
            with self.analysis_lock:
                self.completed_analyses[request.analysis_id] = result
                if request.analysis_id in self.active_analyses:
                    del self.active_analyses[request.analysis_id]

            # Update statistics
            self._update_performance_stats(result)

            self.logger.info(f"Advanced dynamic analysis completed: {request.analysis_id} "
                           f"(Score: {result.threat_score:.2f}, Classification: {result.threat_classification})")

            return result

        except Exception as e:
            self.logger.error(f"Advanced dynamic analysis failed: {e}")
            return self._create_error_result(request, str(e))

        finally:
            # Cleanup
            with self.analysis_lock:
                if request.analysis_id in self.active_analyses:
                    del self.active_analyses[request.analysis_id]

    def analyze_file(self, file_path: str, static_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Synchronous wrapper for file analysis

        Args:
            file_path: Path to file to analyze
            static_results: Results from static analysis

        Returns:
            Analysis results dictionary
        """
        # Create analysis request
        request = AnalysisRequest(
            file_path=file_path,
            analysis_id=self._generate_analysis_id(),
            static_analysis_results=static_results,
            metadata={"source": "sync_api"}
        )

        # Run async analysis
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(self.analyze_file_async(request))
            return asdict(result)
        finally:
            loop.close()

    async def _analyze_in_sandbox(self, request: AnalysisRequest) -> Dict[str, Any]:
        """Perform sandbox analysis"""
        try:
            return await self.sandbox_orchestrator.analyze_file_async(
                request.file_path,
                timeout=request.timeout_seconds,
                enable_network_monitoring=request.enable_network_monitoring
            )
        except Exception as e:
            self.logger.error(f"Sandbox analysis failed: {e}")
            return {"error": str(e), "success": False}

    async def _analyze_with_cuckoo(self, request: AnalysisRequest) -> Dict[str, Any]:
        """Perform Cuckoo Sandbox analysis"""
        try:
            return await self.cuckoo_integration.analyze_file_async(
                request.file_path,
                timeout=request.timeout_seconds
            )
        except Exception as e:
            self.logger.error(f"Cuckoo analysis failed: {e}")
            return {"error": str(e), "success": False}

    async def _analyze_memory(self, request: AnalysisRequest) -> Dict[str, Any]:
        """Perform memory analysis using C++ components"""
        try:
            return await self.cpp_bridge.analyze_memory_async(
                request.file_path,
                timeout=request.timeout_seconds
            )
        except Exception as e:
            self.logger.error(f"Memory analysis failed: {e}")
            return {"error": str(e), "success": False}

    async def _monitor_api_calls(self, request: AnalysisRequest) -> Dict[str, Any]:
        """Monitor API calls using C++ hooking framework"""
        try:
            return await self.cpp_bridge.monitor_api_calls_async(
                request.file_path,
                timeout=request.timeout_seconds
            )
        except Exception as e:
            self.logger.error(f"API monitoring failed: {e}")
            return {"error": str(e), "success": False}

    async def _simulate_user_interaction(self, request: AnalysisRequest) -> Dict[str, Any]:
        """Simulate user interactions"""
        try:
            return await self.user_simulator.simulate_interaction_async(
                request.file_path,
                timeout=request.timeout_seconds
            )
        except Exception as e:
            self.logger.error(f"User simulation failed: {e}")
            return {"error": str(e), "success": False}

    async def _perform_behavioral_analysis(self, result: AnalysisResult) -> Dict[str, Any]:
        """Perform comprehensive behavioral analysis"""
        try:
            return await self.behavioral_analyzer.analyze_behavior_async(result)
        except Exception as e:
            self.logger.error(f"Behavioral analysis failed: {e}")
            return {"error": str(e), "success": False}

    def _calculate_threat_score(self, result: AnalysisResult) -> float:
        """Calculate overall threat score"""
        try:
            score = 0.0
            weight_sum = 0.0

            # Sandbox analysis score (weight: 0.3)
            if result.sandbox_results.get("success", False):
                sandbox_score = result.sandbox_results.get("threat_score", 0.0)
                score += sandbox_score * 0.3
                weight_sum += 0.3

            # Behavioral analysis score (weight: 0.4)
            if result.behavioral_analysis.get("success", False):
                behavioral_score = result.behavioral_analysis.get("threat_score", 0.0)
                score += behavioral_score * 0.4
                weight_sum += 0.4

            # Memory analysis score (weight: 0.2)
            if result.memory_analysis.get("success", False):
                memory_score = result.memory_analysis.get("threat_score", 0.0)
                score += memory_score * 0.2
                weight_sum += 0.2

            # API monitoring score (weight: 0.1)
            if result.api_monitoring.get("success", False):
                api_score = result.api_monitoring.get("threat_score", 0.0)
                score += api_score * 0.1
                weight_sum += 0.1

            # Normalize score
            if weight_sum > 0:
                score = score / weight_sum

            return min(max(score, 0.0), 1.0)

        except Exception as e:
            self.logger.error(f"Failed to calculate threat score: {e}")
            return 0.0

    def _classify_threat(self, threat_score: float) -> str:
        """Classify threat based on score"""
        if threat_score >= 0.8:
            return "CRITICAL"
        elif threat_score >= 0.6:
            return "HIGH"
        elif threat_score >= 0.4:
            return "MEDIUM"
        elif threat_score >= 0.2:
            return "LOW"
        else:
            return "BENIGN"

    def _extract_iocs(self, result: AnalysisResult) -> List[str]:
        """Extract indicators of compromise"""
        iocs = []

        try:
            # Extract from sandbox results
            if "indicators" in result.sandbox_results:
                iocs.extend(result.sandbox_results["indicators"])

            # Extract from behavioral analysis
            if "indicators" in result.behavioral_analysis:
                iocs.extend(result.behavioral_analysis["indicators"])

            # Extract from memory analysis
            if "indicators" in result.memory_analysis:
                iocs.extend(result.memory_analysis["indicators"])

            # Extract from API monitoring
            if "indicators" in result.api_monitoring:
                iocs.extend(result.api_monitoring["indicators"])

            # Remove duplicates
            iocs = list(set(iocs))

        except Exception as e:
            self.logger.error(f"Failed to extract IOCs: {e}")

        return iocs

    def _generate_recommendations(self, result: AnalysisResult) -> List[str]:
        """Generate security recommendations"""
        recommendations = []

        try:
            if result.threat_score >= 0.6:
                recommendations.append("Quarantine file immediately")
                recommendations.append("Scan all systems for similar threats")
                recommendations.append("Update security signatures")

            if result.threat_score >= 0.4:
                recommendations.append("Monitor network traffic for suspicious activity")
                recommendations.append("Review system logs for anomalies")

            if "network_connections" in result.sandbox_results:
                recommendations.append("Block suspicious network destinations")

            if "registry_modifications" in result.api_monitoring:
                recommendations.append("Monitor registry for unauthorized changes")

        except Exception as e:
            self.logger.error(f"Failed to generate recommendations: {e}")

        return recommendations

    def _validate_analysis_request(self, request: AnalysisRequest) -> bool:
        """Validate analysis request"""
        try:
            if not request.file_path or not os.path.exists(request.file_path):
                return False

            if not request.analysis_id:
                return False

            if request.timeout_seconds <= 0 or request.timeout_seconds > 3600:
                return False

            return True

        except Exception as e:
            self.logger.error(f"Request validation failed: {e}")
            return False

    def _create_error_result(self, request: AnalysisRequest, error_message: str) -> AnalysisResult:
        """Create error result"""
        return AnalysisResult(
            analysis_id=request.analysis_id,
            file_path=request.file_path,
            success=False,
            start_time=datetime.now(),
            end_time=datetime.now(),
            duration_seconds=0.0,
            sandbox_results={},
            behavioral_analysis={},
            memory_analysis={},
            network_analysis={},
            api_monitoring={},
            user_simulation={},
            threat_score=0.0,
            threat_classification="ERROR",
            indicators_of_compromise=[],
            recommendations=[],
            error_message=error_message
        )

    def _generate_analysis_id(self) -> str:
        """Generate unique analysis ID"""
        import uuid
        timestamp = int(datetime.now().timestamp())
        unique_id = uuid.uuid4().hex[:8]
        return f"adv_analysis_{timestamp}_{unique_id}"

    def _get_file_size(self, file_path: str) -> int:
        """Get file size"""
        try:
            return os.path.getsize(file_path)
        except:
            return 0

    def _get_file_type(self, file_path: str) -> str:
        """Get file type"""
        try:
            import magic
            return magic.from_file(file_path)
        except:
            return Path(file_path).suffix.lower()

    def _update_performance_stats(self, result: AnalysisResult):
        """Update performance statistics"""
        try:
            self.performance_stats["total_analyses"] += 1

            if result.success:
                self.performance_stats["successful_analyses"] += 1
            else:
                self.performance_stats["failed_analyses"] += 1

            # Update average duration
            total_successful = self.performance_stats["successful_analyses"]
            if total_successful > 0:
                current_avg = self.performance_stats["average_duration"]
                new_avg = ((current_avg * (total_successful - 1)) + result.duration_seconds) / total_successful
                self.performance_stats["average_duration"] = new_avg

            # Update memory usage
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            if current_memory > self.performance_stats["peak_memory_usage"]:
                self.performance_stats["peak_memory_usage"] = current_memory

        except Exception as e:
            self.logger.error(f"Failed to update performance stats: {e}")

    def _monitoring_loop(self):
        """Performance monitoring loop"""
        try:
            while self.monitoring_active:
                try:
                    # Monitor CPU usage
                    cpu_percent = psutil.cpu_percent(interval=1)
                    self.performance_stats["cpu_usage_history"].append(cpu_percent)

                    # Keep only last 100 measurements
                    if len(self.performance_stats["cpu_usage_history"]) > 100:
                        self.performance_stats["cpu_usage_history"].pop(0)

                    # Monitor memory usage
                    memory_info = psutil.Process().memory_info()
                    current_memory_mb = memory_info.rss / 1024 / 1024

                    if current_memory_mb > self.performance_stats["peak_memory_usage"]:
                        self.performance_stats["peak_memory_usage"] = current_memory_mb

                    # Log warnings for high resource usage
                    if cpu_percent > 90:
                        self.logger.warning(f"High CPU usage detected: {cpu_percent}%")

                    if current_memory_mb > 4096:  # 4GB
                        self.logger.warning(f"High memory usage detected: {current_memory_mb:.1f} MB")

                    time.sleep(10)  # Monitor every 10 seconds

                except Exception as e:
                    self.logger.error(f"Monitoring error: {e}")
                    time.sleep(30)  # Wait longer on error

        except Exception as e:
            self.logger.error(f"Monitoring loop failed: {e}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return dict(self.performance_stats)

    def get_active_analyses(self) -> Dict[str, str]:
        """Get active analyses"""
        with self.analysis_lock:
            return {aid: req.file_path for aid, req in self.active_analyses.items()}

    def stop_analysis(self, analysis_id: str) -> bool:
        """Stop a running analysis"""
        try:
            with self.analysis_lock:
                if analysis_id in self.active_analyses:
                    # Mark for stopping - actual implementation would need
                    # to coordinate with running analysis tasks
                    self.logger.info(f"Stopping analysis: {analysis_id}")
                    return True
                return False
        except Exception as e:
            self.logger.error(f"Failed to stop analysis {analysis_id}: {e}")
            return False

    def cleanup(self):
        """Cleanup resources"""
        try:
            self.monitoring_active = False

            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5)

            self.thread_pool.shutdown(wait=True)
            self.process_pool.shutdown(wait=True)

            # Cleanup sandbox orchestrator
            if hasattr(self.sandbox_orchestrator, 'cleanup_all_analyses'):
                self.sandbox_orchestrator.cleanup_all_analyses()

            self.logger.info("Advanced Dynamic Analyzer cleanup completed")

        except Exception as e:
            self.logger.error(f"Cleanup failed: {e}")

    def __del__(self):
        """Destructor"""
        try:
            self.cleanup()
        except:
            pass
