/**
 * SBARDS Advanced Sandbox Engine Implementation
 */

#include "sandbox_engine.hpp"
#include <iostream>
#include <sstream>
#include <fstream>
#include <random>
#include <iomanip>
#include <algorithm>
#include <cstring>

#ifdef _WIN32
    #include <shlwapi.h>
    #include <winsock2.h>
    #include <iphlpapi.h>
    #pragma comment(lib, "ws2_32.lib")
    #pragma comment(lib, "iphlpapi.lib")
    #pragma comment(lib, "shlwapi.lib")
#else
    #include <sys/stat.h>
    #include <sys/types.h>
    #include <sys/resource.h>
    #include <fcntl.h>
    #include <errno.h>
    #include <dirent.h>
#endif

namespace sbards {
namespace sandbox {

// SandboxEngine Implementation
SandboxEngine::SandboxEngine(const SandboxConfig& config)
    : config_(config)
    , initialized_(false)
    , running_(false)
    , stop_requested_(false)
#ifdef _WIN32
    , process_handle_(INVALID_HANDLE_VALUE)
    , job_object_(INVALID_HANDLE_VALUE)
#else
    , child_pid_(-1)
#endif
{
    // Initialize components
    network_isolator_ = std::make_unique<NetworkIsolator>(config_);
    fs_virtualizer_ = std::make_unique<FileSystemVirtualizer>(config_);
    memory_isolator_ = std::make_unique<MemoryIsolator>(config_);

#ifndef _WIN32
    pipe_fd_[0] = pipe_fd_[1] = -1;
#endif
}

SandboxEngine::~SandboxEngine() {
    if (running_) {
        stop_execution();
    }

    cleanup_isolation();

#ifdef _WIN32
    if (process_handle_ != INVALID_HANDLE_VALUE) {
        CloseHandle(process_handle_);
    }
    if (job_object_ != INVALID_HANDLE_VALUE) {
        CloseHandle(job_object_);
    }
#else
    if (pipe_fd_[0] != -1) close(pipe_fd_[0]);
    if (pipe_fd_[1] != -1) close(pipe_fd_[1]);
#endif
}

bool SandboxEngine::initialize() {
    std::lock_guard<std::mutex> lock(state_mutex_);

    if (initialized_) {
        return true;
    }

    try {
        // Initialize components based on sandbox type
        bool success = false;

        switch (config_.type) {
            case SandboxType::DOCKER_CONTAINER:
                success = initialize_docker_sandbox();
                break;
            case SandboxType::VIRTUAL_MACHINE:
                success = initialize_vm_sandbox();
                break;
            case SandboxType::ISOLATED_PROCESS:
                success = initialize_process_sandbox();
                break;
            case SandboxType::HYBRID_ENVIRONMENT:
                // Try Docker first, fallback to process isolation
                success = initialize_docker_sandbox();
                if (!success) {
                    success = initialize_process_sandbox();
                }
                break;
        }

        if (!success) {
            return false;
        }

        // Initialize isolation components
        if (config_.network_isolation && !network_isolator_->initialize()) {
            return false;
        }

        if (config_.file_isolation && !fs_virtualizer_->initialize()) {
            return false;
        }

        if (config_.memory_isolation && !memory_isolator_->initialize()) {
            return false;
        }

        initialized_ = true;
        log_activity("SYSTEM", "Sandbox engine initialized successfully");

        return true;

    } catch (const std::exception& e) {
        log_activity("ERROR", "Failed to initialize sandbox: " + std::string(e.what()));
        return false;
    }
}

SandboxResult SandboxEngine::execute_file(const std::string& file_path,
                                          const std::vector<std::string>& args) {
    if (!initialized_) {
        return {false, -1, "", "Sandbox not initialized", std::chrono::milliseconds(0), {}, {}};
    }

    std::lock_guard<std::mutex> lock(state_mutex_);

    if (running_) {
        return {false, -1, "", "Sandbox already running", std::chrono::milliseconds(0), {}, {}};
    }

    running_ = true;
    stop_requested_ = false;
    start_time_ = std::chrono::steady_clock::now();

    log_activity("EXECUTION", "Starting execution of: " + file_path);

    // Setup isolation
    setup_isolation();

    SandboxResult result;

    try {
        // Execute based on sandbox type
        switch (config_.type) {
            case SandboxType::DOCKER_CONTAINER:
                result = execute_in_docker(file_path, args);
                break;
            case SandboxType::VIRTUAL_MACHINE:
                result = execute_in_vm(file_path, args);
                break;
            case SandboxType::ISOLATED_PROCESS:
            case SandboxType::HYBRID_ENVIRONMENT:
                result = execute_in_process(file_path, args);
                break;
        }

        // Calculate execution time
        auto end_time = std::chrono::steady_clock::now();
        result.execution_time = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time_);

        // Add metadata
        result.metadata["execution_id"] = generate_execution_id();
        result.metadata["sandbox_type"] = std::to_string(static_cast<int>(config_.type));
        result.metadata["network_isolated"] = config_.network_isolation ? "true" : "false";
        result.metadata["file_isolated"] = config_.file_isolation ? "true" : "false";
        result.metadata["memory_isolated"] = config_.memory_isolation ? "true" : "false";

        log_activity("EXECUTION", "Execution completed with exit code: " + std::to_string(result.exit_code));

    } catch (const std::exception& e) {
        result = {false, -1, "", "Execution failed: " + std::string(e.what()),
                 std::chrono::milliseconds(0), {}, {}};
        log_activity("ERROR", "Execution failed: " + std::string(e.what()));
    }

    // Cleanup
    cleanup_isolation();
    running_ = false;

    return result;
}

SandboxResult SandboxEngine::execute_command(const std::string& command,
                                            const std::vector<std::string>& args) {
    // For now, treat command execution similar to file execution
    return execute_file(command, args);
}

void SandboxEngine::stop_execution() {
    stop_requested_ = true;

    if (execution_thread_.joinable()) {
        execution_thread_.join();
    }

#ifdef _WIN32
    if (process_handle_ != INVALID_HANDLE_VALUE) {
        TerminateProcess(process_handle_, 1);
    }
#else
    if (child_pid_ > 0) {
        kill(child_pid_, SIGTERM);
        // Give it a chance to terminate gracefully
        sleep(1);
        kill(child_pid_, SIGKILL);
    }
#endif

    running_ = false;
    log_activity("SYSTEM", "Execution stopped");
}

bool SandboxEngine::is_running() const {
    return running_;
}

std::unordered_map<std::string, std::string> SandboxEngine::get_status() const {
    std::lock_guard<std::mutex> lock(state_mutex_);

    std::unordered_map<std::string, std::string> status;
    status["initialized"] = initialized_ ? "true" : "false";
    status["running"] = running_ ? "true" : "false";
    status["sandbox_type"] = std::to_string(static_cast<int>(config_.type));
    status["network_isolated"] = (config_.network_isolation && network_isolator_->is_isolated()) ? "true" : "false";
    status["file_isolated"] = (config_.file_isolation && fs_virtualizer_->is_virtualized()) ? "true" : "false";
    status["memory_isolated"] = (config_.memory_isolation && memory_isolator_->is_isolated()) ? "true" : "false";

    if (running_) {
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time_);
        status["elapsed_seconds"] = std::to_string(elapsed.count());
    }

    return status;
}

void SandboxEngine::set_timeout(std::chrono::seconds timeout) {
    config_.timeout = timeout;
}

void SandboxEngine::set_network_isolation(bool enabled) {
    config_.network_isolation = enabled;
}

void SandboxEngine::set_file_isolation(bool enabled) {
    config_.file_isolation = enabled;
}

void SandboxEngine::set_memory_isolation(bool enabled) {
    config_.memory_isolation = enabled;
}

std::vector<std::string> SandboxEngine::get_network_activity() const {
    std::lock_guard<std::mutex> lock(log_mutex_);
    return network_logs_;
}

std::vector<std::string> SandboxEngine::get_filesystem_activity() const {
    std::lock_guard<std::mutex> lock(log_mutex_);
    return filesystem_logs_;
}

std::vector<std::string> SandboxEngine::get_process_activity() const {
    std::lock_guard<std::mutex> lock(log_mutex_);
    return process_logs_;
}

std::unordered_map<std::string, uint64_t> SandboxEngine::get_memory_stats() const {
    if (memory_isolator_) {
        return memory_isolator_->get_memory_stats();
    }
    return {};
}

// Private methods
bool SandboxEngine::initialize_docker_sandbox() {
    // Check if Docker is available
    // This is a simplified implementation
    log_activity("SYSTEM", "Initializing Docker sandbox");

#ifdef _WIN32
    STARTUPINFOA si = {sizeof(si)};
    PROCESS_INFORMATION pi;

    if (CreateProcessA(nullptr, const_cast<char*>("docker --version"),
                      nullptr, nullptr, FALSE, CREATE_NO_WINDOW,
                      nullptr, nullptr, &si, &pi)) {
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        return true;
    }
#else
    int result = system("docker --version > /dev/null 2>&1");
    if (result == 0) {
        return true;
    }
#endif

    log_activity("WARNING", "Docker not available, falling back to process isolation");
    return false;
}

bool SandboxEngine::initialize_vm_sandbox() {
    // VM sandbox initialization would require integration with
    // virtualization platforms like VirtualBox, VMware, or Hyper-V
    log_activity("SYSTEM", "VM sandbox not implemented yet");
    return false;
}

bool SandboxEngine::initialize_process_sandbox() {
    log_activity("SYSTEM", "Initializing process sandbox");

#ifdef _WIN32
    // Create job object for process isolation
    job_object_ = CreateJobObjectA(nullptr, nullptr);
    if (job_object_ == nullptr) {
        log_activity("ERROR", "Failed to create job object");
        return false;
    }

    // Set job limits
    JOBOBJECT_EXTENDED_LIMIT_INFORMATION jeli = {0};
    jeli.BasicLimitInformation.LimitFlags = JOB_OBJECT_LIMIT_KILL_ON_JOB_CLOSE;

    if (!SetInformationJobObject(job_object_, JobObjectExtendedLimitInformation,
                                &jeli, sizeof(jeli))) {
        log_activity("ERROR", "Failed to set job object limits");
        return false;
    }
#else
    // Create pipe for communication
    if (pipe(pipe_fd_) == -1) {
        log_activity("ERROR", "Failed to create pipe");
        return false;
    }
#endif

    return true;
}

void SandboxEngine::setup_isolation() {
    if (config_.network_isolation && network_isolator_) {
        network_isolator_->enable_isolation();
    }

    if (config_.file_isolation && fs_virtualizer_) {
        fs_virtualizer_->enable_virtualization();
    }

    if (config_.memory_isolation && memory_isolator_) {
        memory_isolator_->enable_isolation();
    }
}

void SandboxEngine::cleanup_isolation() {
    if (network_isolator_) {
        network_isolator_->disable_isolation();
    }

    if (fs_virtualizer_) {
        fs_virtualizer_->disable_virtualization();
    }

    if (memory_isolator_) {
        memory_isolator_->disable_isolation();
    }
}

std::string SandboxEngine::generate_execution_id() const {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 15);

    std::stringstream ss;
    for (int i = 0; i < 16; ++i) {
        ss << std::hex << dis(gen);
    }

    return ss.str();
}

void SandboxEngine::log_activity(const std::string& category, const std::string& message) {
    std::lock_guard<std::mutex> lock(log_mutex_);

    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << " [" << category << "] " << message;

    std::string log_entry = ss.str();

    if (category == "NETWORK") {
        network_logs_.push_back(log_entry);
    } else if (category == "FILESYSTEM") {
        filesystem_logs_.push_back(log_entry);
    } else if (category == "PROCESS") {
        process_logs_.push_back(log_entry);
    }

    // Also log to console for debugging
    std::cout << log_entry << std::endl;
}

SandboxResult SandboxEngine::execute_in_docker(const std::string& file_path,
                                              const std::vector<std::string>& args) {
    log_activity("DOCKER", "Executing in Docker container: " + file_path);

    // Build Docker command
    std::stringstream cmd;
    cmd << "docker run --rm --network=none --memory=512m --cpus=1.0";
    cmd << " -v \"" << file_path << ":/analysis/target\"";
    cmd << " " << config_.container_image;
    cmd << " timeout " << config_.timeout.count() << " /analysis/target";

    for (const auto& arg : args) {
        cmd << " \"" << arg << "\"";
    }

    std::string command = cmd.str();
    log_activity("DOCKER", "Command: " + command);

#ifdef _WIN32
    STARTUPINFOA si = {sizeof(si)};
    PROCESS_INFORMATION pi;
    si.dwFlags = STARTF_USESTDHANDLES;

    // Create pipes for stdout/stderr
    HANDLE stdout_read, stdout_write;
    HANDLE stderr_read, stderr_write;

    SECURITY_ATTRIBUTES sa = {sizeof(sa), nullptr, TRUE};

    if (!CreatePipe(&stdout_read, &stdout_write, &sa, 0) ||
        !CreatePipe(&stderr_read, &stderr_write, &sa, 0)) {
        return {false, -1, "", "Failed to create pipes", std::chrono::milliseconds(0), {}, {}};
    }

    si.hStdOutput = stdout_write;
    si.hStdError = stderr_write;

    if (CreateProcessA(nullptr, const_cast<char*>(command.c_str()),
                      nullptr, nullptr, TRUE, CREATE_NO_WINDOW,
                      nullptr, nullptr, &si, &pi)) {

        CloseHandle(stdout_write);
        CloseHandle(stderr_write);

        // Wait for process completion
        WaitForSingleObject(pi.hProcess, static_cast<DWORD>(config_.timeout.count() * 1000));

        DWORD exit_code;
        GetExitCodeProcess(pi.hProcess, &exit_code);

        // Read output
        std::string stdout_output, stderr_output;
        char buffer[4096];
        DWORD bytes_read;

        while (ReadFile(stdout_read, buffer, sizeof(buffer), &bytes_read, nullptr) && bytes_read > 0) {
            stdout_output.append(buffer, bytes_read);
        }

        while (ReadFile(stderr_read, buffer, sizeof(buffer), &bytes_read, nullptr) && bytes_read > 0) {
            stderr_output.append(buffer, bytes_read);
        }

        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        CloseHandle(stdout_read);
        CloseHandle(stderr_read);

        return {true, static_cast<int>(exit_code), stdout_output, stderr_output,
                std::chrono::milliseconds(0), {}, {}};
    }
#else
    FILE* pipe = popen(command.c_str(), "r");
    if (!pipe) {
        return {false, -1, "", "Failed to execute Docker command", std::chrono::milliseconds(0), {}, {}};
    }

    std::string output;
    char buffer[4096];
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        output += buffer;
    }

    int exit_code = pclose(pipe);

    return {true, exit_code, output, "", std::chrono::milliseconds(0), {}, {}};
#endif

    return {false, -1, "", "Docker execution failed", std::chrono::milliseconds(0), {}, {}};
}

SandboxResult SandboxEngine::execute_in_vm(const std::string& file_path,
                                           const std::vector<std::string>& args) {
    // VM execution would require integration with virtualization APIs
    log_activity("VM", "VM execution not implemented yet");
    return {false, -1, "", "VM execution not implemented", std::chrono::milliseconds(0), {}, {}};
}

SandboxResult SandboxEngine::execute_in_process(const std::string& file_path,
                                               const std::vector<std::string>& args) {
    log_activity("PROCESS", "Executing in isolated process: " + file_path);

#ifdef _WIN32
    // Build command line
    std::stringstream cmd;
    cmd << "\"" << file_path << "\"";
    for (const auto& arg : args) {
        cmd << " \"" << arg << "\"";
    }

    std::string command = cmd.str();

    STARTUPINFOA si = {sizeof(si)};
    PROCESS_INFORMATION pi;
    si.dwFlags = STARTF_USESTDHANDLES;

    // Create pipes for stdout/stderr
    HANDLE stdout_read, stdout_write;
    HANDLE stderr_read, stderr_write;

    SECURITY_ATTRIBUTES sa = {sizeof(sa), nullptr, TRUE};

    if (!CreatePipe(&stdout_read, &stdout_write, &sa, 0) ||
        !CreatePipe(&stderr_read, &stderr_write, &sa, 0)) {
        return {false, -1, "", "Failed to create pipes", std::chrono::milliseconds(0), {}, {}};
    }

    si.hStdOutput = stdout_write;
    si.hStdError = stderr_write;

    if (CreateProcessA(nullptr, const_cast<char*>(command.c_str()),
                      nullptr, nullptr, TRUE, CREATE_SUSPENDED,
                      nullptr, nullptr, &si, &pi)) {

        process_handle_ = pi.hProcess;

        // Assign to job object for isolation
        if (job_object_ != INVALID_HANDLE_VALUE) {
            AssignProcessToJobObject(job_object_, pi.hProcess);
        }

        // Resume the process
        ResumeThread(pi.hThread);
        CloseHandle(pi.hThread);

        CloseHandle(stdout_write);
        CloseHandle(stderr_write);

        // Wait for process completion with timeout
        DWORD wait_result = WaitForSingleObject(pi.hProcess,
                                               static_cast<DWORD>(config_.timeout.count() * 1000));

        DWORD exit_code = 0;
        if (wait_result == WAIT_TIMEOUT) {
            TerminateProcess(pi.hProcess, 1);
            exit_code = 1;
        } else {
            GetExitCodeProcess(pi.hProcess, &exit_code);
        }

        // Read output
        std::string stdout_output, stderr_output;
        char buffer[4096];
        DWORD bytes_read;

        while (ReadFile(stdout_read, buffer, sizeof(buffer), &bytes_read, nullptr) && bytes_read > 0) {
            stdout_output.append(buffer, bytes_read);
        }

        while (ReadFile(stderr_read, buffer, sizeof(buffer), &bytes_read, nullptr) && bytes_read > 0) {
            stderr_output.append(buffer, bytes_read);
        }

        CloseHandle(stdout_read);
        CloseHandle(stderr_read);

        return {true, static_cast<int>(exit_code), stdout_output, stderr_output,
                std::chrono::milliseconds(0), {}, {}};
    }
#else
    // Linux implementation using fork/exec
    if (pipe(pipe_fd_) == -1) {
        return {false, -1, "", "Failed to create pipe", std::chrono::milliseconds(0), {}, {}};
    }

    child_pid_ = fork();

    if (child_pid_ == 0) {
        // Child process
        close(pipe_fd_[0]); // Close read end

        // Redirect stdout/stderr to pipe
        dup2(pipe_fd_[1], STDOUT_FILENO);
        dup2(pipe_fd_[1], STDERR_FILENO);
        close(pipe_fd_[1]);

        // Set resource limits
        struct rlimit limit;
        limit.rlim_cur = 512 * 1024 * 1024; // 512MB
        limit.rlim_max = 512 * 1024 * 1024;
        setrlimit(RLIMIT_AS, &limit);

        // Execute the file
        std::vector<char*> argv_c;
        argv_c.push_back(const_cast<char*>(file_path.c_str()));
        for (const auto& arg : args) {
            argv_c.push_back(const_cast<char*>(arg.c_str()));
        }
        argv_c.push_back(nullptr);

        execv(file_path.c_str(), argv_c.data());

        // If we get here, exec failed
        exit(1);
    } else if (child_pid_ > 0) {
        // Parent process
        close(pipe_fd_[1]); // Close write end

        // Wait for child with timeout
        int status;
        int wait_result = waitpid(child_pid_, &status, WNOHANG);

        auto start = std::chrono::steady_clock::now();
        while (wait_result == 0) {
            auto elapsed = std::chrono::steady_clock::now() - start;
            if (elapsed >= config_.timeout) {
                kill(child_pid_, SIGKILL);
                waitpid(child_pid_, &status, 0);
                break;
            }

            usleep(100000); // 100ms
            wait_result = waitpid(child_pid_, &status, WNOHANG);
        }

        // Read output
        std::string output;
        char buffer[4096];
        ssize_t bytes_read;

        while ((bytes_read = read(pipe_fd_[0], buffer, sizeof(buffer))) > 0) {
            output.append(buffer, bytes_read);
        }

        close(pipe_fd_[0]);

        int exit_code = WIFEXITED(status) ? WEXITSTATUS(status) : -1;

        return {true, exit_code, output, "", std::chrono::milliseconds(0), {}, {}};
    }
#endif

    return {false, -1, "", "Process execution failed", std::chrono::milliseconds(0), {}, {}};
}

} // namespace sandbox
} // namespace sbards
