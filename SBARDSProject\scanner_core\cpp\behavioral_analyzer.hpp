/**
 * SBARDS Advanced Behavioral Analyzer
 * Comprehensive behavioral analysis with resource monitoring and pattern detection
 */

#ifndef SBARDS_BEHAVIORAL_ANALYZER_HPP
#define SBARDS_BEHAVIORAL_ANALYZER_HPP

#include <memory>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <string>
#include <chrono>
#include <atomic>
#include <mutex>
#include <thread>
#include <functional>
#include <deque>

#ifdef _WIN32
    #include <windows.h>
    #include <psapi.h>
    #include <tlhelp32.h>
    #include <pdh.h>
    #include <pdhmsg.h>
    #pragma comment(lib, "pdh.lib")
    #pragma comment(lib, "psapi.lib")
#else
    #include <sys/resource.h>
    #include <sys/types.h>
    #include <sys/stat.h>
    #include <unistd.h>
    #include <dirent.h>
    #include <proc/readproc.h>
#endif

namespace sbards {
namespace behavioral {

/**
 * Resource usage statistics
 */
struct ResourceUsage {
    std::chrono::system_clock::time_point timestamp;
    uint32_t process_id;
    std::string process_name;
    
    // CPU metrics
    double cpu_usage_percent;
    uint64_t cpu_time_user;
    uint64_t cpu_time_kernel;
    uint32_t thread_count;
    
    // Memory metrics
    uint64_t memory_working_set;
    uint64_t memory_private_bytes;
    uint64_t memory_virtual_size;
    uint64_t memory_peak_working_set;
    bool memory_leak_detected;
    
    // I/O metrics
    uint64_t io_read_bytes;
    uint64_t io_write_bytes;
    uint64_t io_read_operations;
    uint64_t io_write_operations;
    double io_read_rate_mbps;
    double io_write_rate_mbps;
    
    // Network metrics
    uint64_t network_bytes_sent;
    uint64_t network_bytes_received;
    uint32_t network_connections_active;
    uint32_t network_connections_total;
    double network_send_rate_mbps;
    double network_receive_rate_mbps;
    std::vector<std::string> network_destinations;
    
    // Handle metrics
    uint32_t handle_count;
    uint32_t gdi_objects;
    uint32_t user_objects;
};

/**
 * Behavioral pattern information
 */
struct BehavioralPattern {
    std::string pattern_type;
    std::string description;
    double confidence_score;
    std::chrono::system_clock::time_point first_detected;
    std::chrono::system_clock::time_point last_detected;
    uint32_t occurrence_count;
    std::vector<std::string> indicators;
    std::unordered_map<std::string, std::string> metadata;
    
    // Pattern-specific data
    struct {
        // File encryption patterns
        uint32_t files_encrypted;
        std::vector<std::string> encryption_extensions;
        double encryption_rate_files_per_second;
        
        // File access patterns
        std::string access_pattern_type; // sequential, random, bulk
        uint32_t files_accessed;
        double access_rate_files_per_second;
        
        // Shadow copy patterns
        uint32_t shadow_copies_deleted;
        std::vector<std::string> shadow_copy_commands;
        
        // Security tool interference
        std::vector<std::string> disabled_security_tools;
        std::vector<std::string> security_processes_terminated;
        
        // Process patterns
        uint32_t child_processes_created;
        std::vector<uint32_t> process_chain;
        std::vector<std::string> injection_techniques;
        
        // Service patterns
        std::vector<std::string> services_installed;
        std::vector<std::string> services_started;
        std::vector<std::string> services_modified;
    } pattern_data;
};

/**
 * Process information
 */
struct ProcessInfo {
    uint32_t process_id;
    uint32_t parent_process_id;
    std::string process_name;
    std::string executable_path;
    std::string command_line;
    std::chrono::system_clock::time_point creation_time;
    std::vector<uint32_t> child_processes;
    std::vector<std::string> loaded_modules;
    bool is_injected;
    bool is_suspicious;
    std::vector<std::string> injection_indicators;
};

/**
 * Advanced Behavioral Analyzer
 */
class BehavioralAnalyzer {
public:
    explicit BehavioralAnalyzer();
    ~BehavioralAnalyzer();
    
    // Core functionality
    bool start_monitoring(uint32_t target_process_id = 0);
    void stop_monitoring();
    bool is_monitoring() const;
    
    // Resource monitoring
    std::vector<ResourceUsage> get_resource_usage_history() const;
    ResourceUsage get_current_resource_usage(uint32_t process_id) const;
    void set_resource_callback(std::function<void(const ResourceUsage&)> callback);
    
    // Pattern detection
    std::vector<BehavioralPattern> get_detected_patterns() const;
    void set_pattern_callback(std::function<void(const BehavioralPattern&)> callback);
    
    // Process analysis
    std::vector<ProcessInfo> get_process_tree() const;
    ProcessInfo get_process_info(uint32_t process_id) const;
    std::vector<uint32_t> get_suspicious_processes() const;
    
    // Configuration
    void set_monitoring_interval(std::chrono::milliseconds interval);
    void set_resource_thresholds(const std::unordered_map<std::string, double>& thresholds);
    void enable_pattern_detection(const std::string& pattern_type, bool enabled);
    
    // Analysis methods
    bool detect_file_encryption_pattern(uint32_t process_id);
    bool detect_sequential_file_access(uint32_t process_id);
    bool detect_shadow_copy_deletion(uint32_t process_id);
    bool detect_security_tool_interference(uint32_t process_id);
    bool detect_process_injection(uint32_t process_id);
    bool detect_suspicious_service_activity(uint32_t process_id);
    
    // Resource analysis
    bool detect_memory_leak(uint32_t process_id);
    bool detect_cpu_usage_anomaly(uint32_t process_id);
    bool detect_io_anomaly(uint32_t process_id);
    bool detect_network_anomaly(uint32_t process_id);
    
private:
    // Monitoring state
    std::atomic<bool> monitoring_;
    std::chrono::milliseconds monitoring_interval_;
    uint32_t target_process_id_;
    
    // Threading
    std::vector<std::thread> monitoring_threads_;
    mutable std::mutex data_mutex_;
    
    // Data storage
    std::deque<ResourceUsage> resource_history_;
    std::vector<BehavioralPattern> detected_patterns_;
    std::unordered_map<uint32_t, ProcessInfo> process_map_;
    std::unordered_set<uint32_t> suspicious_processes_;
    
    // Callbacks
    std::function<void(const ResourceUsage&)> resource_callback_;
    std::function<void(const BehavioralPattern&)> pattern_callback_;
    
    // Configuration
    std::unordered_map<std::string, double> resource_thresholds_;
    std::unordered_map<std::string, bool> pattern_detection_enabled_;
    
    // Platform-specific handles
#ifdef _WIN32
    std::unordered_map<uint32_t, HANDLE> process_handles_;
    std::unordered_map<std::string, PDH_HQUERY> pdh_queries_;
    std::unordered_map<std::string, PDH_HCOUNTER> pdh_counters_;
#endif
    
    // Monitoring methods
    void resource_monitoring_loop();
    void pattern_detection_loop();
    void process_monitoring_loop();
    
    // Resource collection
    ResourceUsage collect_process_resources(uint32_t process_id);
    void collect_cpu_metrics(uint32_t process_id, ResourceUsage& usage);
    void collect_memory_metrics(uint32_t process_id, ResourceUsage& usage);
    void collect_io_metrics(uint32_t process_id, ResourceUsage& usage);
    void collect_network_metrics(uint32_t process_id, ResourceUsage& usage);
    
    // Pattern detection methods
    void analyze_file_encryption_patterns();
    void analyze_file_access_patterns();
    void analyze_shadow_copy_operations();
    void analyze_security_tool_interference();
    void analyze_process_creation_patterns();
    void analyze_service_operations();
    
    // Process analysis methods
    void update_process_tree();
    void detect_process_injection_techniques(uint32_t process_id);
    void analyze_process_chain(uint32_t process_id);
    
    // Utility methods
    bool is_process_running(uint32_t process_id);
    std::string get_process_name(uint32_t process_id);
    std::string get_process_path(uint32_t process_id);
    std::string get_process_command_line(uint32_t process_id);
    std::vector<uint32_t> get_child_processes(uint32_t parent_process_id);
    std::vector<std::string> get_loaded_modules(uint32_t process_id);
    
    // Threshold checking
    bool exceeds_cpu_threshold(const ResourceUsage& usage);
    bool exceeds_memory_threshold(const ResourceUsage& usage);
    bool exceeds_io_threshold(const ResourceUsage& usage);
    bool exceeds_network_threshold(const ResourceUsage& usage);
    
    // Pattern creation
    BehavioralPattern create_pattern(const std::string& type, const std::string& description,
                                   double confidence, const std::vector<std::string>& indicators);
    void update_pattern_occurrence(BehavioralPattern& pattern);
    
    // Platform-specific implementations
#ifdef _WIN32
    void init_windows_monitoring();
    void cleanup_windows_monitoring();
    ResourceUsage collect_windows_process_resources(uint32_t process_id);
    std::vector<uint32_t> enumerate_windows_processes();
    bool detect_windows_process_injection(uint32_t process_id);
#else
    void init_linux_monitoring();
    void cleanup_linux_monitoring();
    ResourceUsage collect_linux_process_resources(uint32_t process_id);
    std::vector<uint32_t> enumerate_linux_processes();
    bool detect_linux_process_injection(uint32_t process_id);
#endif
};

/**
 * Behavioral Analysis Configuration
 */
struct BehavioralConfig {
    // Monitoring settings
    std::chrono::milliseconds monitoring_interval{1000};
    uint32_t max_history_entries{10000};
    bool enable_real_time_analysis{true};
    
    // Resource thresholds
    double cpu_usage_threshold{80.0};
    double memory_usage_threshold_mb{2048.0};
    double io_rate_threshold_mbps{100.0};
    double network_rate_threshold_mbps{50.0};
    
    // Pattern detection settings
    bool detect_file_encryption{true};
    bool detect_file_access_patterns{true};
    bool detect_shadow_copy_deletion{true};
    bool detect_security_interference{true};
    bool detect_process_injection{true};
    bool detect_service_manipulation{true};
    
    // File encryption detection
    uint32_t encryption_file_threshold{10};
    double encryption_rate_threshold{5.0}; // files per second
    std::vector<std::string> encryption_extensions{".encrypted", ".locked", ".crypto"};
    
    // Process monitoring
    bool monitor_child_processes{true};
    bool monitor_process_chains{true};
    uint32_t max_process_chain_depth{10};
    
    // Memory leak detection
    double memory_leak_threshold_mb{100.0};
    std::chrono::minutes memory_leak_timeframe{5};
    
    // Network anomaly detection
    uint32_t network_connection_threshold{50};
    double network_data_threshold_mb{1000.0};
};

} // namespace behavioral
} // namespace sbards

#endif // SBARDS_BEHAVIORAL_ANALYZER_HPP
