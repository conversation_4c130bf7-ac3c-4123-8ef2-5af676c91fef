"""
SBARDS C++ Bridge
Python interface to high-performance C++ dynamic analysis components

Features:
- Sandbox engine integration
- API hooking framework interface
- Memory analyzer integration
- Performance monitoring bridge
- Asynchronous operation support
"""

import os
import asyncio
import logging
import json
import ctypes
import tempfile
import threading
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
import subprocess
import platform

class CPPBridge:
    """
    C++ Bridge for Dynamic Analysis Components
    
    Provides Python interface to high-performance C++ components:
    - Sandbox Engine
    - API Hooking Framework
    - Memory Analyzer
    - Performance Monitor
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize C++ Bridge
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.CPPBridge")
        
        # C++ components configuration
        self.cpp_config = config.get("dynamic_analysis", {}).get("cpp_components", {})
        self.enabled = self.cpp_config.get("enabled", True)
        
        if not self.enabled:
            self.logger.info("C++ components disabled")
            return
        
        # Component paths
        self.sandbox_engine_path = self.cpp_config.get("sandbox_engine", "scanner_core/cpp/sandbox_engine")
        self.api_hooking_path = self.cpp_config.get("api_hooking", "scanner_core/cpp/api_hooking")
        self.memory_analyzer_path = self.cpp_config.get("memory_analyzer", "scanner_core/cpp/memory_analyzer")
        self.performance_monitor_path = self.cpp_config.get("performance_monitor", "scanner_core/cpp/performance_monitor")
        
        # Platform detection
        self.platform = platform.system()
        self.library_extension = self._get_library_extension()
        
        # Initialize components
        self.sandbox_engine = None
        self.api_hooking = None
        self.memory_analyzer = None
        self.performance_monitor = None
        
        self._init_cpp_components()
        
        # Execution state
        self.active_processes: Dict[str, subprocess.Popen] = {}
        self.process_lock = threading.Lock()
    
    def _get_library_extension(self) -> str:
        """Get platform-specific library extension"""
        if self.platform == "Windows":
            return ".dll"
        elif self.platform == "Darwin":
            return ".dylib"
        else:
            return ".so"
    
    def _init_cpp_components(self):
        """Initialize C++ components"""
        try:
            # Check if C++ components are built
            if not self._check_cpp_components():
                self.logger.warning("C++ components not found, attempting to build...")
                if not self._build_cpp_components():
                    self.logger.error("Failed to build C++ components")
                    self.enabled = False
                    return
            
            # Load shared libraries if available
            self._load_shared_libraries()
            
            self.logger.info("C++ components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize C++ components: {e}")
            self.enabled = False
    
    def _check_cpp_components(self) -> bool:
        """Check if C++ components are built"""
        try:
            # Check for main dynamic analyzer executable
            exe_name = "sbards_dynamic_analyzer"
            if self.platform == "Windows":
                exe_name += ".exe"
            
            exe_path = Path("scanner_core/cpp/build/bin") / exe_name
            if exe_path.exists():
                return True
            
            # Check alternative paths
            alt_paths = [
                Path("scanner_core/cpp") / exe_name,
                Path("build/bin") / exe_name,
                Path("bin") / exe_name
            ]
            
            for path in alt_paths:
                if path.exists():
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking C++ components: {e}")
            return False
    
    def _build_cpp_components(self) -> bool:
        """Build C++ components using CMake"""
        try:
            cpp_dir = Path("scanner_core/cpp")
            build_dir = cpp_dir / "build"
            
            # Create build directory
            build_dir.mkdir(exist_ok=True)
            
            # Run CMake configuration
            cmake_cmd = [
                "cmake",
                "-S", str(cpp_dir),
                "-B", str(build_dir),
                "-DCMAKE_BUILD_TYPE=Release"
            ]
            
            self.logger.info("Configuring C++ build with CMake...")
            result = subprocess.run(cmake_cmd, capture_output=True, text=True, cwd=cpp_dir)
            
            if result.returncode != 0:
                self.logger.error(f"CMake configuration failed: {result.stderr}")
                return False
            
            # Build the project
            build_cmd = ["cmake", "--build", str(build_dir), "--config", "Release"]
            
            self.logger.info("Building C++ components...")
            result = subprocess.run(build_cmd, capture_output=True, text=True, cwd=cpp_dir)
            
            if result.returncode != 0:
                self.logger.error(f"C++ build failed: {result.stderr}")
                return False
            
            self.logger.info("C++ components built successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to build C++ components: {e}")
            return False
    
    def _load_shared_libraries(self):
        """Load shared libraries for direct C++ integration"""
        try:
            # Try to load the Python bridge library
            bridge_lib_name = f"sbards_python_bridge{self.library_extension}"
            bridge_paths = [
                Path("scanner_core/cpp/build/lib") / bridge_lib_name,
                Path("scanner_core/cpp/lib") / bridge_lib_name,
                Path("lib") / bridge_lib_name
            ]
            
            for path in bridge_paths:
                if path.exists():
                    try:
                        self.bridge_lib = ctypes.CDLL(str(path))
                        self.logger.info(f"Loaded C++ bridge library: {path}")
                        self._setup_bridge_functions()
                        return
                    except Exception as e:
                        self.logger.warning(f"Failed to load {path}: {e}")
            
            self.logger.warning("C++ bridge library not found, using subprocess interface")
            
        except Exception as e:
            self.logger.error(f"Failed to load shared libraries: {e}")
    
    def _setup_bridge_functions(self):
        """Setup C++ bridge function signatures"""
        try:
            if hasattr(self, 'bridge_lib'):
                # Define function signatures
                # Example: analyze_memory function
                self.bridge_lib.analyze_memory.argtypes = [ctypes.c_char_p, ctypes.c_int]
                self.bridge_lib.analyze_memory.restype = ctypes.c_char_p
                
                # Example: hook_api_calls function
                self.bridge_lib.hook_api_calls.argtypes = [ctypes.c_char_p, ctypes.c_int]
                self.bridge_lib.hook_api_calls.restype = ctypes.c_char_p
                
                self.logger.info("C++ bridge functions configured")
                
        except Exception as e:
            self.logger.error(f"Failed to setup bridge functions: {e}")
    
    async def analyze_memory_async(self, file_path: str, timeout: int = 300) -> Dict[str, Any]:
        """
        Perform memory analysis using C++ memory analyzer
        
        Args:
            file_path: Path to file to analyze
            timeout: Analysis timeout in seconds
            
        Returns:
            Memory analysis results
        """
        if not self.enabled:
            return {
                "success": False,
                "error": "C++ components not available",
                "timestamp": datetime.now().isoformat()
            }
        
        try:
            self.logger.info(f"Starting C++ memory analysis for: {file_path}")
            
            # Use direct library call if available
            if hasattr(self, 'bridge_lib') and hasattr(self.bridge_lib, 'analyze_memory'):
                return await self._analyze_memory_direct(file_path, timeout)
            else:
                return await self._analyze_memory_subprocess(file_path, timeout)
                
        except Exception as e:
            self.logger.error(f"Memory analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _analyze_memory_direct(self, file_path: str, timeout: int) -> Dict[str, Any]:
        """Direct memory analysis using loaded library"""
        try:
            # Call C++ function directly
            file_path_bytes = file_path.encode('utf-8')
            result_json = self.bridge_lib.analyze_memory(file_path_bytes, timeout)
            
            if result_json:
                result = json.loads(result_json.decode('utf-8'))
                result["method"] = "direct_library"
                return result
            else:
                return {
                    "success": False,
                    "error": "No result from C++ memory analyzer",
                    "method": "direct_library"
                }
                
        except Exception as e:
            self.logger.error(f"Direct memory analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "method": "direct_library"
            }
    
    async def _analyze_memory_subprocess(self, file_path: str, timeout: int) -> Dict[str, Any]:
        """Memory analysis using subprocess"""
        try:
            # Find memory analyzer executable
            exe_name = "sbards_memory_analyzer"
            if self.platform == "Windows":
                exe_name += ".exe"
            
            exe_paths = [
                Path("scanner_core/cpp/build/bin") / exe_name,
                Path("scanner_core/cpp") / exe_name,
                Path("bin") / exe_name
            ]
            
            exe_path = None
            for path in exe_paths:
                if path.exists():
                    exe_path = path
                    break
            
            if not exe_path:
                return {
                    "success": False,
                    "error": "Memory analyzer executable not found",
                    "method": "subprocess"
                }
            
            # Create temporary output file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                output_file = temp_file.name
            
            # Build command
            cmd = [
                str(exe_path),
                "--file", file_path,
                "--timeout", str(timeout),
                "--output", output_file,
                "--format", "json"
            ]
            
            # Execute memory analyzer
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout + 30
                )
                
                # Read results
                if os.path.exists(output_file):
                    with open(output_file, 'r') as f:
                        result = json.load(f)
                    os.unlink(output_file)
                    
                    result["method"] = "subprocess"
                    result["stdout"] = stdout.decode('utf-8') if stdout else ""
                    result["stderr"] = stderr.decode('utf-8') if stderr else ""
                    result["exit_code"] = process.returncode
                    
                    return result
                else:
                    return {
                        "success": False,
                        "error": "No output file generated",
                        "method": "subprocess",
                        "exit_code": process.returncode
                    }
                    
            except asyncio.TimeoutError:
                process.kill()
                return {
                    "success": False,
                    "error": "Memory analysis timeout",
                    "method": "subprocess"
                }
            
        except Exception as e:
            self.logger.error(f"Subprocess memory analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "method": "subprocess"
            }
    
    async def monitor_api_calls_async(self, file_path: str, timeout: int = 300) -> Dict[str, Any]:
        """
        Monitor API calls using C++ API hooking framework
        
        Args:
            file_path: Path to file to analyze
            timeout: Monitoring timeout in seconds
            
        Returns:
            API monitoring results
        """
        if not self.enabled:
            return {
                "success": False,
                "error": "C++ components not available",
                "timestamp": datetime.now().isoformat()
            }
        
        try:
            self.logger.info(f"Starting C++ API monitoring for: {file_path}")
            
            # Use direct library call if available
            if hasattr(self, 'bridge_lib') and hasattr(self.bridge_lib, 'hook_api_calls'):
                return await self._monitor_api_direct(file_path, timeout)
            else:
                return await self._monitor_api_subprocess(file_path, timeout)
                
        except Exception as e:
            self.logger.error(f"API monitoring failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _monitor_api_direct(self, file_path: str, timeout: int) -> Dict[str, Any]:
        """Direct API monitoring using loaded library"""
        try:
            file_path_bytes = file_path.encode('utf-8')
            result_json = self.bridge_lib.hook_api_calls(file_path_bytes, timeout)
            
            if result_json:
                result = json.loads(result_json.decode('utf-8'))
                result["method"] = "direct_library"
                return result
            else:
                return {
                    "success": False,
                    "error": "No result from C++ API hooking",
                    "method": "direct_library"
                }
                
        except Exception as e:
            self.logger.error(f"Direct API monitoring failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "method": "direct_library"
            }
    
    async def _monitor_api_subprocess(self, file_path: str, timeout: int) -> Dict[str, Any]:
        """API monitoring using subprocess"""
        try:
            # Find API hooking executable
            exe_name = "sbards_api_hooking"
            if self.platform == "Windows":
                exe_name += ".exe"
            
            exe_paths = [
                Path("scanner_core/cpp/build/bin") / exe_name,
                Path("scanner_core/cpp") / exe_name,
                Path("bin") / exe_name
            ]
            
            exe_path = None
            for path in exe_paths:
                if path.exists():
                    exe_path = path
                    break
            
            if not exe_path:
                return {
                    "success": False,
                    "error": "API hooking executable not found",
                    "method": "subprocess"
                }
            
            # Create temporary output file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                output_file = temp_file.name
            
            # Build command
            cmd = [
                str(exe_path),
                "--file", file_path,
                "--timeout", str(timeout),
                "--output", output_file,
                "--format", "json",
                "--monitor-syscalls",
                "--monitor-winapi"
            ]
            
            # Execute API hooking
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout + 30
                )
                
                # Read results
                if os.path.exists(output_file):
                    with open(output_file, 'r') as f:
                        result = json.load(f)
                    os.unlink(output_file)
                    
                    result["method"] = "subprocess"
                    result["stdout"] = stdout.decode('utf-8') if stdout else ""
                    result["stderr"] = stderr.decode('utf-8') if stderr else ""
                    result["exit_code"] = process.returncode
                    
                    return result
                else:
                    return {
                        "success": False,
                        "error": "No output file generated",
                        "method": "subprocess",
                        "exit_code": process.returncode
                    }
                    
            except asyncio.TimeoutError:
                process.kill()
                return {
                    "success": False,
                    "error": "API monitoring timeout",
                    "method": "subprocess"
                }
            
        except Exception as e:
            self.logger.error(f"Subprocess API monitoring failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "method": "subprocess"
            }
    
    def is_available(self) -> bool:
        """Check if C++ components are available"""
        return self.enabled
    
    def get_component_status(self) -> Dict[str, Any]:
        """Get status of C++ components"""
        return {
            "enabled": self.enabled,
            "platform": self.platform,
            "bridge_library_loaded": hasattr(self, 'bridge_lib'),
            "sandbox_engine_available": self._check_component_executable("sbards_sandbox_engine"),
            "api_hooking_available": self._check_component_executable("sbards_api_hooking"),
            "memory_analyzer_available": self._check_component_executable("sbards_memory_analyzer"),
            "performance_monitor_available": self._check_component_executable("sbards_performance_monitor")
        }
    
    def _check_component_executable(self, exe_name: str) -> bool:
        """Check if a component executable exists"""
        if self.platform == "Windows":
            exe_name += ".exe"
        
        exe_paths = [
            Path("scanner_core/cpp/build/bin") / exe_name,
            Path("scanner_core/cpp") / exe_name,
            Path("bin") / exe_name
        ]
        
        return any(path.exists() for path in exe_paths)
