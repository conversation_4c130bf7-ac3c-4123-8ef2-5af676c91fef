#!/usr/bin/env python3
"""
SBARDS Response System - Python Binding Layer
MINIMAL Python interface to C++ Response Engine

This module provides ONLY:
1. C++ Engine Interface (ctypes bindings)
2. Configuration Management
3. Integration with other SBARDS layers
4. Basic error handling

ALL CORE LOGIC IS IMPLEMENTED IN C++
Python is used ONLY for bindings and integration
"""

import os
import sys
import json
import logging
import ctypes
import platform
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

# Platform detection
PLATFORM = platform.system().lower()
ARCHITECTURE = platform.machine().lower()

# C++ Engine availability
CPP_ENGINE_AVAILABLE = False
CPP_LIBRARY_PATH = None

class CPPEngineInterface:
    """Minimal interface to C++ Response Engine."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize C++ engine interface."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.CPPEngine")
        
        # C++ library handle
        self.cpp_lib = None
        self.engine_handle = None
        
        # Initialize C++ engine
        self._load_cpp_engine()
    
    def _load_cpp_engine(self):
        """Load C++ response engine."""
        try:
            # Find and load C++ library
            lib_path = self._find_cpp_library()
            if lib_path:
                self.cpp_lib = ctypes.CDLL(str(lib_path))
                self._setup_function_signatures()
                
                # Create engine instance
                config_json = json.dumps(self._convert_config_to_cpp())
                self.engine_handle = self.cpp_lib.CreateResponseEngine(config_json.encode('utf-8'))
                
                if self.engine_handle:
                    if self.cpp_lib.InitializeResponseEngine(self.engine_handle):
                        global CPP_ENGINE_AVAILABLE
                        CPP_ENGINE_AVAILABLE = True
                        self.logger.info("C++ Response Engine loaded successfully")
                    else:
                        self.logger.error("Failed to initialize C++ Response Engine")
                        self._cleanup()
                else:
                    self.logger.error("Failed to create C++ Response Engine")
            else:
                self.logger.warning("C++ Response Engine library not found")
                
        except Exception as e:
            self.logger.error(f"C++ engine loading failed: {e}")
            self._cleanup()
    
    def _find_cpp_library(self) -> Optional[Path]:
        """Find C++ response engine library."""
        base_path = Path(__file__).parent / "cpp_core"
        
        if PLATFORM == "windows":
            extensions = [".dll"]
        elif PLATFORM == "darwin":
            extensions = [".dylib", ".so"]
        else:
            extensions = [".so"]
        
        # Search for library
        for ext in extensions:
            for lib_name in ["libsbards_response_engine", "sbards_response_engine"]:
                # Check build directories
                for build_dir in ["build", "build/Release", "build/Debug", "Release", "Debug", ""]:
                    lib_path = base_path / build_dir / f"{lib_name}{ext}"
                    if lib_path.exists():
                        global CPP_LIBRARY_PATH
                        CPP_LIBRARY_PATH = str(lib_path)
                        return lib_path
        
        return None
    
    def _setup_function_signatures(self):
        """Setup C++ function signatures."""
        try:
            # Engine management
            self.cpp_lib.CreateResponseEngine.restype = ctypes.c_void_p
            self.cpp_lib.CreateResponseEngine.argtypes = [ctypes.c_char_p]
            
            self.cpp_lib.DestroyResponseEngine.restype = None
            self.cpp_lib.DestroyResponseEngine.argtypes = [ctypes.c_void_p]
            
            self.cpp_lib.InitializeResponseEngine.restype = ctypes.c_bool
            self.cpp_lib.InitializeResponseEngine.argtypes = [ctypes.c_void_p]
            
            self.cpp_lib.ShutdownResponseEngine.restype = None
            self.cpp_lib.ShutdownResponseEngine.argtypes = [ctypes.c_void_p]
            
            # Core operations
            self.cpp_lib.ProcessThreat.restype = ctypes.c_char_p
            self.cpp_lib.ProcessThreat.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            self.cpp_lib.QuarantineFile.restype = ctypes.c_char_p
            self.cpp_lib.QuarantineFile.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            self.cpp_lib.IsolateInHoneypot.restype = ctypes.c_char_p
            self.cpp_lib.IsolateInHoneypot.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            self.cpp_lib.SendAlerts.restype = ctypes.c_char_p
            self.cpp_lib.SendAlerts.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            # Management operations
            self.cpp_lib.GetPerformanceMetrics.restype = ctypes.c_char_p
            self.cpp_lib.GetPerformanceMetrics.argtypes = [ctypes.c_void_p]
            
            self.cpp_lib.GetOperationHistory.restype = ctypes.c_char_p
            self.cpp_lib.GetOperationHistory.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            self.cpp_lib.GetEngineStatus.restype = ctypes.c_char_p
            self.cpp_lib.GetEngineStatus.argtypes = [ctypes.c_void_p]
            
            # Memory management
            self.cpp_lib.FreeMemory.restype = None
            self.cpp_lib.FreeMemory.argtypes = [ctypes.c_char_p]
            
        except Exception as e:
            self.logger.error(f"Error setting up function signatures: {e}")
            raise
    
    def _convert_config_to_cpp(self) -> Dict[str, Any]:
        """Convert Python config to C++ format."""
        response_config = self.config.get("response", {})
        
        return {
            "security_level": 1,  # ENHANCED
            "base_directory": response_config.get("base_directory", "response_data"),
            "quarantine_directory": response_config.get("quarantine_directory", "response_data/quarantine"),
            "honeypot_directory": response_config.get("honeypot_directory", "response_data/honeypot"),
            "forensics_directory": response_config.get("forensics_directory", "response_data/forensics"),
            "backup_directory": response_config.get("backup_directory", "response_data/backup"),
            "logs_directory": response_config.get("logs_directory", "response_data/logs"),
            "encryption_enabled": response_config.get("encryption_enabled", True),
            "compression_enabled": response_config.get("compression_enabled", False),
            "forensic_mode": response_config.get("forensic_mode", True),
            "real_time_monitoring": response_config.get("real_time_monitoring", True),
            "max_concurrent_operations": response_config.get("max_concurrent_operations", 10),
            "operation_timeout_seconds": response_config.get("operation_timeout_seconds", 300)
        }
    
    def _cleanup(self):
        """Cleanup C++ resources."""
        try:
            if self.engine_handle and self.cpp_lib:
                self.cpp_lib.ShutdownResponseEngine(self.engine_handle)
                self.cpp_lib.DestroyResponseEngine(self.engine_handle)
                self.engine_handle = None
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def __del__(self):
        """Destructor."""
        self._cleanup()

class ResponseSystem:
    """
    SBARDS Response System - Python Binding Layer
    
    This class provides MINIMAL Python interface to C++ Response Engine.
    ALL CORE LOGIC IS IMPLEMENTED IN C++.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Response System."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.ResponseSystem")
        
        # Initialize C++ engine interface
        self.cpp_interface = CPPEngineInterface(config)
        
        # Basic metrics (C++ engine provides detailed metrics)
        self.operation_count = 0
        self.start_time = datetime.now()
        
        self.logger.info("Response System initialized (C++ core engine)")
    
    def process_analysis_results(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process analysis results using C++ engine.
        
        Args:
            analysis_results: Analysis results from threat detection
            
        Returns:
            Response execution results
        """
        try:
            self.operation_count += 1
            self.logger.info(f"Processing analysis results (operation #{self.operation_count})")
            
            # Check if C++ engine is available
            if not CPP_ENGINE_AVAILABLE or not self.cpp_interface.engine_handle:
                return self._fallback_response(analysis_results)
            
            # Convert to threat assessment format
            threat_assessment = self._convert_to_threat_assessment(analysis_results)
            
            # Call C++ engine
            threat_json = json.dumps(threat_assessment)
            result_ptr = self.cpp_interface.cpp_lib.ProcessThreat(
                self.cpp_interface.engine_handle,
                threat_json.encode('utf-8')
            )
            
            if not result_ptr:
                return self._fallback_response(analysis_results)
            
            # Convert result back to Python
            result_json = ctypes.string_at(result_ptr).decode('utf-8')
            self.cpp_interface.cpp_lib.FreeMemory(result_ptr)
            
            cpp_result = json.loads(result_json)
            cpp_result["cpp_engine"] = True
            cpp_result["operation_count"] = self.operation_count
            
            return cpp_result
            
        except Exception as e:
            self.logger.error(f"Error processing analysis results: {e}")
            return {"success": False, "error": str(e), "cpp_engine": False}
    
    def _convert_to_threat_assessment(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Convert analysis results to threat assessment format."""
        try:
            threat_assessment_data = analysis_results.get("threat_assessment", {})
            file_info = analysis_results.get("phases", {}).get("capture", {}).get("file_info", {})
            
            return {
                "threat_id": analysis_results.get("workflow_id", f"threat_{int(datetime.now().timestamp())}"),
                "file_path": analysis_results.get("file_path", file_info.get("file_path", "")),
                "workflow_id": analysis_results.get("workflow_id", ""),
                "threat_level": self._map_threat_level(threat_assessment_data.get("overall_threat_level", "unknown")),
                "threat_score": threat_assessment_data.get("threat_score", 0.0),
                "detected_threats": analysis_results.get("detected_threats", []),
                "metadata": {
                    "detection_time": datetime.now(timezone.utc).isoformat(),
                    "file_size": file_info.get("file_size", 0),
                    "file_hash": file_info.get("file_hash", ""),
                    "original_filename": file_info.get("original_filename", "")
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error converting to threat assessment: {e}")
            return {
                "threat_id": f"error_threat_{int(datetime.now().timestamp())}",
                "file_path": "",
                "threat_level": 0,  # SAFE
                "threat_score": 0.0,
                "detected_threats": [],
                "metadata": {}
            }
    
    def _map_threat_level(self, threat_level_str: str) -> int:
        """Map threat level string to C++ enum value."""
        mapping = {
            "safe": 0,
            "low": 1,
            "medium": 2,
            "high": 3,
            "critical": 4
        }
        return mapping.get(threat_level_str.lower(), 0)
    
    def _fallback_response(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback response when C++ engine is not available."""
        self.logger.warning("Using fallback response (C++ engine not available)")
        
        decision = analysis_results.get("final_decision", {}).get("decision", "UNKNOWN")
        
        return {
            "success": True,
            "cpp_engine": False,
            "fallback": True,
            "action_taken": decision.lower(),
            "operation_id": f"fallback_{self.operation_count}",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "message": "Processed with fallback system"
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics from C++ engine."""
        try:
            base_metrics = {
                "operation_count": self.operation_count,
                "uptime_seconds": (datetime.now() - self.start_time).total_seconds(),
                "cpp_engine_available": CPP_ENGINE_AVAILABLE,
                "cpp_library_path": CPP_LIBRARY_PATH
            }
            
            # Get C++ engine metrics if available
            if CPP_ENGINE_AVAILABLE and self.cpp_interface.engine_handle:
                try:
                    metrics_ptr = self.cpp_interface.cpp_lib.GetPerformanceMetrics(
                        self.cpp_interface.engine_handle
                    )
                    
                    if metrics_ptr:
                        metrics_json = ctypes.string_at(metrics_ptr).decode('utf-8')
                        self.cpp_interface.cpp_lib.FreeMemory(metrics_ptr)
                        
                        cpp_metrics = json.loads(metrics_json)
                        base_metrics.update(cpp_metrics)
                        
                except Exception as e:
                    self.logger.warning(f"Failed to get C++ metrics: {e}")
            
            return base_metrics
            
        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {e}")
            return {"operation_count": self.operation_count, "error": str(e)}
    
    def get_operation_history(self, threat_id: str = "") -> List[Dict[str, Any]]:
        """Get operation history from C++ engine."""
        try:
            if CPP_ENGINE_AVAILABLE and self.cpp_interface.engine_handle:
                history_ptr = self.cpp_interface.cpp_lib.GetOperationHistory(
                    self.cpp_interface.engine_handle,
                    threat_id.encode('utf-8')
                )
                
                if history_ptr:
                    history_json = ctypes.string_at(history_ptr).decode('utf-8')
                    self.cpp_interface.cpp_lib.FreeMemory(history_ptr)
                    
                    return json.loads(history_json)
            
            return []
            
        except Exception as e:
            self.logger.error(f"Error getting operation history: {e}")
            return []
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get engine status."""
        try:
            status = {
                "cpp_engine_available": CPP_ENGINE_AVAILABLE,
                "cpp_library_path": CPP_LIBRARY_PATH,
                "operation_count": self.operation_count,
                "uptime_seconds": (datetime.now() - self.start_time).total_seconds()
            }
            
            if CPP_ENGINE_AVAILABLE and self.cpp_interface.engine_handle:
                try:
                    status_ptr = self.cpp_interface.cpp_lib.GetEngineStatus(
                        self.cpp_interface.engine_handle
                    )
                    
                    if status_ptr:
                        status_json = ctypes.string_at(status_ptr).decode('utf-8')
                        self.cpp_interface.cpp_lib.FreeMemory(status_ptr)
                        
                        cpp_status = json.loads(status_json)
                        status.update(cpp_status)
                        
                except Exception as e:
                    status["cpp_status_error"] = str(e)
            
            return status
            
        except Exception as e:
            return {"error": str(e)}

# Backward compatibility function
def Response(analysis_results: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Backward compatibility function.
    
    Args:
        analysis_results: Analysis results
        config: Configuration dictionary
        
    Returns:
        Response results
    """
    response_system = ResponseSystem(config)
    return response_system.process_analysis_results(analysis_results)

# Export main classes
__all__ = ['ResponseSystem', 'Response', 'CPP_ENGINE_AVAILABLE']
