/**
 * SBARDS Advanced Behavioral Analyzer Implementation
 * Resource monitoring, pattern detection, and process analysis
 */

#include "behavioral_analyzer.hpp"
#include <iostream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <sstream>
#include <fstream>

#ifdef _WIN32
    #include <wtsapi32.h>
    #include <userenv.h>
    #pragma comment(lib, "wtsapi32.lib")
    #pragma comment(lib, "userenv.lib")
#else
    #include <sys/sysinfo.h>
    #include <sys/statvfs.h>
    #include <ifaddrs.h>
    #include <net/if.h>
#endif

namespace sbards {
namespace behavioral {

BehavioralAnalyzer::BehavioralAnalyzer()
    : monitoring_(false)
    , monitoring_interval_(1000)
    , target_process_id_(0) {
    
    // Initialize default thresholds
    resource_thresholds_["cpu_usage"] = 80.0;
    resource_thresholds_["memory_usage_mb"] = 2048.0;
    resource_thresholds_["io_rate_mbps"] = 100.0;
    resource_thresholds_["network_rate_mbps"] = 50.0;
    
    // Enable all pattern detection by default
    pattern_detection_enabled_["file_encryption"] = true;
    pattern_detection_enabled_["file_access_patterns"] = true;
    pattern_detection_enabled_["shadow_copy_deletion"] = true;
    pattern_detection_enabled_["security_interference"] = true;
    pattern_detection_enabled_["process_injection"] = true;
    pattern_detection_enabled_["service_manipulation"] = true;
    
#ifdef _WIN32
    init_windows_monitoring();
#else
    init_linux_monitoring();
#endif
}

BehavioralAnalyzer::~BehavioralAnalyzer() {
    stop_monitoring();
    
#ifdef _WIN32
    cleanup_windows_monitoring();
#else
    cleanup_linux_monitoring();
#endif
}

bool BehavioralAnalyzer::start_monitoring(uint32_t target_process_id) {
    if (monitoring_) {
        return true;
    }
    
    target_process_id_ = target_process_id;
    monitoring_ = true;
    
    try {
        // Start monitoring threads
        monitoring_threads_.emplace_back(&BehavioralAnalyzer::resource_monitoring_loop, this);
        monitoring_threads_.emplace_back(&BehavioralAnalyzer::pattern_detection_loop, this);
        monitoring_threads_.emplace_back(&BehavioralAnalyzer::process_monitoring_loop, this);
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to start behavioral monitoring: " << e.what() << std::endl;
        monitoring_ = false;
        return false;
    }
}

void BehavioralAnalyzer::stop_monitoring() {
    monitoring_ = false;
    
    // Wait for threads to complete
    for (auto& thread : monitoring_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    monitoring_threads_.clear();
}

bool BehavioralAnalyzer::is_monitoring() const {
    return monitoring_;
}

std::vector<ResourceUsage> BehavioralAnalyzer::get_resource_usage_history() const {
    std::lock_guard<std::mutex> lock(data_mutex_);
    return std::vector<ResourceUsage>(resource_history_.begin(), resource_history_.end());
}

ResourceUsage BehavioralAnalyzer::get_current_resource_usage(uint32_t process_id) const {
    return collect_process_resources(process_id);
}

void BehavioralAnalyzer::set_resource_callback(std::function<void(const ResourceUsage&)> callback) {
    resource_callback_ = callback;
}

std::vector<BehavioralPattern> BehavioralAnalyzer::get_detected_patterns() const {
    std::lock_guard<std::mutex> lock(data_mutex_);
    return detected_patterns_;
}

void BehavioralAnalyzer::set_pattern_callback(std::function<void(const BehavioralPattern&)> callback) {
    pattern_callback_ = callback;
}

std::vector<ProcessInfo> BehavioralAnalyzer::get_process_tree() const {
    std::lock_guard<std::mutex> lock(data_mutex_);
    std::vector<ProcessInfo> processes;
    for (const auto& pair : process_map_) {
        processes.push_back(pair.second);
    }
    return processes;
}

ProcessInfo BehavioralAnalyzer::get_process_info(uint32_t process_id) const {
    std::lock_guard<std::mutex> lock(data_mutex_);
    auto it = process_map_.find(process_id);
    if (it != process_map_.end()) {
        return it->second;
    }
    return ProcessInfo{};
}

std::vector<uint32_t> BehavioralAnalyzer::get_suspicious_processes() const {
    std::lock_guard<std::mutex> lock(data_mutex_);
    return std::vector<uint32_t>(suspicious_processes_.begin(), suspicious_processes_.end());
}

void BehavioralAnalyzer::set_monitoring_interval(std::chrono::milliseconds interval) {
    monitoring_interval_ = interval;
}

void BehavioralAnalyzer::set_resource_thresholds(const std::unordered_map<std::string, double>& thresholds) {
    resource_thresholds_ = thresholds;
}

void BehavioralAnalyzer::enable_pattern_detection(const std::string& pattern_type, bool enabled) {
    pattern_detection_enabled_[pattern_type] = enabled;
}

void BehavioralAnalyzer::resource_monitoring_loop() {
    while (monitoring_) {
        try {
            std::vector<uint32_t> processes_to_monitor;
            
            if (target_process_id_ != 0) {
                processes_to_monitor.push_back(target_process_id_);
                
                // Also monitor child processes
                auto children = get_child_processes(target_process_id_);
                processes_to_monitor.insert(processes_to_monitor.end(), children.begin(), children.end());
            } else {
                // Monitor all processes (system-wide)
#ifdef _WIN32
                processes_to_monitor = enumerate_windows_processes();
#else
                processes_to_monitor = enumerate_linux_processes();
#endif
            }
            
            // Collect resource usage for each process
            for (uint32_t process_id : processes_to_monitor) {
                if (!is_process_running(process_id)) {
                    continue;
                }
                
                ResourceUsage usage = collect_process_resources(process_id);
                
                {
                    std::lock_guard<std::mutex> lock(data_mutex_);
                    resource_history_.push_back(usage);
                    
                    // Limit history size
                    if (resource_history_.size() > 10000) {
                        resource_history_.pop_front();
                    }
                }
                
                // Check for anomalies
                if (exceeds_cpu_threshold(usage) || exceeds_memory_threshold(usage) ||
                    exceeds_io_threshold(usage) || exceeds_network_threshold(usage)) {
                    
                    std::lock_guard<std::mutex> lock(data_mutex_);
                    suspicious_processes_.insert(process_id);
                }
                
                // Invoke callback if set
                if (resource_callback_) {
                    resource_callback_(usage);
                }
            }
            
            std::this_thread::sleep_for(monitoring_interval_);
            
        } catch (const std::exception& e) {
            std::cerr << "Resource monitoring error: " << e.what() << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(5));
        }
    }
}

void BehavioralAnalyzer::pattern_detection_loop() {
    while (monitoring_) {
        try {
            // Run pattern detection algorithms
            if (pattern_detection_enabled_["file_encryption"]) {
                analyze_file_encryption_patterns();
            }
            
            if (pattern_detection_enabled_["file_access_patterns"]) {
                analyze_file_access_patterns();
            }
            
            if (pattern_detection_enabled_["shadow_copy_deletion"]) {
                analyze_shadow_copy_operations();
            }
            
            if (pattern_detection_enabled_["security_interference"]) {
                analyze_security_tool_interference();
            }
            
            if (pattern_detection_enabled_["process_injection"]) {
                analyze_process_creation_patterns();
            }
            
            if (pattern_detection_enabled_["service_manipulation"]) {
                analyze_service_operations();
            }
            
            std::this_thread::sleep_for(std::chrono::seconds(5));
            
        } catch (const std::exception& e) {
            std::cerr << "Pattern detection error: " << e.what() << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(10));
        }
    }
}

void BehavioralAnalyzer::process_monitoring_loop() {
    while (monitoring_) {
        try {
            update_process_tree();
            
            // Analyze each process for injection and suspicious behavior
            std::vector<uint32_t> processes_to_analyze;
            
            {
                std::lock_guard<std::mutex> lock(data_mutex_);
                for (const auto& pair : process_map_) {
                    processes_to_analyze.push_back(pair.first);
                }
            }
            
            for (uint32_t process_id : processes_to_analyze) {
                detect_process_injection_techniques(process_id);
                analyze_process_chain(process_id);
            }
            
            std::this_thread::sleep_for(std::chrono::seconds(2));
            
        } catch (const std::exception& e) {
            std::cerr << "Process monitoring error: " << e.what() << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(5));
        }
    }
}

ResourceUsage BehavioralAnalyzer::collect_process_resources(uint32_t process_id) {
    ResourceUsage usage{};
    usage.timestamp = std::chrono::system_clock::now();
    usage.process_id = process_id;
    usage.process_name = get_process_name(process_id);
    
    try {
#ifdef _WIN32
        return collect_windows_process_resources(process_id);
#else
        return collect_linux_process_resources(process_id);
#endif
    } catch (const std::exception& e) {
        std::cerr << "Failed to collect resources for process " << process_id << ": " << e.what() << std::endl;
        return usage;
    }
}

void BehavioralAnalyzer::analyze_file_encryption_patterns() {
    // This would integrate with file monitoring to detect encryption patterns
    // For now, this is a placeholder that would be connected to the file monitor
    
    // Example pattern detection logic:
    // 1. Monitor file operations for bulk writes
    // 2. Check for file extension changes to suspicious formats
    // 3. Analyze entropy changes in files
    // 4. Detect crypto API usage
    
    static uint32_t simulated_encrypted_files = 0;
    simulated_encrypted_files += 5; // Simulate detection
    
    if (simulated_encrypted_files > 20) {
        BehavioralPattern pattern = create_pattern(
            "file_encryption",
            "Bulk file encryption detected",
            0.9,
            {"Multiple files encrypted", "Suspicious extensions", "Crypto API usage"}
        );
        
        pattern.pattern_data.files_encrypted = simulated_encrypted_files;
        pattern.pattern_data.encryption_extensions = {".encrypted", ".locked"};
        pattern.pattern_data.encryption_rate_files_per_second = 2.5;
        
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            detected_patterns_.push_back(pattern);
        }
        
        if (pattern_callback_) {
            pattern_callback_(pattern);
        }
        
        simulated_encrypted_files = 0; // Reset for next detection cycle
    }
}

void BehavioralAnalyzer::analyze_file_access_patterns() {
    // Analyze file access patterns for sequential reads, bulk operations, etc.
    // This would integrate with file monitoring data
    
    // Example: Detect sequential file access pattern (potential data theft)
    static uint32_t sequential_accesses = 0;
    sequential_accesses += 10;
    
    if (sequential_accesses > 100) {
        BehavioralPattern pattern = create_pattern(
            "sequential_file_access",
            "Sequential file access pattern detected",
            0.8,
            {"Sequential reads", "Multiple directories", "Large data volume"}
        );
        
        pattern.pattern_data.access_pattern_type = "sequential";
        pattern.pattern_data.files_accessed = sequential_accesses;
        pattern.pattern_data.access_rate_files_per_second = 5.0;
        
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            detected_patterns_.push_back(pattern);
        }
        
        if (pattern_callback_) {
            pattern_callback_(pattern);
        }
        
        sequential_accesses = 0;
    }
}

void BehavioralAnalyzer::analyze_shadow_copy_operations() {
    // Monitor for shadow copy deletion attempts
    // This would integrate with system call monitoring
    
    // Example: Detect shadow copy deletion commands
    std::vector<std::string> shadow_copy_commands = {
        "vssadmin delete shadows",
        "wmic shadowcopy delete",
        "bcdedit /set {default} bootstatuspolicy ignoreallfailures"
    };
    
    // In real implementation, this would check actual command executions
    static bool shadow_copy_detected = false;
    if (!shadow_copy_detected) {
        shadow_copy_detected = true;
        
        BehavioralPattern pattern = create_pattern(
            "shadow_copy_deletion",
            "Shadow copy deletion attempt detected",
            0.95,
            {"Shadow copy deletion command", "Ransomware indicator"}
        );
        
        pattern.pattern_data.shadow_copies_deleted = 1;
        pattern.pattern_data.shadow_copy_commands = shadow_copy_commands;
        
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            detected_patterns_.push_back(pattern);
        }
        
        if (pattern_callback_) {
            pattern_callback_(pattern);
        }
    }
}

void BehavioralAnalyzer::analyze_security_tool_interference() {
    // Monitor for attempts to disable security tools
    // This would integrate with process and registry monitoring
    
    std::vector<std::string> security_processes = {
        "MsMpEng.exe", "avp.exe", "avgnt.exe", "avguard.exe",
        "bdagent.exe", "ccSvcHst.exe", "ntrtscan.exe"
    };
    
    // In real implementation, check if security processes are being terminated
    static bool security_interference_detected = false;
    if (!security_interference_detected) {
        security_interference_detected = true;
        
        BehavioralPattern pattern = create_pattern(
            "security_interference",
            "Security tool interference detected",
            0.9,
            {"Security process termination", "Antivirus disabling"}
        );
        
        pattern.pattern_data.disabled_security_tools = {"Windows Defender"};
        pattern.pattern_data.security_processes_terminated = {"MsMpEng.exe"};
        
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            detected_patterns_.push_back(pattern);
        }
        
        if (pattern_callback_) {
            pattern_callback_(pattern);
        }
    }
}

void BehavioralAnalyzer::analyze_process_creation_patterns() {
    // Analyze process creation patterns for suspicious behavior
    std::lock_guard<std::mutex> lock(data_mutex_);
    
    for (const auto& pair : process_map_) {
        const ProcessInfo& process = pair.second;
        
        // Check for excessive child process creation
        if (process.child_processes.size() > 10) {
            BehavioralPattern pattern = create_pattern(
                "excessive_process_creation",
                "Excessive child process creation detected",
                0.7,
                {"Multiple child processes", "Process spawning"}
            );
            
            pattern.pattern_data.child_processes_created = process.child_processes.size();
            pattern.pattern_data.process_chain = process.child_processes;
            
            detected_patterns_.push_back(pattern);
            
            if (pattern_callback_) {
                pattern_callback_(pattern);
            }
        }
    }
}

void BehavioralAnalyzer::analyze_service_operations() {
    // Monitor service installation and modification
    // This would integrate with registry and system monitoring
    
    static bool service_manipulation_detected = false;
    if (!service_manipulation_detected) {
        service_manipulation_detected = true;
        
        BehavioralPattern pattern = create_pattern(
            "service_manipulation",
            "Suspicious service activity detected",
            0.8,
            {"Service installation", "Service modification"}
        );
        
        pattern.pattern_data.services_installed = {"MaliciousService"};
        pattern.pattern_data.services_started = {"MaliciousService"};
        
        {
            std::lock_guard<std::mutex> lock(data_mutex_);
            detected_patterns_.push_back(pattern);
        }
        
        if (pattern_callback_) {
            pattern_callback_(pattern);
        }
    }
}

BehavioralPattern BehavioralAnalyzer::create_pattern(const std::string& type, const std::string& description,
                                                   double confidence, const std::vector<std::string>& indicators) {
    BehavioralPattern pattern;
    pattern.pattern_type = type;
    pattern.description = description;
    pattern.confidence_score = confidence;
    pattern.first_detected = std::chrono::system_clock::now();
    pattern.last_detected = pattern.first_detected;
    pattern.occurrence_count = 1;
    pattern.indicators = indicators;
    
    return pattern;
}

void BehavioralAnalyzer::update_pattern_occurrence(BehavioralPattern& pattern) {
    pattern.last_detected = std::chrono::system_clock::now();
    pattern.occurrence_count++;
}

bool BehavioralAnalyzer::exceeds_cpu_threshold(const ResourceUsage& usage) {
    return usage.cpu_usage_percent > resource_thresholds_["cpu_usage"];
}

bool BehavioralAnalyzer::exceeds_memory_threshold(const ResourceUsage& usage) {
    double memory_mb = static_cast<double>(usage.memory_working_set) / (1024 * 1024);
    return memory_mb > resource_thresholds_["memory_usage_mb"];
}

bool BehavioralAnalyzer::exceeds_io_threshold(const ResourceUsage& usage) {
    return usage.io_read_rate_mbps > resource_thresholds_["io_rate_mbps"] ||
           usage.io_write_rate_mbps > resource_thresholds_["io_rate_mbps"];
}

bool BehavioralAnalyzer::exceeds_network_threshold(const ResourceUsage& usage) {
    return usage.network_send_rate_mbps > resource_thresholds_["network_rate_mbps"] ||
           usage.network_receive_rate_mbps > resource_thresholds_["network_rate_mbps"];
}

} // namespace behavioral
} // namespace sbards
