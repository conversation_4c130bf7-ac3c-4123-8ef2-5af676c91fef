2025-05-30 18:41:40,693 - SBARDS - INFO - Logging initialized at level INFO
2025-05-30 18:41:40,693 - SBARDS.Main - INFO - Initializing SBARDS...
2025-05-30 18:41:40,713 - SBARDS.Utils - INFO - Environment validation passed
2025-05-30 18:41:40,714 - SBARDS - INFO - Logging initialized at level INFO
2025-05-30 18:41:40,715 - SBARDS.Prescanning - INFO - Initializing scanners
2025-05-30 18:41:40,715 - SBARDS.DynamicAnalyzer - ERROR - Failed to initialize Docker: 'NoneType' object has no attribute 'from_env'
2025-05-30 18:41:40,716 - SBARDS.MemoryProtection - INFO - Memory Protection Layer initialized
2025-05-30 18:41:40,770 - SBARDS.MonitorManager - INFO - Detected platform: windows
2025-05-30 18:41:40,778 - SBARDS.ResponseManager - INFO - Response Manager initialized
2025-05-30 18:41:40,778 - SBARDS.AlertManager - INFO - Alert <PERSON> initialized
2025-05-30 18:41:40,818 - SBARDS.Mock.MockOSQueryMonitor - INFO - MockOSQueryMonitor initialized
2025-05-30 18:41:40,818 - SBARDS.Mock.MockSysmonMonitor - INFO - MockSysmonMonitor initialized
2025-05-30 18:41:40,818 - SBARDS.Mock.MockETWMonitor - INFO - MockETWMonitor initialized
2025-05-30 18:41:40,818 - SBARDS.MonitorManager - INFO - Using mock monitors for Windows
2025-05-30 18:41:40,818 - SBARDS.MonitorManager - INFO - Monitor Manager initialized
2025-05-30 18:41:40,818 - SBARDS.Main - INFO - Initialized 5/9 phases successfully
2025-05-30 18:41:40,818 - SBARDS.Main - WARNING - Missing dependencies: CaptureLayer, StaticAnalyzer, ExternalIntegrationLayer, WorkflowOrchestrator
2025-05-30 18:41:40,818 - SBARDS.Main - INFO - SBARDS initialization completed successfully
2025-05-30 18:41:40,818 - SBARDS.Main - INFO - === INTERACTIVE MODE ===
2025-05-30 18:42:11,636 - SBARDS.Main - INFO - === DYNAMIC ANALYSIS LAYER PHASE ===
2025-05-30 18:42:11,636 - SBARDS.Main - INFO - Performing behavioral analysis in sandbox...
2025-05-30 18:42:11,636 - SBARDS.DynamicAnalyzer - INFO - Starting dynamic analysis of: C:\Users\<USER>\Desktop\مجلد جديد
2025-05-30 18:42:11,652 - SBARDS.DynamicAnalyzer - INFO - Dynamic monitoring started
2025-05-30 18:47:11,654 - SBARDS.DynamicAnalyzer - INFO - Dynamic monitoring stopped
2025-05-30 18:47:11,654 - SBARDS.DynamicAnalyzer - INFO - Dynamic analysis completed for: C:\Users\<USER>\Desktop\مجلد جديد
2025-05-30 18:47:11,655 - SBARDS.DynamicAnalyzer - INFO - Dynamic monitoring stopped
2025-05-30 18:47:11,683 - SBARDS.Main - INFO - ✓ Dynamic analysis completed
2025-05-30 18:47:11,684 - SBARDS.Main - INFO -   - Analysis Duration: 300.018177 seconds
2025-05-30 18:47:11,684 - SBARDS.Main - INFO -   - Risk Level: LOW
2025-05-30 18:47:11,685 - SBARDS.Main - INFO -   - Risk Score: 22/100
2025-05-30 18:47:11,685 - SBARDS.Main - INFO -   - Sandbox Type: docker
2025-05-30 18:47:52,784 - SBARDS - INFO - Logging initialized at level INFO
2025-05-30 18:47:52,784 - SBARDS.Main - INFO - Initializing SBARDS...
2025-05-30 18:47:52,800 - SBARDS.Utils - INFO - Environment validation passed
2025-05-30 18:47:52,801 - SBARDS - INFO - Logging initialized at level INFO
2025-05-30 18:47:52,801 - SBARDS.Prescanning - INFO - Initializing scanners
2025-05-30 18:47:52,802 - SBARDS.DynamicAnalyzer - ERROR - Failed to initialize Docker: 'NoneType' object has no attribute 'from_env'
2025-05-30 18:47:52,803 - SBARDS.MemoryProtection - INFO - Memory Protection Layer initialized
2025-05-30 18:47:52,853 - SBARDS.MonitorManager - INFO - Detected platform: windows
2025-05-30 18:47:52,857 - SBARDS.ResponseManager - INFO - Response Manager initialized
2025-05-30 18:47:52,858 - SBARDS.AlertManager - INFO - Alert Manager initialized
2025-05-30 18:47:52,883 - SBARDS.Mock.MockOSQueryMonitor - INFO - MockOSQueryMonitor initialized
2025-05-30 18:47:52,883 - SBARDS.Mock.MockSysmonMonitor - INFO - MockSysmonMonitor initialized
2025-05-30 18:47:52,883 - SBARDS.Mock.MockETWMonitor - INFO - MockETWMonitor initialized
2025-05-30 18:47:52,883 - SBARDS.MonitorManager - INFO - Using mock monitors for Windows
2025-05-30 18:47:52,883 - SBARDS.MonitorManager - INFO - Monitor Manager initialized
2025-05-30 18:47:52,884 - SBARDS.Main - INFO - Initialized 5/9 phases successfully
2025-05-30 18:47:52,884 - SBARDS.Main - WARNING - Missing dependencies: CaptureLayer, StaticAnalyzer, ExternalIntegrationLayer, WorkflowOrchestrator
2025-05-30 18:47:52,884 - SBARDS.Main - INFO - SBARDS initialization completed successfully
2025-05-30 18:47:52,884 - SBARDS.Main - INFO - === INTERACTIVE MODE ===
2025-05-30 18:48:13,611 - SBARDS.Main - INFO - === DYNAMIC ANALYSIS LAYER PHASE ===
2025-05-30 18:48:13,611 - SBARDS.Main - INFO - Performing behavioral analysis in sandbox...
2025-05-30 18:48:13,611 - SBARDS.DynamicAnalyzer - INFO - Starting dynamic analysis of: C:\Users\<USER>\Desktop\django_pro
2025-05-30 18:48:13,611 - SBARDS.DynamicAnalyzer - INFO - Dynamic monitoring started
2025-05-30 18:53:03,761 - SBARDS.DynamicAnalyzer - INFO - Dynamic monitoring stopped
2025-05-30 18:53:03,761 - SBARDS.Main - INFO - Interrupted by user
2025-05-30 19:06:39,128 - SBARDS - INFO - Logging initialized at level INFO
2025-05-30 19:06:39,128 - SBARDS.Main - INFO - Initializing SBARDS...
2025-05-30 19:06:39,139 - SBARDS.Utils - INFO - Environment validation passed
2025-05-30 19:06:39,140 - SBARDS - INFO - Logging initialized at level INFO
2025-05-30 19:06:39,141 - SBARDS.Prescanning - INFO - Initializing scanners
2025-05-30 19:06:39,141 - SBARDS.DynamicAnalyzer - ERROR - Failed to initialize Docker: 'NoneType' object has no attribute 'from_env'
2025-05-30 19:06:39,142 - SBARDS.MemoryProtection - INFO - Memory Protection Layer initialized
2025-05-30 19:06:39,194 - SBARDS.MonitorManager - INFO - Detected platform: windows
2025-05-30 19:06:39,195 - SBARDS.ResponseManager - INFO - Response Manager initialized
2025-05-30 19:06:39,195 - SBARDS.AlertManager - INFO - Alert Manager initialized
2025-05-30 19:06:39,230 - SBARDS.Mock.MockOSQueryMonitor - INFO - MockOSQueryMonitor initialized
2025-05-30 19:06:39,230 - SBARDS.Mock.MockSysmonMonitor - INFO - MockSysmonMonitor initialized
2025-05-30 19:06:39,230 - SBARDS.Mock.MockETWMonitor - INFO - MockETWMonitor initialized
2025-05-30 19:06:39,230 - SBARDS.MonitorManager - INFO - Using mock monitors for Windows
2025-05-30 19:06:39,230 - SBARDS.MonitorManager - INFO - Monitor Manager initialized
2025-05-30 19:06:39,230 - SBARDS.Main - INFO - Initialized 5/9 phases successfully
2025-05-30 19:06:39,230 - SBARDS.Main - WARNING - Missing dependencies: CaptureLayer, StaticAnalyzer, ExternalIntegrationLayer, WorkflowOrchestrator
2025-05-30 19:06:39,230 - SBARDS.Main - INFO - SBARDS initialization completed successfully
2025-05-30 19:06:39,230 - SBARDS.Main - INFO - === INTERACTIVE MODE ===
2025-05-30 19:07:13,032 - SBARDS.Main - INFO - === DYNAMIC ANALYSIS LAYER PHASE ===
2025-05-30 19:07:13,032 - SBARDS.Main - INFO - Performing behavioral analysis in sandbox...
2025-05-30 19:07:13,049 - SBARDS.Main - ERROR - ✗ Dynamic analysis failed: File not found: 4
2025-05-30 19:07:40,186 - SBARDS.Main - INFO - === DYNAMIC ANALYSIS LAYER PHASE ===
2025-05-30 19:07:40,186 - SBARDS.Main - INFO - Performing behavioral analysis in sandbox...
2025-05-30 19:07:40,186 - SBARDS.DynamicAnalyzer - INFO - Starting dynamic analysis of: C:\Users\<USER>\Desktop\django_pro
2025-05-30 19:07:40,202 - SBARDS.DynamicAnalyzer - INFO - Dynamic monitoring started
2025-05-30 19:11:04,076 - SBARDS.Main - INFO - === DYNAMIC ANALYSIS LAYER PHASE ===
2025-05-30 19:11:04,076 - SBARDS.Main - INFO - Performing behavioral analysis in sandbox...
2025-05-30 19:11:04,076 - SBARDS.DynamicAnalyzer - INFO - Starting dynamic analysis of: C:\Users\<USER>\Desktop\django_pro
2025-05-30 19:11:04,086 - SBARDS.DynamicAnalyzer - INFO - Dynamic monitoring started
2025-05-30 19:11:08,075 - SBARDS - INFO - Logging initialized at level INFO
2025-05-30 19:11:08,075 - SBARDS.Main - INFO - Initializing SBARDS...
2025-05-30 19:11:08,089 - SBARDS.Utils - INFO - Environment validation passed
2025-05-30 19:11:08,090 - SBARDS - INFO - Logging initialized at level INFO
2025-05-30 19:11:08,090 - SBARDS.Prescanning - INFO - Initializing scanners
2025-05-30 19:11:08,091 - SBARDS.DynamicAnalyzer - ERROR - Failed to initialize Docker: 'NoneType' object has no attribute 'from_env'
2025-05-30 19:11:08,091 - SBARDS.MemoryProtection - INFO - Memory Protection Layer initialized
2025-05-30 19:11:08,149 - SBARDS.MonitorManager - INFO - Detected platform: windows
2025-05-30 19:11:08,152 - SBARDS.ResponseManager - INFO - Response Manager initialized
2025-05-30 19:11:08,152 - SBARDS.AlertManager - INFO - Alert Manager initialized
2025-05-30 19:11:08,186 - SBARDS.Mock.MockOSQueryMonitor - INFO - MockOSQueryMonitor initialized
2025-05-30 19:11:08,186 - SBARDS.Mock.MockSysmonMonitor - INFO - MockSysmonMonitor initialized
2025-05-30 19:11:08,186 - SBARDS.Mock.MockETWMonitor - INFO - MockETWMonitor initialized
2025-05-30 19:11:08,186 - SBARDS.MonitorManager - INFO - Using mock monitors for Windows
2025-05-30 19:11:08,186 - SBARDS.MonitorManager - INFO - Monitor Manager initialized
2025-05-30 19:11:08,186 - SBARDS.Main - INFO - Initialized 5/9 phases successfully
2025-05-30 19:11:08,186 - SBARDS.Main - WARNING - Missing dependencies: CaptureLayer, StaticAnalyzer, ExternalIntegrationLayer, WorkflowOrchestrator
2025-05-30 19:11:08,186 - SBARDS.Main - INFO - SBARDS initialization completed successfully
2025-05-30 19:11:08,186 - SBARDS.Main - INFO - === INTERACTIVE MODE ===
2025-05-30 19:11:16,787 - SBARDS.Main - INFO - === DYNAMIC ANALYSIS LAYER PHASE ===
2025-05-30 19:11:16,787 - SBARDS.Main - INFO - Performing behavioral analysis in sandbox...
2025-05-30 19:11:16,788 - SBARDS.DynamicAnalyzer - INFO - Starting dynamic analysis of: C:\Users\<USER>\Desktop\django_pro
2025-05-30 19:11:16,805 - SBARDS.DynamicAnalyzer - INFO - Dynamic monitoring started
2025-05-30 19:11:43,738 - SBARDS.DynamicAnalyzer - INFO - Dynamic monitoring stopped
2025-05-30 19:11:43,738 - SBARDS.Main - INFO - Interrupted by user
2025-05-30 19:11:47,125 - SBARDS.DynamicAnalyzer - INFO - Dynamic monitoring stopped
2025-05-30 19:11:47,125 - SBARDS.Main - INFO - Interrupted by user
2025-05-30 21:32:16,921 - SBARDS - INFO - Logging initialized at level INFO
2025-05-30 21:32:16,921 - SBARDS.Main - INFO - Initializing SBARDS...
2025-05-30 21:32:16,938 - SBARDS.Utils - INFO - Environment validation passed
2025-05-30 21:32:16,939 - SBARDS - INFO - Logging initialized at level INFO
2025-05-30 21:32:16,940 - SBARDS.Prescanning - INFO - Initializing scanners
2025-05-30 21:32:16,940 - SBARDS.DynamicAnalyzer - ERROR - Failed to initialize Docker: 'NoneType' object has no attribute 'from_env'
2025-05-30 21:32:16,941 - SBARDS.MemoryProtection - INFO - Memory Protection Layer initialized
2025-05-30 21:32:16,992 - SBARDS.MonitorManager - INFO - Detected platform: windows
2025-05-30 21:32:16,996 - SBARDS.ResponseManager - INFO - Response Manager initialized
2025-05-30 21:32:16,996 - SBARDS.AlertManager - INFO - Alert Manager initialized
2025-05-30 21:32:17,026 - SBARDS.Mock.MockOSQueryMonitor - INFO - MockOSQueryMonitor initialized
2025-05-30 21:32:17,026 - SBARDS.Mock.MockSysmonMonitor - INFO - MockSysmonMonitor initialized
2025-05-30 21:32:17,026 - SBARDS.Mock.MockETWMonitor - INFO - MockETWMonitor initialized
2025-05-30 21:32:17,026 - SBARDS.MonitorManager - INFO - Using mock monitors for Windows
2025-05-30 21:32:17,026 - SBARDS.MonitorManager - INFO - Monitor Manager initialized
2025-05-30 21:32:17,026 - SBARDS.Main - INFO - Initialized 5/9 phases successfully
2025-05-30 21:32:17,026 - SBARDS.Main - WARNING - Missing dependencies: CaptureLayer, StaticAnalyzer, ExternalIntegrationLayer, WorkflowOrchestrator
2025-05-30 21:32:17,026 - SBARDS.Main - INFO - SBARDS initialization completed successfully
2025-05-30 21:32:17,026 - SBARDS.Main - INFO - === INTERACTIVE MODE ===
2025-05-30 21:33:19,992 - SBARDS.Main - INFO - === COMPLETE WORKFLOW EXECUTION ===
2025-05-30 21:33:19,992 - SBARDS.Main - INFO - Processing file: C:\Users\<USER>\Desktop\django_pro
2025-05-30 21:33:19,992 - SBARDS.Main - ERROR - Complete workflow error: 'NoneType' object has no attribute 'process_file'
2025-05-30 21:33:54,625 - SBARDS.Main - INFO - === COMPLETE WORKFLOW EXECUTION ===
2025-05-30 21:33:54,625 - SBARDS.Main - INFO - Processing file: C:\Users\<USER>\Desktop\django_pro
2025-05-30 21:33:54,625 - SBARDS.Main - ERROR - Complete workflow error: 'NoneType' object has no attribute 'process_file'
2025-05-30 21:34:26,020 - SBARDS.Main - INFO - Interrupted by user
2025-05-30 21:36:37,648 - SBARDS - INFO - Logging initialized at level INFO
2025-05-30 21:36:37,648 - SBARDS.Main - INFO - Initializing SBARDS...
2025-05-30 21:36:37,660 - SBARDS.Utils - INFO - Environment validation passed
2025-05-30 21:36:37,660 - SBARDS - INFO - Logging initialized at level INFO
2025-05-30 21:36:37,660 - SBARDS.Prescanning - INFO - Initializing scanners
2025-05-30 21:36:37,660 - SBARDS.DynamicAnalyzer - ERROR - Failed to initialize Docker: 'NoneType' object has no attribute 'from_env'
2025-05-30 21:36:37,660 - SBARDS.MemoryProtection - INFO - Memory Protection Layer initialized
2025-05-30 21:36:37,705 - SBARDS.MonitorManager - INFO - Detected platform: windows
2025-05-30 21:36:37,705 - SBARDS.ResponseManager - INFO - Response Manager initialized
2025-05-30 21:36:37,705 - SBARDS.AlertManager - INFO - Alert Manager initialized
2025-05-30 21:36:37,739 - SBARDS.Mock.MockOSQueryMonitor - INFO - MockOSQueryMonitor initialized
2025-05-30 21:36:37,739 - SBARDS.Mock.MockSysmonMonitor - INFO - MockSysmonMonitor initialized
2025-05-30 21:36:37,739 - SBARDS.Mock.MockETWMonitor - INFO - MockETWMonitor initialized
2025-05-30 21:36:37,739 - SBARDS.MonitorManager - INFO - Using mock monitors for Windows
2025-05-30 21:36:37,739 - SBARDS.MonitorManager - INFO - Monitor Manager initialized
2025-05-30 21:36:37,739 - SBARDS.Main - INFO - Initialized 5/9 phases successfully
2025-05-30 21:36:37,739 - SBARDS.Main - WARNING - Missing dependencies: CaptureLayer, StaticAnalyzer, ExternalIntegrationLayer, WorkflowOrchestrator
2025-05-30 21:36:37,741 - SBARDS.Main - INFO - SBARDS initialization completed successfully
2025-05-30 21:36:37,741 - SBARDS.Main - INFO - === INTERACTIVE MODE ===
