#!/usr/bin/env python3
"""
SBARDS Unified Response System - Python Integration Layer
Complete integration of Response and Data management with C++ Core Engine

This unified system provides:
1. C++ Core Engine (Primary processing)
2. Python Integration Layer (API, Configuration, Monitoring)
3. Unified Response Operations (Quarantine, Honeypot, Alerts)
4. Integrated Data Management (Storage, Retrieval, Encryption)
5. Advanced Security Features (Military-grade encryption)
6. Cross-Platform Compatibility (Windows, Linux, macOS)
7. Real-time Performance Monitoring
8. Enterprise-Grade Operations
"""

import os
import sys
import json
import logging
import ctypes
import asyncio
import threading
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Callable
import platform
import subprocess
from dataclasses import dataclass, asdict

# Platform detection for C++ library loading
PLATFORM = platform.system().lower()
ARCHITECTURE = platform.machine().lower()

# C++ Library availability check
CPP_UNIFIED_CORE_AVAILABLE = False
CPP_UNIFIED_LIBRARY_PATH = None

# Optional imports with fallbacks
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    requests = None
    REQUESTS_AVAILABLE = False

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    psutil = None
    PSUTIL_AVAILABLE = False

@dataclass
class ThreatAssessment:
    """Threat assessment data structure."""
    threat_id: str
    file_path: str
    threat_score: float
    threat_level: str
    detected_threats: List[str]
    metadata: Dict[str, Any]
    detection_time: str

@dataclass
class ResponseResult:
    """Response operation result."""
    success: bool
    operation_id: str
    action_taken: str
    target_path: str
    stored_path: str
    error_message: str
    execution_time_ms: float
    timestamp: str
    metadata: Dict[str, Any]

@dataclass
class UnifiedMetrics:
    """Unified performance metrics."""
    total_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    quarantine_operations: int = 0
    honeypot_operations: int = 0
    data_operations: int = 0
    encryption_operations: int = 0
    average_operation_time_ms: float = 0.0
    total_data_processed_bytes: int = 0

class CPPUnifiedEngineInterface:
    """Interface to C++ Unified Response Engine."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize C++ Unified Engine Interface."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.CPPUnifiedInterface")
        
        # C++ library handle
        self.cpp_lib = None
        self.engine_handle = None
        self.library_info = None
        
        # Threading
        self.lock = threading.RLock()
        
        # Initialize C++ engine
        self._initialize_cpp_unified_engine()
    
    def _initialize_cpp_unified_engine(self):
        """Initialize C++ Unified Engine."""
        try:
            # Load C++ library
            if self._load_cpp_unified_library():
                # Create engine instance
                config_json = json.dumps(self._convert_config_to_cpp_format())
                self.engine_handle = self.cpp_lib.CreateUnifiedResponseEngine(config_json.encode('utf-8'))
                
                if self.engine_handle:
                    # Initialize engine
                    if self.cpp_lib.InitializeUnifiedResponseEngine(self.engine_handle):
                        global CPP_UNIFIED_CORE_AVAILABLE
                        CPP_UNIFIED_CORE_AVAILABLE = True
                        self.logger.info("C++ Unified Response Engine initialized successfully")
                    else:
                        self.logger.error("Failed to initialize C++ Unified Response Engine")
                        self._cleanup()
                else:
                    self.logger.error("Failed to create C++ Unified Response Engine")
            else:
                self.logger.warning("C++ unified library not available, using Python fallback")
                
        except Exception as e:
            self.logger.error(f"C++ unified engine initialization failed: {e}")
            self._cleanup()
    
    def _load_cpp_unified_library(self) -> bool:
        """Load C++ unified library."""
        try:
            # Determine library paths based on platform
            lib_paths = self._get_unified_library_paths()
            
            for lib_path in lib_paths:
                if lib_path.exists():
                    try:
                        self.cpp_lib = ctypes.CDLL(str(lib_path))
                        self._setup_unified_function_signatures()
                        
                        global CPP_UNIFIED_LIBRARY_PATH
                        CPP_UNIFIED_LIBRARY_PATH = str(lib_path)
                        
                        self.logger.info(f"Loaded C++ unified library: {lib_path}")
                        return True
                        
                    except Exception as e:
                        self.logger.warning(f"Failed to load {lib_path}: {e}")
                        continue
            
            self.logger.warning("No C++ unified library found")
            return False
            
        except Exception as e:
            self.logger.error(f"Error loading C++ unified library: {e}")
            return False
    
    def _get_unified_library_paths(self) -> List[Path]:
        """Get possible C++ unified library paths."""
        base_path = Path(__file__).parent / "cpp_core"
        
        if PLATFORM == "windows":
            extensions = [".dll"]
        elif PLATFORM == "darwin":
            extensions = [".dylib", ".so"]
        else:  # Linux and others
            extensions = [".so"]
        
        paths = []
        for ext in extensions:
            # Build directory
            paths.append(base_path / "build" / f"libsbards_unified_response{ext}")
            paths.append(base_path / "build" / "lib" / f"libsbards_unified_response{ext}")
            
            # Direct in cpp_core
            paths.append(base_path / f"libsbards_unified_response{ext}")
            
            # Release/Debug directories
            paths.append(base_path / "Release" / f"sbards_unified_response{ext}")
            paths.append(base_path / "Debug" / f"sbards_unified_response{ext}")
        
        return paths
    
    def _setup_unified_function_signatures(self):
        """Setup C++ unified function signatures for ctypes."""
        try:
            # CreateUnifiedResponseEngine
            self.cpp_lib.CreateUnifiedResponseEngine.restype = ctypes.c_void_p
            self.cpp_lib.CreateUnifiedResponseEngine.argtypes = [ctypes.c_char_p]
            
            # DestroyUnifiedResponseEngine
            self.cpp_lib.DestroyUnifiedResponseEngine.restype = None
            self.cpp_lib.DestroyUnifiedResponseEngine.argtypes = [ctypes.c_void_p]
            
            # InitializeUnifiedResponseEngine
            self.cpp_lib.InitializeUnifiedResponseEngine.restype = ctypes.c_bool
            self.cpp_lib.InitializeUnifiedResponseEngine.argtypes = [ctypes.c_void_p]
            
            # ShutdownUnifiedResponseEngine
            self.cpp_lib.ShutdownUnifiedResponseEngine.restype = None
            self.cpp_lib.ShutdownUnifiedResponseEngine.argtypes = [ctypes.c_void_p]
            
            # ProcessThreatOperation
            self.cpp_lib.ProcessThreatOperation.restype = ctypes.c_char_p
            self.cpp_lib.ProcessThreatOperation.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            # QuarantineFileOperation
            self.cpp_lib.QuarantineFileOperation.restype = ctypes.c_char_p
            self.cpp_lib.QuarantineFileOperation.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            # HoneypotIsolateOperation
            self.cpp_lib.HoneypotIsolateOperation.restype = ctypes.c_char_p
            self.cpp_lib.HoneypotIsolateOperation.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            # StoreDataOperation
            self.cpp_lib.StoreDataOperation.restype = ctypes.c_char_p
            self.cpp_lib.StoreDataOperation.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            # RetrieveDataOperation
            self.cpp_lib.RetrieveDataOperation.restype = ctypes.c_char_p
            self.cpp_lib.RetrieveDataOperation.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            # GetPerformanceMetrics
            self.cpp_lib.GetPerformanceMetrics.restype = ctypes.c_char_p
            self.cpp_lib.GetPerformanceMetrics.argtypes = [ctypes.c_void_p]
            
            # GetResponseHistory
            self.cpp_lib.GetResponseHistory.restype = ctypes.c_char_p
            self.cpp_lib.GetResponseHistory.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            # UpdateConfiguration
            self.cpp_lib.UpdateConfiguration.restype = ctypes.c_bool
            self.cpp_lib.UpdateConfiguration.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            # FreeMemory
            self.cpp_lib.FreeMemory.restype = None
            self.cpp_lib.FreeMemory.argtypes = [ctypes.c_char_p]
            
        except Exception as e:
            self.logger.error(f"Error setting up unified function signatures: {e}")
            raise
    
    def _convert_config_to_cpp_format(self) -> Dict[str, Any]:
        """Convert Python config to C++ format."""
        response_config = self.config.get("response", {})
        
        return {
            "security_level": 1,  # ENHANCED
            "base_directory": response_config.get("base_directory", "response_data"),
            "quarantine_directory": response_config.get("quarantine_directory", "response_data/quarantine"),
            "honeypot_directory": response_config.get("honeypot_directory", "response_data/honeypot"),
            "forensics_directory": response_config.get("forensics_directory", "response_data/forensics"),
            "backup_directory": response_config.get("backup_directory", "response_data/backup"),
            "reports_directory": response_config.get("reports_directory", "response_data/reports"),
            "encryption_enabled": response_config.get("encryption_enabled", True),
            "compression_enabled": response_config.get("compression_enabled", False),
            "blockchain_logging": response_config.get("blockchain_logging", False),
            "forensic_mode": response_config.get("forensic_mode", True),
            "real_time_monitoring": response_config.get("real_time_monitoring", True),
            "max_concurrent_operations": response_config.get("max_concurrent_operations", 10),
            "operation_timeout_seconds": response_config.get("operation_timeout_seconds", 300)
        }
    
    def _cleanup(self):
        """Cleanup C++ resources."""
        try:
            if self.engine_handle and self.cpp_lib:
                self.cpp_lib.ShutdownUnifiedResponseEngine(self.engine_handle)
                self.cpp_lib.DestroyUnifiedResponseEngine(self.engine_handle)
                self.engine_handle = None
        except Exception as e:
            self.logger.error(f"Error during unified cleanup: {e}")
    
    def __del__(self):
        """Destructor."""
        self._cleanup()

class UnifiedResponseSystem:
    """Unified Response System with integrated data management."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize Unified Response System."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.UnifiedResponseSystem")

        # Initialize C++ Unified Engine Interface
        self.cpp_interface = CPPUnifiedEngineInterface(config)

        # Unified configuration
        self.unified_config = config.get("unified_response", {})
        self.security_enabled = self.unified_config.get("security_enabled", True)
        self.encryption_enabled = self.unified_config.get("encryption_enabled", True)
        self.forensic_mode = self.unified_config.get("forensic_mode", True)
        self.real_time_monitoring = self.unified_config.get("real_time_monitoring", True)

        # Performance monitoring
        self.unified_metrics = {
            "total_operations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "quarantine_operations": 0,
            "honeypot_operations": 0,
            "data_operations": 0,
            "encryption_operations": 0,
            "cpp_enhanced_operations": 0,
            "average_operation_time_ms": 0.0,
            "total_data_processed_bytes": 0
        }

        # Operation tracking
        self.operation_history = []
        self.operation_lock = threading.RLock()

        self.logger.info("Unified Response System with C++ Core initialized successfully")

    async def process_threat(self, threat_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Process threat with unified response system."""
        try:
            self.logger.info(f"Processing threat: {threat_assessment.get('threat_id', 'unknown')}")

            # Try C++ core engine first
            if CPP_UNIFIED_CORE_AVAILABLE and self.cpp_interface.engine_handle:
                cpp_result = await self._process_with_cpp_engine(threat_assessment)
                if cpp_result.get("success", False):
                    self.logger.info("C++ unified engine processed threat successfully")
                    return cpp_result
                else:
                    self.logger.warning("C++ unified engine failed, using Python fallback")

            # Python fallback
            return await self._process_with_python_fallback(threat_assessment)

        except Exception as e:
            self.logger.error(f"Error in threat processing: {e}")
            return {"success": False, "error": str(e)}

    async def _process_with_cpp_engine(self, threat_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Process threat using C++ unified engine."""
        try:
            with self.cpp_interface.lock:
                if not self.cpp_interface.engine_handle:
                    return {"success": False, "error": "C++ unified engine not available"}

                # Convert threat assessment to JSON format for C++ engine
                threat_json = json.dumps(threat_assessment)
                
                # Call C++ engine
                result_ptr = self.cpp_interface.cpp_lib.ProcessThreatOperation(
                    self.cpp_interface.engine_handle,
                    threat_json.encode('utf-8')
                )

                if not result_ptr:
                    return {"success": False, "error": "C++ unified engine returned null"}

                # Convert result back to Python
                result_json = ctypes.string_at(result_ptr).decode('utf-8')
                self.cpp_interface.cpp_lib.FreeMemory(result_ptr)

                cpp_result = json.loads(result_json)
                
                # Add Python integration metadata
                cpp_result["cpp_integration"] = True
                cpp_result["unified_engine_version"] = "2.0.0"
                cpp_result["timestamp"] = datetime.now(timezone.utc).isoformat()

                # Update metrics
                self.unified_metrics["cpp_enhanced_operations"] += 1

                return cpp_result

        except Exception as e:
            self.logger.error(f"Error processing with C++ unified engine: {e}")
            return {"success": False, "error": str(e)}

    async def _process_with_python_fallback(self, threat_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Process threat using Python fallback."""
        try:
            # Determine optimal action based on threat assessment
            action = self._determine_optimal_action(threat_assessment)
            
            # Execute action
            if action == "quarantine":
                result = await self.quarantine_file(threat_assessment)
            elif action == "honeypot":
                result = await self.isolate_in_honeypot(threat_assessment)
            elif action == "alert":
                result = await self.send_alerts(threat_assessment)
            elif action == "block":
                result = await self.block_file(threat_assessment)
            else:
                result = await self.monitor_file(threat_assessment)
            
            # Update metrics
            self.unified_metrics["total_operations"] += 1
            if result.get("success", False):
                self.unified_metrics["successful_operations"] += 1
            else:
                self.unified_metrics["failed_operations"] += 1
            
            return result

        except Exception as e:
            self.logger.error(f"Error in Python fallback processing: {e}")
            return {"success": False, "error": str(e)}

    def _determine_optimal_action(self, threat_assessment: Dict[str, Any]) -> str:
        """Determine optimal action based on threat assessment."""
        threat_score = threat_assessment.get("threat_score", 0.0)
        threat_level = threat_assessment.get("threat_level", "unknown").lower()
        
        if threat_level in ["critical", "high"] or threat_score > 0.8:
            return "quarantine"
        elif threat_level == "medium" or threat_score > 0.6:
            return "honeypot"
        elif threat_level == "low" or threat_score > 0.3:
            return "alert"
        else:
            return "monitor"

    async def quarantine_file(self, threat_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Quarantine file with integrated data management."""
        try:
            self.logger.info(f"Quarantining file: {threat_assessment.get('file_path', 'unknown')}")
            
            # Try C++ engine first
            if CPP_UNIFIED_CORE_AVAILABLE and self.cpp_interface.engine_handle:
                cpp_result = await self._quarantine_with_cpp_engine(threat_assessment)
                if cpp_result.get("success", False):
                    return cpp_result
            
            # Python fallback
            return await self._quarantine_with_python_fallback(threat_assessment)
            
        except Exception as e:
            self.logger.error(f"Error in file quarantine: {e}")
            return {"success": False, "error": str(e)}

    async def _quarantine_with_cpp_engine(self, threat_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Quarantine file using C++ engine."""
        try:
            with self.cpp_interface.lock:
                threat_json = json.dumps(threat_assessment)
                
                result_ptr = self.cpp_interface.cpp_lib.QuarantineFileOperation(
                    self.cpp_interface.engine_handle,
                    threat_json.encode('utf-8')
                )

                if not result_ptr:
                    return {"success": False, "error": "C++ quarantine operation failed"}

                result_json = ctypes.string_at(result_ptr).decode('utf-8')
                self.cpp_interface.cpp_lib.FreeMemory(result_ptr)

                cpp_result = json.loads(result_json)
                cpp_result["cpp_integration"] = True
                
                # Update metrics
                self.unified_metrics["quarantine_operations"] += 1

                return cpp_result

        except Exception as e:
            self.logger.error(f"Error in C++ quarantine: {e}")
            return {"success": False, "error": str(e)}

    async def _quarantine_with_python_fallback(self, threat_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Quarantine file using Python fallback."""
        try:
            # Generate operation ID
            operation_id = self._generate_operation_id()
            
            # Create quarantine directory
            quarantine_dir = Path(self.config.get("response", {}).get("quarantine_directory", "quarantine"))
            quarantine_dir.mkdir(parents=True, exist_ok=True)
            
            # Move file to quarantine
            file_path = threat_assessment.get("file_path", "")
            if file_path and Path(file_path).exists():
                quarantine_path = quarantine_dir / f"{operation_id}_{Path(file_path).name}"
                
                # Move file
                import shutil
                shutil.move(file_path, quarantine_path)
                
                # Set restrictive permissions
                os.chmod(quarantine_path, 0o000)
                
                # Store metadata
                metadata_path = quarantine_dir / f"{operation_id}.meta"
                with open(metadata_path, 'w') as f:
                    json.dump(threat_assessment, f, indent=2)
                
                # Update metrics
                self.unified_metrics["quarantine_operations"] += 1
                
                return {
                    "success": True,
                    "operation_id": operation_id,
                    "action_taken": "quarantine",
                    "target_path": file_path,
                    "stored_path": str(quarantine_path),
                    "cpp_integration": False,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            else:
                return {"success": False, "error": "File not found or invalid path"}

        except Exception as e:
            self.logger.error(f"Error in Python quarantine fallback: {e}")
            return {"success": False, "error": str(e)}

    def _generate_operation_id(self) -> str:
        """Generate unique operation ID."""
        import uuid
        return f"OP_{int(time.time())}_{uuid.uuid4().hex[:8]}"

    async def isolate_in_honeypot(self, threat_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Isolate file in honeypot with integrated data management."""
        try:
            self.logger.info(f"Isolating in honeypot: {threat_assessment.get('file_path', 'unknown')}")
            
            # Try C++ engine first
            if CPP_UNIFIED_CORE_AVAILABLE and self.cpp_interface.engine_handle:
                cpp_result = await self._honeypot_with_cpp_engine(threat_assessment)
                if cpp_result.get("success", False):
                    return cpp_result
            
            # Python fallback
            return await self._honeypot_with_python_fallback(threat_assessment)
            
        except Exception as e:
            self.logger.error(f"Error in honeypot isolation: {e}")
            return {"success": False, "error": str(e)}

    async def _honeypot_with_cpp_engine(self, threat_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Isolate in honeypot using C++ engine."""
        try:
            with self.cpp_interface.lock:
                threat_json = json.dumps(threat_assessment)
                
                result_ptr = self.cpp_interface.cpp_lib.HoneypotIsolateOperation(
                    self.cpp_interface.engine_handle,
                    threat_json.encode('utf-8')
                )

                if not result_ptr:
                    return {"success": False, "error": "C++ honeypot operation failed"}

                result_json = ctypes.string_at(result_ptr).decode('utf-8')
                self.cpp_interface.cpp_lib.FreeMemory(result_ptr)

                cpp_result = json.loads(result_json)
                cpp_result["cpp_integration"] = True
                
                # Update metrics
                self.unified_metrics["honeypot_operations"] += 1

                return cpp_result

        except Exception as e:
            self.logger.error(f"Error in C++ honeypot: {e}")
            return {"success": False, "error": str(e)}

    async def _honeypot_with_python_fallback(self, threat_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Isolate in honeypot using Python fallback."""
        try:
            # Generate operation ID
            operation_id = self._generate_operation_id()
            
            # Create honeypot directory
            honeypot_dir = Path(self.config.get("response", {}).get("honeypot_directory", "honeypot"))
            honeypot_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy file to honeypot
            file_path = threat_assessment.get("file_path", "")
            if file_path and Path(file_path).exists():
                honeypot_path = honeypot_dir / f"{operation_id}_{Path(file_path).name}"
                
                # Copy file (don't move, keep original for analysis)
                import shutil
                shutil.copy2(file_path, honeypot_path)
                
                # Set read-only permissions
                os.chmod(honeypot_path, 0o444)
                
                # Store metadata
                metadata_path = honeypot_dir / f"{operation_id}.meta"
                with open(metadata_path, 'w') as f:
                    json.dump(threat_assessment, f, indent=2)
                
                # Update metrics
                self.unified_metrics["honeypot_operations"] += 1
                
                return {
                    "success": True,
                    "operation_id": operation_id,
                    "action_taken": "honeypot_isolate",
                    "target_path": file_path,
                    "stored_path": str(honeypot_path),
                    "cpp_integration": False,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            else:
                return {"success": False, "error": "File not found or invalid path"}

        except Exception as e:
            self.logger.error(f"Error in Python honeypot fallback: {e}")
            return {"success": False, "error": str(e)}

    async def send_alerts(self, threat_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Send alerts for threat."""
        try:
            self.logger.warning(f"THREAT ALERT: {threat_assessment.get('threat_level', 'unknown')} threat detected")
            self.logger.warning(f"File: {threat_assessment.get('file_path', 'unknown')}")
            self.logger.warning(f"Threats: {', '.join(threat_assessment.get('detected_threats', []))}")
            
            return {
                "success": True,
                "operation_id": self._generate_operation_id(),
                "action_taken": "alert",
                "alerts_sent": ["log"],
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error sending alerts: {e}")
            return {"success": False, "error": str(e)}

    async def block_file(self, threat_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Block file access."""
        try:
            file_path = threat_assessment.get("file_path", "")
            if file_path and Path(file_path).exists():
                # Set no permissions (block access)
                os.chmod(file_path, 0o000)
                
                return {
                    "success": True,
                    "operation_id": self._generate_operation_id(),
                    "action_taken": "block",
                    "target_path": file_path,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            else:
                return {"success": False, "error": "File not found"}
                
        except Exception as e:
            self.logger.error(f"Error blocking file: {e}")
            return {"success": False, "error": str(e)}

    async def monitor_file(self, threat_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Monitor file for suspicious activity."""
        try:
            self.logger.info(f"Monitoring file: {threat_assessment.get('file_path', 'unknown')}")
            
            return {
                "success": True,
                "operation_id": self._generate_operation_id(),
                "action_taken": "monitor",
                "target_path": threat_assessment.get("file_path", ""),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error monitoring file: {e}")
            return {"success": False, "error": str(e)}

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get unified performance metrics."""
        try:
            # Try to get C++ metrics first
            if CPP_UNIFIED_CORE_AVAILABLE and self.cpp_interface.engine_handle:
                with self.cpp_interface.lock:
                    metrics_ptr = self.cpp_interface.cpp_lib.GetPerformanceMetrics(
                        self.cpp_interface.engine_handle
                    )
                    
                    if metrics_ptr:
                        metrics_json = ctypes.string_at(metrics_ptr).decode('utf-8')
                        self.cpp_interface.cpp_lib.FreeMemory(metrics_ptr)
                        
                        cpp_metrics = json.loads(metrics_json)
                        cpp_metrics.update(self.unified_metrics)
                        return cpp_metrics
            
            # Return Python metrics
            return self.unified_metrics

        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {e}")
            return self.unified_metrics

    def get_response_history(self, threat_id: str = "") -> List[Dict[str, Any]]:
        """Get response operation history."""
        try:
            # Try to get C++ history first
            if CPP_UNIFIED_CORE_AVAILABLE and self.cpp_interface.engine_handle:
                with self.cpp_interface.lock:
                    history_ptr = self.cpp_interface.cpp_lib.GetResponseHistory(
                        self.cpp_interface.engine_handle,
                        threat_id.encode('utf-8')
                    )
                    
                    if history_ptr:
                        history_json = ctypes.string_at(history_ptr).decode('utf-8')
                        self.cpp_interface.cpp_lib.FreeMemory(history_ptr)
                        
                        return json.loads(history_json)
            
            # Return Python history
            with self.operation_lock:
                if threat_id:
                    return [op for op in self.operation_history if op.get("threat_id") == threat_id]
                return self.operation_history.copy()

        except Exception as e:
            self.logger.error(f"Error getting response history: {e}")
            return []
