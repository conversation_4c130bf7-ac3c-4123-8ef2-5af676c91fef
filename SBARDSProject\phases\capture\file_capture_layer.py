#!/usr/bin/env python3
"""
SBARDS File Capture Layer (طبقة مراقبة والتقاط الملفات)
Comprehensive file interception, monitoring, and secure storage system

This module implements:
- Real-time file download interception from any source/application
- Secure isolated temporary storage
- File metadata and hash extraction with verification
- Encrypted database storage
- Integration with static analysis layer
"""

import os
import sys
import hashlib
import shutil
import sqlite3
import json
import logging
import tempfile
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import mimetypes
import stat

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import C++ integration module
try:
    import sys
    sys.path.append('scanner_core')
    from cpp_integration import FileHashExtractor, SecureStorage
    CPP_AVAILABLE = True
except ImportError:
    CPP_AVAILABLE = False
    logging.warning("C++ integration not available, using Python fallback")

class FileCaptureLayer:
    """
    Advanced File Capture and Monitoring System
    Intercepts, analyzes, and securely stores all downloaded files
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the file capture layer."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.FileCaptureLayer")

        # Capture configuration
        self.capture_config = config.get("file_capture", {})

        # Initialize secure directories
        self._initialize_secure_directories()

        # Initialize database
        self._initialize_capture_database()

        # Initialize C++ components
        self._initialize_cpp_components()

        # Initialize file monitoring
        self._initialize_file_monitoring()

        # Monitoring state
        self.monitoring_active = False
        self.monitored_paths = set()

        self.logger.info("File Capture Layer initialized successfully")

    def _initialize_secure_directories(self):
        """Initialize secure isolated directories."""
        base_dir = Path(self.capture_config.get("base_directory", "capture_data"))

        # Secure temporary storage (completely isolated)
        self.secure_temp_dir = base_dir / "secure_temp"
        self.quarantine_dir = base_dir / "quarantine"
        self.honeypot_dir = base_dir / "honeypot"
        self.safe_storage_dir = base_dir / "safe_files"

        # Create directories with secure permissions
        for directory in [self.secure_temp_dir, self.quarantine_dir,
                         self.honeypot_dir, self.safe_storage_dir]:
            directory.mkdir(parents=True, exist_ok=True)
            # Set restrictive permissions (owner only)
            os.chmod(directory, 0o700)

        self.logger.info("Secure directories initialized")

    def _initialize_capture_database(self):
        """Initialize encrypted capture database."""
        self.db_path = self.secure_temp_dir.parent / "capture_database.db"

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Create main files table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS captured_files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_hash_sha256 TEXT UNIQUE NOT NULL,
                file_hash_md5 TEXT NOT NULL,
                file_hash_sha1 TEXT NOT NULL,
                original_path TEXT NOT NULL,
                temp_path TEXT NOT NULL,
                file_name TEXT NOT NULL,
                file_size INTEGER NOT NULL,
                file_extension TEXT NOT NULL,
                mime_type TEXT NOT NULL,
                file_permissions TEXT NOT NULL,
                capture_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                source_application TEXT,
                source_url TEXT,
                metadata_json TEXT,
                static_analysis_status TEXT DEFAULT 'pending',
                static_analysis_result TEXT,
                dynamic_analysis_status TEXT DEFAULT 'pending',
                dynamic_analysis_result TEXT,
                final_classification TEXT DEFAULT 'unknown',
                final_action TEXT DEFAULT 'pending',
                action_timestamp TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Create hash verification table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS hash_verification (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_hash_sha256 TEXT NOT NULL,
                verification_round INTEGER NOT NULL,
                hash_sha256_check TEXT NOT NULL,
                hash_md5_check TEXT NOT NULL,
                hash_sha1_check TEXT NOT NULL,
                verification_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                verification_status TEXT NOT NULL,
                FOREIGN KEY (file_hash_sha256) REFERENCES captured_files (file_hash_sha256)
            )
        ''')

        # Create action log table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS action_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_hash_sha256 TEXT NOT NULL,
                action_type TEXT NOT NULL,
                action_details TEXT,
                layer_name TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                success BOOLEAN NOT NULL,
                error_message TEXT,
                FOREIGN KEY (file_hash_sha256) REFERENCES captured_files (file_hash_sha256)
            )
        ''')

        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_file_hash ON captured_files(file_hash_sha256)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_capture_timestamp ON captured_files(capture_timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_static_status ON captured_files(static_analysis_status)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_dynamic_status ON captured_files(dynamic_analysis_status)')

        conn.commit()
        conn.close()

        self.logger.info("Capture database initialized")

    def _initialize_cpp_components(self):
        """Initialize C++ performance components."""
        if CPP_AVAILABLE:
            try:
                self.hash_extractor = FileHashExtractor()
                self.secure_storage = SecureStorage()
                self.logger.info("C++ components initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize C++ components: {e}")
                self.hash_extractor = None
                self.secure_storage = None
        else:
            self.hash_extractor = None
            self.secure_storage = None

    def _initialize_file_monitoring(self):
        """Initialize file system monitoring for downloads."""
        # Common download directories to monitor
        self.download_paths = [
            Path.home() / "Downloads",
            Path.home() / "Desktop",
            Path("C:/Users") / os.getenv("USERNAME", "") / "Downloads" if os.name == 'nt' else Path("/tmp"),
            # Add more paths as needed
        ]

        # Browser cache directories
        self.browser_cache_paths = [
            Path.home() / "AppData/Local/Google/Chrome/User Data/Default/Downloads" if os.name == 'nt' else Path.home() / ".cache/google-chrome",
            Path.home() / "AppData/Roaming/Mozilla/Firefox/Profiles" if os.name == 'nt' else Path.home() / ".mozilla/firefox",
            # Add more browser paths
        ]

        self.logger.info("File monitoring paths configured")

    def start_monitoring(self):
        """Start real-time file monitoring."""
        if self.monitoring_active:
            self.logger.warning("Monitoring already active")
            return

        self.monitoring_active = True

        # Start monitoring threads for different sources
        threading.Thread(target=self._monitor_download_directories, daemon=True).start()
        threading.Thread(target=self._monitor_browser_downloads, daemon=True).start()
        threading.Thread(target=self._monitor_network_downloads, daemon=True).start()

        self.logger.info("File monitoring started")

    def stop_monitoring(self):
        """Stop file monitoring."""
        self.monitoring_active = False
        self.logger.info("File monitoring stopped")

    def _monitor_download_directories(self):
        """Monitor standard download directories."""
        known_files = set()

        # Initialize known files
        for path in self.download_paths:
            if path.exists():
                for file_path in path.rglob("*"):
                    if file_path.is_file():
                        known_files.add(str(file_path))

        while self.monitoring_active:
            try:
                current_files = set()

                for path in self.download_paths:
                    if path.exists():
                        for file_path in path.rglob("*"):
                            if file_path.is_file():
                                current_files.add(str(file_path))

                # Detect new files
                new_files = current_files - known_files
                for file_path in new_files:
                    self._intercept_file(Path(file_path), "download_directory")

                known_files = current_files
                time.sleep(2)  # Check every 2 seconds

            except Exception as e:
                self.logger.error(f"Error monitoring download directories: {e}")
                time.sleep(5)

    def _monitor_browser_downloads(self):
        """Monitor browser download activities."""
        # This would integrate with browser APIs or monitor browser download folders
        # Implementation depends on specific browser integration requirements
        while self.monitoring_active:
            try:
                # Placeholder for browser-specific monitoring
                time.sleep(5)
            except Exception as e:
                self.logger.error(f"Error monitoring browser downloads: {e}")
                time.sleep(10)

    def _monitor_network_downloads(self):
        """Monitor network-based file downloads."""
        # This would integrate with network monitoring to catch downloads
        # from any application or service
        while self.monitoring_active:
            try:
                # Placeholder for network monitoring integration
                time.sleep(5)
            except Exception as e:
                self.logger.error(f"Error monitoring network downloads: {e}")
                time.sleep(10)

    def _intercept_file(self, file_path: Path, source: str) -> Dict[str, Any]:
        """
        Intercept and process a downloaded file.

        Args:
            file_path: Path to the intercepted file
            source: Source of the file (download_directory, browser, network, etc.)

        Returns:
            Dict containing interception results
        """
        try:
            self.logger.info(f"Intercepting file: {file_path} from {source}")

            # Step 1: Immediate isolation - move to secure temp directory
            secure_temp_path = self._move_to_secure_temp(file_path)
            if not secure_temp_path:
                return {"success": False, "error": "Failed to move file to secure location"}

            # Step 2: Extract file metadata and properties
            metadata = self._extract_file_metadata(secure_temp_path)

            # Step 3: Extract and verify file hashes (double verification)
            hash_results = self._extract_and_verify_hashes(secure_temp_path)
            if not hash_results["verified"]:
                return {"success": False, "error": "Hash verification failed"}

            # Step 4: Store in encrypted database
            db_result = self._store_in_database(
                secure_temp_path, file_path, metadata, hash_results, source
            )

            # Step 5: Log the interception action
            self._log_action(
                hash_results["sha256"],
                "file_intercepted",
                f"File intercepted from {source}",
                "capture_layer",
                True
            )

            # Step 6: Trigger static analysis
            self._trigger_static_analysis(hash_results["sha256"])

            return {
                "success": True,
                "file_hash": hash_results["sha256"],
                "secure_path": str(secure_temp_path),
                "metadata": metadata
            }

        except Exception as e:
            self.logger.error(f"Error intercepting file {file_path}: {e}")
            return {"success": False, "error": str(e)}

    def _move_to_secure_temp(self, file_path: Path) -> Optional[Path]:
        """Move file to secure temporary directory."""
        try:
            # Generate unique secure filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            secure_filename = f"captured_{timestamp}_{file_path.name}"
            secure_path = self.secure_temp_dir / secure_filename

            # Move file (cut operation to prevent original access)
            shutil.move(str(file_path), str(secure_path))

            # Set restrictive permissions
            os.chmod(secure_path, 0o600)  # Owner read/write only

            self.logger.info(f"File moved to secure location: {secure_path}")
            return secure_path

        except Exception as e:
            self.logger.error(f"Failed to move file to secure location: {e}")
            return None

    def _extract_file_metadata(self, file_path: Path) -> Dict[str, Any]:
        """Extract comprehensive file metadata."""
        try:
            stat_info = file_path.stat()
            return {
                "size": stat_info.st_size,
                "created": datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                "accessed": datetime.fromtimestamp(stat_info.st_atime).isoformat(),
                "permissions": oct(stat_info.st_mode)[-3:],
                "extension": file_path.suffix.lower(),
                "mime_type": "application/octet-stream"  # Default
            }
        except Exception as e:
            self.logger.error(f"Error extracting metadata: {e}")
            return {}

    def _extract_and_verify_hashes(self, file_path: Path) -> Dict[str, Any]:
        """Extract and verify file hashes with double verification."""
        try:
            if CPP_AVAILABLE and self.hash_extractor:
                # Use C++ accelerated hash extraction
                result = self.hash_extractor.extractHashes(str(file_path))
                return {
                    "sha256": result.sha256,
                    "sha1": result.sha1,
                    "md5": result.md5,
                    "verified": result.verified,
                    "extraction_time": result.extraction_time_ms
                }
            else:
                # Python fallback with double verification
                return self._python_hash_extraction(file_path)

        except Exception as e:
            self.logger.error(f"Error extracting hashes: {e}")
            return {"verified": False, "error": str(e)}

    def _python_hash_extraction(self, file_path: Path) -> Dict[str, Any]:
        """Python fallback for hash extraction with double verification."""
        import hashlib

        def calculate_hashes(path):
            sha256_hash = hashlib.sha256()
            sha1_hash = hashlib.sha1()
            md5_hash = hashlib.md5()

            with open(path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
                    sha1_hash.update(chunk)
                    md5_hash.update(chunk)

            return {
                "sha256": sha256_hash.hexdigest(),
                "sha1": sha1_hash.hexdigest(),
                "md5": md5_hash.hexdigest()
            }

        # First extraction
        hashes1 = calculate_hashes(file_path)
        # Second extraction for verification
        hashes2 = calculate_hashes(file_path)

        # Verify consistency
        verified = (hashes1["sha256"] == hashes2["sha256"] and
                   hashes1["sha1"] == hashes2["sha1"] and
                   hashes1["md5"] == hashes2["md5"])

        return {
            "sha256": hashes1["sha256"],
            "sha1": hashes1["sha1"],
            "md5": hashes1["md5"],
            "verified": verified
        }

    def _store_in_database(self, secure_path: Path, original_path: Path,
                          metadata: Dict[str, Any], hash_results: Dict[str, Any],
                          source: str) -> Dict[str, Any]:
        """Store file information in encrypted database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO captured_files
                (file_hash_sha256, file_hash_md5, file_hash_sha1, original_path, temp_path,
                 file_name, file_size, file_extension, mime_type, file_permissions,
                 source_application, metadata_json)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                hash_results["sha256"],
                hash_results["md5"],
                hash_results["sha1"],
                str(original_path),
                str(secure_path),
                original_path.name,
                metadata.get("size", 0),
                metadata.get("extension", ""),
                metadata.get("mime_type", ""),
                metadata.get("permissions", ""),
                source,
                json.dumps(metadata)
            ))

            conn.commit()
            conn.close()

            return {"success": True, "file_hash": hash_results["sha256"]}

        except Exception as e:
            self.logger.error(f"Error storing in database: {e}")
            return {"success": False, "error": str(e)}

    def _log_action(self, file_hash: str, action_type: str, details: str,
                   layer_name: str, success: bool, error_message: str = None):
        """Log action to database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO action_log
                (file_hash_sha256, action_type, action_details, layer_name, success, error_message)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (file_hash, action_type, details, layer_name, success, error_message))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error logging action: {e}")

    def _trigger_static_analysis(self, file_hash: str):
        """Trigger static analysis for the captured file."""
        try:
            # Update status to trigger static analysis
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE captured_files
                SET static_analysis_status = 'pending'
                WHERE file_hash_sha256 = ?
            """, (file_hash,))

            conn.commit()
            conn.close()

            self.logger.info(f"Static analysis triggered for: {file_hash}")

        except Exception as e:
            self.logger.error(f"Error triggering static analysis: {e}")
