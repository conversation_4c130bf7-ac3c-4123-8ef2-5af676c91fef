2025-05-30 21:35:07,505 - SBARDS.ComprehensiveResponse - INFO - Initializing C++ Response Integration...
2025-05-30 21:35:07,505 - SBARDS.CPPResponseIntegration - INFO - Initializing C++ Response Integration...
2025-05-30 21:35:07,505 - SBARDS.CPPResponseIntegration - ERROR - C++ response library not found
2025-05-30 21:35:07,505 - SBARDS.CPPResponseIntegration - ERROR - Failed to load C++ response library
2025-05-30 21:35:07,505 - SBARDS.CPPResponseIntegrationFactory - WARNING - Failed to create CPP Response Integration: Failed to initialize CPP Response Integration
2025-05-30 21:35:07,505 - SBARDS.ComprehensiveResponse - WARNING - C++ Response Integration failed to initialize, using Python fallback
2025-05-30 21:35:07,505 - SBARDS.ComprehensiveResponse - INFO - Initializing Blockchain Integration...
2025-05-30 21:35:07,521 - SBARDS.ComprehensiveResponse - WARNING - Blockchain integration initialization failed: ('Could not deserialize key data. The data may be in an incorrect format, the provided password may be incorrect, it may be encrypted with an unsupported algorithm, or it may be an unsupported key type (e.g. EC curves with explicit parameters).', [<OpenSSLError(code=478150756, lib=57, reason=100, reason_text=bad decrypt)>, <OpenSSLError(code=293601396, lib=35, reason=116, reason_text=pkcs12 cipherfinal error)>, <OpenSSLError(code=478150756, lib=57, reason=100, reason_text=bad decrypt)>, <OpenSSLError(code=293601396, lib=35, reason=116, reason_text=pkcs12 cipherfinal error)>])
2025-05-30 21:35:07,521 - SBARDS.ComprehensiveResponse - INFO - Continuing without blockchain integration
2025-05-30 21:35:07,525 - SBARDS.ComprehensiveResponse - INFO - Response directories initialized
2025-05-30 21:35:07,530 - SBARDS.ComprehensiveResponse - INFO - Response databases initialized
2025-05-30 21:35:07,530 - SBARDS.ComprehensiveResponse - INFO - Notification systems initialized
2025-05-30 21:35:07,530 - SBARDS.ComprehensiveResponse - INFO - Security systems initialized
2025-05-30 21:35:07,530 - SBARDS.ComprehensiveResponse - INFO - Initializing Blockchain Integration...
2025-05-30 21:35:07,536 - SBARDS.ComprehensiveResponse - WARNING - Blockchain integration initialization failed: ('Could not deserialize key data. The data may be in an incorrect format, the provided password may be incorrect, it may be encrypted with an unsupported algorithm, or it may be an unsupported key type (e.g. EC curves with explicit parameters).', [<OpenSSLError(code=478150756, lib=57, reason=100, reason_text=bad decrypt)>, <OpenSSLError(code=293601396, lib=35, reason=116, reason_text=pkcs12 cipherfinal error)>, <OpenSSLError(code=478150756, lib=57, reason=100, reason_text=bad decrypt)>, <OpenSSLError(code=293601396, lib=35, reason=116, reason_text=pkcs12 cipherfinal error)>])
2025-05-30 21:35:07,536 - SBARDS.ComprehensiveResponse - INFO - Continuing without blockchain integration
2025-05-30 21:35:07,536 - SBARDS.ComprehensiveResponse - INFO - ML model management initialized
2025-05-30 21:35:07,536 - SBARDS.ComprehensiveResponse - INFO - Comprehensive Response System initialized
2025-05-30 21:35:07,538 - SBARDS.MLModelsManager - INFO - Initializing model: threat_classifier
2025-05-30 21:35:07,543 - SBARDS.MLModelsManager - INFO - Loaded existing model: threat_classifier
2025-05-30 21:35:07,549 - SBARDS.MLModelsManager - INFO - Initializing model: malware_family_detector
2025-05-30 21:35:07,570 - SBARDS.MLModelsManager - INFO - Loaded existing model: malware_family_detector
2025-05-30 21:35:07,577 - SBARDS.MLModelsManager - INFO - Initializing model: anomaly_detector
2025-05-30 21:35:07,584 - SBARDS.MLModelsManager - INFO - Loaded existing model: anomaly_detector
2025-05-30 21:35:07,591 - SBARDS.MLModelsManager - INFO - Initializing model: behavioral_analyzer
2025-05-30 21:35:07,596 - SBARDS.MLModelsManager - INFO - Loaded existing model: behavioral_analyzer
2025-05-30 21:35:07,604 - SBARDS.MLModelsManager - INFO - Initializing model: evasion_detector
2025-05-30 21:35:07,625 - SBARDS.MLModelsManager - INFO - Loaded existing model: evasion_detector
2025-05-30 21:35:07,627 - SBARDS.MLModelsManager - INFO - Initializing model: response_optimizer
2025-05-30 21:35:07,628 - SBARDS.MLModelsManager - ERROR - Error creating model response_optimizer: Unknown algorithm: ensemble
2025-05-30 21:35:07,628 - SBARDS.MLModelsManager - INFO - Created new model: response_optimizer
2025-05-30 21:35:07,631 - SBARDS.MLModelsManager - INFO - Advanced ML Models Manager initialized successfully
2025-05-30 21:35:07,631 - SBARDS.EnhancedResponse - INFO - Enhanced Response System initialized successfully
2025-05-30 21:35:07,633 - SBARDS.EnhancedResponse - INFO - Processing enhanced analysis results with ML integration
2025-05-30 21:35:07,633 - SBARDS.MLModelsManager - ERROR - Error in threat level prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,633 - SBARDS.MLModelsManager - ERROR - Error in malware family detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,633 - SBARDS.MLModelsManager - ERROR - Error in evasion detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,633 - SBARDS.MLModelsManager - ERROR - Error in behavioral analysis: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,634 - SBARDS.MLModelsManager - ERROR - Error in anomaly detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,634 - SBARDS.EnhancedResponse - ERROR - Error in enhanced threat assessment: argument 1 (impossible<bad format char>)
2025-05-30 21:35:07,634 - SBARDS.ComprehensiveResponse - INFO - Executing safe file strategy for: C:\temp\sample_safe_file.exe
2025-05-30 21:35:07,648 - SBARDS.ComprehensiveResponse - INFO - Safe file database inserted: 286c60a5101fffa4e3a260f1c30ed0872d864efbe1ebe3e27f686bd3549d6aaa
2025-05-30 21:35:07,649 - SBARDS.ComprehensiveResponse - INFO - ML models updated with safe file data
2025-05-30 21:35:07,649 - SBARDS.ComprehensiveResponse - INFO - Normal access policies applied: C:\temp\sample_safe_file.exe
2025-05-30 21:35:07,657 - SBARDS.ComprehensiveResponse - INFO - Light monitoring setup for: C:\temp\sample_safe_file.exe
2025-05-30 21:35:07,658 - SBARDS.ComprehensiveResponse - INFO - Safe file notification: sample_safe_file.exe
2025-05-30 21:35:07,658 - SBARDS.ComprehensiveResponse - INFO - Safe file strategy completed successfully for: C:\temp\sample_safe_file.exe
2025-05-30 21:35:07,659 - SBARDS.EnhancedResponse - ERROR - Error in enhanced monitoring: argument 1 (impossible<bad format char>)
2025-05-30 21:35:07,659 - SBARDS.EnhancedResponse - ERROR - Error updating models with feedback: argument 1 (impossible<bad format char>)
2025-05-30 21:35:07,659 - SBARDS.EnhancedResponse - INFO - Enhanced analysis processing completed successfully
2025-05-30 21:35:07,660 - SBARDS.EnhancedResponse - INFO - Processing enhanced analysis results with ML integration
2025-05-30 21:35:07,660 - SBARDS.MLModelsManager - ERROR - Error in threat level prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,660 - SBARDS.MLModelsManager - ERROR - Error in malware family detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,660 - SBARDS.MLModelsManager - ERROR - Error in evasion detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,660 - SBARDS.MLModelsManager - ERROR - Error in behavioral analysis: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,661 - SBARDS.MLModelsManager - ERROR - Error in anomaly detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,661 - SBARDS.EnhancedResponse - ERROR - Error in enhanced threat assessment: argument 1 (impossible<bad format char>)
2025-05-30 21:35:07,661 - SBARDS.ComprehensiveResponse - INFO - Executing malicious file strategy for: C:\temp\sample_suspicious_file.exe
2025-05-30 21:35:07,671 - SBARDS.ComprehensiveResponse - CRITICAL - Malicious file contained: C:\temp\sample_suspicious_file.exe -> C:\Users\<USER>\Desktop\SBARDS\SBARDSProject\response_data\quarantine\malicious\2025\05\30\MAL_20250530_213507_4a8d2460.exe
2025-05-30 21:35:07,681 - SBARDS.ComprehensiveResponse - WARNING - Network connections isolated: 1 connections
2025-05-30 21:35:07,689 - SBARDS.ComprehensiveResponse - WARNING - Associated processes terminated: 1 processes
2025-05-30 21:35:07,694 - SBARDS.ComprehensiveResponse - INFO - Threat documentation generated: THR_20250530_213507_4a8d2460
2025-05-30 21:35:07,715 - SBARDS.ComprehensiveResponse - INFO - Forensic evidence collected: EVD_20250530_213507_4a8d2460
2025-05-30 21:35:07,715 - SBARDS.ComprehensiveResponse - INFO - Malicious file strategy completed successfully for: C:\temp\sample_suspicious_file.exe
2025-05-30 21:35:07,715 - SBARDS.EnhancedResponse - ERROR - Error in enhanced blocking: argument 1 (impossible<bad format char>)
2025-05-30 21:35:07,715 - SBARDS.EnhancedResponse - ERROR - Error updating models with feedback: argument 1 (impossible<bad format char>)
2025-05-30 21:35:07,715 - SBARDS.EnhancedResponse - INFO - Enhanced analysis processing completed successfully
2025-05-30 21:35:07,715 - SBARDS.EnhancedResponse - INFO - Processing enhanced analysis results with ML integration
2025-05-30 21:35:07,715 - SBARDS.MLModelsManager - ERROR - Error in threat level prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,715 - SBARDS.MLModelsManager - ERROR - Error in malware family detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,715 - SBARDS.MLModelsManager - ERROR - Error in evasion detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,715 - SBARDS.MLModelsManager - ERROR - Error in behavioral analysis: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,715 - SBARDS.MLModelsManager - ERROR - Error in anomaly detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,715 - SBARDS.EnhancedResponse - ERROR - Error in enhanced threat assessment: argument 1 (impossible<bad format char>)
2025-05-30 21:35:07,720 - SBARDS.ComprehensiveResponse - INFO - Executing malicious file strategy for: C:\temp\sample_malicious_file.exe
2025-05-30 21:35:07,729 - SBARDS.ComprehensiveResponse - CRITICAL - Malicious file contained: C:\temp\sample_malicious_file.exe -> C:\Users\<USER>\Desktop\SBARDS\SBARDSProject\response_data\quarantine\malicious\2025\05\30\MAL_20250530_213507_e5455536.exe
2025-05-30 21:35:07,730 - SBARDS.ComprehensiveResponse - WARNING - Network connections isolated: 1 connections
2025-05-30 21:35:07,730 - SBARDS.ComprehensiveResponse - WARNING - Associated processes terminated: 1 processes
2025-05-30 21:35:07,736 - SBARDS.ComprehensiveResponse - INFO - Threat documentation generated: THR_20250530_213507_e5455536
2025-05-30 21:35:07,756 - SBARDS.ComprehensiveResponse - INFO - Forensic evidence collected: EVD_20250530_213507_e5455536
2025-05-30 21:35:07,756 - SBARDS.ComprehensiveResponse - INFO - Malicious file strategy completed successfully for: C:\temp\sample_malicious_file.exe
2025-05-30 21:35:07,759 - SBARDS.EnhancedResponse - ERROR - Error in enhanced blocking: argument 1 (impossible<bad format char>)
2025-05-30 21:35:07,759 - SBARDS.EnhancedResponse - ERROR - Error updating models with feedback: argument 1 (impossible<bad format char>)
2025-05-30 21:35:07,759 - SBARDS.EnhancedResponse - INFO - Enhanced analysis processing completed successfully
2025-05-30 21:35:07,759 - SBARDS.EnhancedResponse - INFO - Processing enhanced analysis results with ML integration
2025-05-30 21:35:07,759 - SBARDS.MLModelsManager - ERROR - Error in threat level prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,759 - SBARDS.MLModelsManager - ERROR - Error in malware family detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,762 - SBARDS.MLModelsManager - ERROR - Error in evasion detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,762 - SBARDS.MLModelsManager - ERROR - Error in behavioral analysis: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,762 - SBARDS.MLModelsManager - ERROR - Error in anomaly detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:35:07,762 - SBARDS.EnhancedResponse - ERROR - Error in enhanced threat assessment: argument 1 (impossible<bad format char>)
2025-05-30 21:35:07,763 - SBARDS.ComprehensiveResponse - INFO - Executing malicious file strategy for: C:\temp\sample_critical_file.exe
2025-05-30 21:35:07,772 - SBARDS.ComprehensiveResponse - CRITICAL - Malicious file contained: C:\temp\sample_critical_file.exe -> C:\Users\<USER>\Desktop\SBARDS\SBARDSProject\response_data\quarantine\malicious\2025\05\30\MAL_20250530_213507_f268a4e0.exe
2025-05-30 21:35:07,773 - SBARDS.ComprehensiveResponse - WARNING - Network connections isolated: 1 connections
2025-05-30 21:35:07,774 - SBARDS.ComprehensiveResponse - WARNING - Associated processes terminated: 1 processes
2025-05-30 21:35:07,783 - SBARDS.ComprehensiveResponse - INFO - Threat documentation generated: THR_20250530_213507_f268a4e0
2025-05-30 21:35:07,802 - SBARDS.ComprehensiveResponse - INFO - Forensic evidence collected: EVD_20250530_213507_f268a4e0
2025-05-30 21:35:07,802 - SBARDS.ComprehensiveResponse - INFO - Malicious file strategy completed successfully for: C:\temp\sample_critical_file.exe
2025-05-30 21:35:07,802 - SBARDS.EnhancedResponse - ERROR - Error in enhanced blocking: argument 1 (impossible<bad format char>)
2025-05-30 21:35:07,803 - SBARDS.EnhancedResponse - ERROR - Error updating models with feedback: argument 1 (impossible<bad format char>)
2025-05-30 21:35:07,803 - SBARDS.EnhancedResponse - INFO - Enhanced analysis processing completed successfully
2025-05-30 21:35:07,804 - SBARDS.EnhancedResponse - ERROR - Error getting enhanced statistics: 'ComprehensiveResponseSystem' object has no attribute 'get_response_statistics'
2025-05-30 21:35:07,819 - SBARDS.MLModelsManager - INFO - ML Models Manager shutdown complete
2025-05-30 21:35:07,819 - SBARDS.EnhancedResponse - INFO - Enhanced Response System shutdown complete
