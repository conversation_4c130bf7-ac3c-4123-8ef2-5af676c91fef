/**
 * SBARDS Advanced API Hooking Framework
 * High-performance API monitoring and interception system
 *
 * Features:
 * - System call monitoring (Windows/Linux)
 * - Windows API hooking using Microsoft Detours
 * - Linux system call tracing using ptrace/eBPF
 * - Real-time event logging and analysis
 * - Parameter and return value capture
 */

#ifndef SBARDS_API_HOOKING_HPP
#define SBARDS_API_HOOKING_HPP

#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <unordered_set>
#include <chrono>
#include <thread>
#include <mutex>
#include <atomic>
#include <functional>
#include <queue>

#ifdef _WIN32
    #include <windows.h>
    #include <winternl.h>
    #include <psapi.h>
    #include <detours.h>
    #pragma comment(lib, "detours.lib")
#else
    #include <sys/ptrace.h>
    #include <sys/syscall.h>
    #include <sys/user.h>
    #include <sys/wait.h>
    #include <unistd.h>
    #include <signal.h>
#endif

namespace sbards {
namespace hooking {

/**
 * Hook types
 */
enum class HookType {
    SYSTEM_CALL,
    API_FUNCTION,
    LIBRARY_FUNCTION,
    KERNEL_FUNCTION
};

/**
 * System call information
 */
struct SystemCallInfo {
    long syscall_number;
    std::string syscall_name;
    std::vector<uintptr_t> arguments;
    long return_value;
    int error_code;
    std::chrono::microseconds duration;
    std::string call_stack;
};

/**
 * File access information
 */
struct FileAccessInfo {
    std::string operation; // create, open, read, write, delete, rename
    std::string file_path;
    std::string process_name;
    uint32_t access_mode;
    size_t bytes_transferred;
    bool is_sensitive_file;
    std::string access_pattern; // sequential, random, bulk
    bool encryption_detected;
};

/**
 * Network connection information
 */
struct NetworkConnectionInfo {
    std::string protocol; // TCP, UDP, ICMP
    std::string local_address;
    uint16_t local_port;
    std::string remote_address;
    uint16_t remote_port;
    std::string connection_state;
    size_t bytes_sent;
    size_t bytes_received;
    bool is_encrypted;
    bool is_suspicious;
    std::string c2_indicators;
};

/**
 * Registry/Configuration change information
 */
struct ConfigChangeInfo {
    std::string change_type; // registry, config_file, service, startup
    std::string key_path;
    std::string value_name;
    std::string old_value;
    std::string new_value;
    std::string process_name;
    bool is_security_related;
    bool is_persistence_mechanism;
};

/**
 * Enhanced hook event information
 */
struct HookEvent {
    std::chrono::system_clock::time_point timestamp;
    HookType type;
    std::string function_name;
    std::vector<std::string> parameters;
    std::string return_value;
    uint32_t process_id;
    uint32_t thread_id;
    std::string module_name;
    uintptr_t address;
    bool is_entry; // true for function entry, false for exit

    // Extended information
    SystemCallInfo syscall_info;
    FileAccessInfo file_info;
    NetworkConnectionInfo network_info;
    ConfigChangeInfo config_info;

    // Analysis metadata
    std::string threat_level; // low, medium, high, critical
    std::vector<std::string> indicators;
    std::string call_chain;
    std::unordered_map<std::string, std::string> metadata;
};

/**
 * Advanced hook configuration
 */
struct HookConfig {
    // Basic monitoring
    bool monitor_syscalls;
    bool monitor_winapi;
    bool monitor_file_operations;
    bool monitor_network_operations;
    bool monitor_process_operations;
    bool monitor_registry_operations;
    bool log_parameters;
    bool log_return_values;

    // Advanced monitoring
    bool kernel_level_hooks;
    bool deep_call_analysis;
    bool call_sequence_tracking;
    bool parameter_validation;
    bool return_value_analysis;

    // File monitoring
    bool track_file_patterns;
    bool detect_encryption_operations;
    bool monitor_sensitive_files;
    bool bulk_operation_detection;
    std::vector<std::string> sensitive_file_paths;
    std::vector<std::string> monitored_extensions;

    // Network monitoring
    bool packet_capture;
    bool protocol_analysis;
    bool tls_inspection;
    bool c2_detection;
    bool dns_monitoring;
    std::vector<std::string> suspicious_domains;
    std::vector<std::string> blocked_ips;

    // Registry/Config monitoring
    bool registry_monitoring;
    bool config_file_monitoring;
    bool service_monitoring;
    bool startup_monitoring;
    std::vector<std::string> critical_registry_keys;
    std::vector<std::string> monitored_config_files;

    // Performance and limits
    std::unordered_set<std::string> target_functions;
    std::unordered_set<std::string> target_modules;
    uint32_t max_events;
    uint32_t max_call_depth;
    std::chrono::seconds event_timeout;
    std::chrono::milliseconds hook_timeout;

    // Analysis settings
    bool real_time_analysis;
    bool threat_scoring;
    bool ioc_extraction;
    std::string analysis_mode; // passive, active, aggressive
};

/**
 * Hook statistics
 */
struct HookStats {
    uint64_t total_events;
    uint64_t syscall_events;
    uint64_t api_events;
    uint64_t file_events;
    uint64_t network_events;
    uint64_t process_events;
    uint64_t registry_events;
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point last_event_time;
};

/**
 * API Hooking Engine
 */
class APIHookingEngine {
public:
    /**
     * Constructor
     * @param config Hook configuration
     */
    explicit APIHookingEngine(const HookConfig& config);

    /**
     * Destructor
     */
    ~APIHookingEngine();

    /**
     * Initialize the hooking engine
     * @return true if initialization successful
     */
    bool initialize();

    /**
     * Start hooking for a specific process
     * @param process_id Target process ID
     * @return true if hooking started successfully
     */
    bool start_hooking(uint32_t process_id);

    /**
     * Stop hooking
     */
    void stop_hooking();

    /**
     * Check if hooking is active
     * @return true if hooking is active
     */
    bool is_hooking() const;

    /**
     * Get captured events
     * @return Vector of hook events
     */
    std::vector<HookEvent> get_events() const;

    /**
     * Get events since a specific timestamp
     * @param since Timestamp to filter from
     * @return Vector of hook events
     */
    std::vector<HookEvent> get_events_since(std::chrono::system_clock::time_point since) const;

    /**
     * Clear all captured events
     */
    void clear_events();

    /**
     * Get hooking statistics
     * @return Hook statistics
     */
    HookStats get_statistics() const;

    /**
     * Add function to hook list
     * @param function_name Name of function to hook
     * @param module_name Module containing the function
     */
    void add_function_hook(const std::string& function_name, const std::string& module_name = "");

    /**
     * Remove function from hook list
     * @param function_name Name of function to unhook
     */
    void remove_function_hook(const std::string& function_name);

    /**
     * Set event callback
     * @param callback Function to call when events occur
     */
    void set_event_callback(std::function<void(const HookEvent&)> callback);

private:
    // Configuration
    HookConfig config_;

    // State management
    std::atomic<bool> initialized_;
    std::atomic<bool> hooking_active_;
    std::atomic<bool> stop_requested_;

    // Target process
    uint32_t target_process_id_;

    // Synchronization
    mutable std::mutex events_mutex_;
    mutable std::mutex stats_mutex_;

    // Event storage
    std::queue<HookEvent> events_;
    HookStats stats_;

    // Callback
    std::function<void(const HookEvent&)> event_callback_;

    // Monitoring thread
    std::thread monitoring_thread_;

    // Platform-specific data
#ifdef _WIN32
    HANDLE target_process_handle_;
    std::vector<void*> hooked_functions_;
    std::unordered_map<std::string, void*> original_functions_;
#else
    pid_t target_pid_;
    bool ptrace_attached_;
#endif

    // Private methods
    bool initialize_windows_hooks();
    bool initialize_linux_hooks();

    void monitoring_loop();
    void process_hook_event(const HookEvent& event);

    bool install_function_hooks();
    void uninstall_function_hooks();

    void monitor_system_calls();
    void monitor_api_functions();

    std::string format_parameters(const std::vector<uintptr_t>& params);
    std::string get_module_name(uintptr_t address);

    void log_hook_event(const HookEvent& event);

#ifdef _WIN32
    // Windows-specific hook functions
    static LONG WINAPI exception_filter(EXCEPTION_POINTERS* exception_info);
    static void hook_function(const std::string& function_name, void* hook_func, void** original_func);

    // Hooked function implementations
    static HANDLE WINAPI hooked_CreateFileW(LPCWSTR lpFileName, DWORD dwDesiredAccess,
                                           DWORD dwShareMode, LPSECURITY_ATTRIBUTES lpSecurityAttributes,
                                           DWORD dwCreationDisposition, DWORD dwFlagsAndAttributes,
                                           HANDLE hTemplateFile);

    static BOOL WINAPI hooked_WriteFile(HANDLE hFile, LPCVOID lpBuffer, DWORD nNumberOfBytesToWrite,
                                       LPDWORD lpNumberOfBytesWritten, LPOVERLAPPED lpOverlapped);

    static BOOL WINAPI hooked_ReadFile(HANDLE hFile, LPVOID lpBuffer, DWORD nNumberOfBytesToRead,
                                      LPDWORD lpNumberOfBytesRead, LPOVERLAPPED lpOverlapped);

    static HANDLE WINAPI hooked_CreateProcessW(LPCWSTR lpApplicationName, LPWSTR lpCommandLine,
                                              LPSECURITY_ATTRIBUTES lpProcessAttributes,
                                              LPSECURITY_ATTRIBUTES lpThreadAttributes,
                                              BOOL bInheritHandles, DWORD dwCreationFlags,
                                              LPVOID lpEnvironment, LPCWSTR lpCurrentDirectory,
                                              LPSTARTUPINFOW lpStartupInfo,
                                              LPPROCESS_INFORMATION lpProcessInformation);

    static LONG WINAPI hooked_RegSetValueExW(HKEY hKey, LPCWSTR lpValueName, DWORD Reserved,
                                            DWORD dwType, const BYTE* lpData, DWORD cbData);

    static SOCKET WINAPI hooked_socket(int af, int type, int protocol);
    static int WINAPI hooked_connect(SOCKET s, const struct sockaddr* name, int namelen);
    static int WINAPI hooked_send(SOCKET s, const char* buf, int len, int flags);
    static int WINAPI hooked_recv(SOCKET s, char* buf, int len, int flags);
#else
    // Linux-specific methods
    void handle_syscall(pid_t pid, long syscall_num, long* args, long return_value);
    std::string get_syscall_name(long syscall_num);
    std::vector<std::string> format_syscall_args(long syscall_num, long* args);
#endif

    // Static instance for callbacks
    static APIHookingEngine* instance_;
};

/**
 * System Call Monitor (Linux-specific)
 */
class SystemCallMonitor {
public:
    explicit SystemCallMonitor(uint32_t process_id);
    ~SystemCallMonitor();

    bool start_monitoring();
    void stop_monitoring();
    bool is_monitoring() const;

    std::vector<HookEvent> get_syscall_events() const;
    void set_event_callback(std::function<void(const HookEvent&)> callback);

private:
    uint32_t process_id_;
    std::atomic<bool> monitoring_;
    std::thread monitor_thread_;
    std::vector<HookEvent> events_;
    mutable std::mutex events_mutex_;
    std::function<void(const HookEvent&)> callback_;

    void monitor_loop();
    void handle_syscall_event(long syscall_num, long* args, long return_value);
};

/**
 * Advanced File Access Monitor
 */
class FileAccessMonitor {
public:
    explicit FileAccessMonitor(const HookConfig& config);
    ~FileAccessMonitor();

    bool start_monitoring();
    void stop_monitoring();
    bool is_monitoring() const;

    std::vector<FileAccessInfo> get_file_events() const;
    void set_event_callback(std::function<void(const FileAccessInfo&)> callback);

    // Pattern analysis
    std::string analyze_access_pattern(const std::vector<FileAccessInfo>& events);
    bool detect_encryption_activity(const std::vector<FileAccessInfo>& events);
    bool detect_bulk_operations(const std::vector<FileAccessInfo>& events);

private:
    HookConfig config_;
    std::atomic<bool> monitoring_;
    std::vector<FileAccessInfo> events_;
    mutable std::mutex events_mutex_;
    std::function<void(const FileAccessInfo&)> callback_;

    // File pattern tracking
    std::unordered_map<std::string, std::vector<FileAccessInfo>> file_access_history_;
    std::unordered_set<std::string> sensitive_files_;

    bool is_sensitive_file(const std::string& file_path);
    std::string determine_access_pattern(const std::vector<FileAccessInfo>& accesses);
    bool is_encryption_operation(const FileAccessInfo& access);

#ifdef _WIN32
    void monitor_windows_file_operations();
    static void CALLBACK file_completion_routine(DWORD error_code, DWORD bytes_transferred, LPOVERLAPPED overlapped);
#else
    void monitor_linux_file_operations();
    void setup_inotify_monitoring();
#endif
};

/**
 * Advanced Network Monitor
 */
class NetworkMonitor {
public:
    explicit NetworkMonitor(const HookConfig& config);
    ~NetworkMonitor();

    bool start_monitoring();
    void stop_monitoring();
    bool is_monitoring() const;

    std::vector<NetworkConnectionInfo> get_network_events() const;
    void set_event_callback(std::function<void(const NetworkConnectionInfo&)> callback);

    // Protocol analysis
    bool analyze_dns_traffic(const std::vector<uint8_t>& packet_data);
    bool analyze_http_traffic(const std::vector<uint8_t>& packet_data);
    bool analyze_tls_traffic(const std::vector<uint8_t>& packet_data);

    // C2 detection
    bool detect_c2_communication(const NetworkConnectionInfo& connection);
    std::vector<std::string> extract_c2_indicators(const std::vector<NetworkConnectionInfo>& connections);

private:
    HookConfig config_;
    std::atomic<bool> monitoring_;
    std::vector<NetworkConnectionInfo> events_;
    mutable std::mutex events_mutex_;
    std::function<void(const NetworkConnectionInfo&)> callback_;

    // Network analysis
    std::unordered_map<std::string, std::vector<NetworkConnectionInfo>> connection_history_;
    std::unordered_set<std::string> suspicious_domains_;
    std::unordered_set<std::string> blocked_ips_;

    bool is_suspicious_domain(const std::string& domain);
    bool is_encrypted_connection(const NetworkConnectionInfo& connection);
    void analyze_connection_patterns();

#ifdef _WIN32
    void monitor_windows_network();
    void setup_winsock_hooks();
#else
    void monitor_linux_network();
    void setup_netfilter_hooks();
#endif

    // Packet analysis
    void process_packet(const uint8_t* packet_data, size_t packet_size);
    void analyze_dns_packet(const uint8_t* packet_data, size_t packet_size);
    void analyze_http_packet(const uint8_t* packet_data, size_t packet_size);
    void analyze_tls_packet(const uint8_t* packet_data, size_t packet_size);
};

/**
 * Registry and Configuration Monitor
 */
class ConfigurationMonitor {
public:
    explicit ConfigurationMonitor(const HookConfig& config);
    ~ConfigurationMonitor();

    bool start_monitoring();
    void stop_monitoring();
    bool is_monitoring() const;

    std::vector<ConfigChangeInfo> get_config_events() const;
    void set_event_callback(std::function<void(const ConfigChangeInfo&)> callback);

    // Analysis methods
    bool detect_persistence_mechanisms(const std::vector<ConfigChangeInfo>& changes);
    bool detect_security_modifications(const std::vector<ConfigChangeInfo>& changes);
    std::vector<std::string> analyze_startup_modifications(const std::vector<ConfigChangeInfo>& changes);

private:
    HookConfig config_;
    std::atomic<bool> monitoring_;
    std::vector<ConfigChangeInfo> events_;
    mutable std::mutex events_mutex_;
    std::function<void(const ConfigChangeInfo&)> callback_;

    // Configuration tracking
    std::unordered_map<std::string, std::string> registry_baseline_;
    std::unordered_map<std::string, std::string> config_file_baseline_;
    std::unordered_set<std::string> critical_keys_;

    bool is_critical_registry_key(const std::string& key_path);
    bool is_security_related_change(const ConfigChangeInfo& change);
    bool is_persistence_mechanism(const ConfigChangeInfo& change);

#ifdef _WIN32
    void monitor_windows_registry();
    void setup_registry_hooks();
    static LONG WINAPI hooked_RegSetValueExW(HKEY hKey, LPCWSTR lpValueName, DWORD Reserved,
                                             DWORD dwType, const BYTE* lpData, DWORD cbData);
    static LONG WINAPI hooked_RegCreateKeyExW(HKEY hKey, LPCWSTR lpSubKey, DWORD Reserved,
                                              LPWSTR lpClass, DWORD dwOptions, REGSAM samDesired,
                                              LPSECURITY_ATTRIBUTES lpSecurityAttributes,
                                              PHKEY phkResult, LPDWORD lpdwDisposition);
#else
    void monitor_linux_config_files();
    void setup_config_file_monitoring();
#endif
};

/**
 * Windows API Monitor (Windows-specific)
 */
class WindowsAPIMonitor {
public:
    explicit WindowsAPIMonitor(uint32_t process_id);
    ~WindowsAPIMonitor();

    bool start_monitoring();
    void stop_monitoring();
    bool is_monitoring() const;

    std::vector<HookEvent> get_api_events() const;
    void set_event_callback(std::function<void(const HookEvent&)> callback);

private:
    uint32_t process_id_;
    std::atomic<bool> monitoring_;
    std::vector<HookEvent> events_;
    mutable std::mutex events_mutex_;
    std::function<void(const HookEvent&)> callback_;

    bool install_detours();
    void uninstall_detours();
};

} // namespace hooking
} // namespace sbards

#endif // SBARDS_API_HOOKING_HPP
