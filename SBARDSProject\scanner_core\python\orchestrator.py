import os
import json
import csv
import platform
import logging
import concurrent.futures
import time
from datetime import datetime
from typing import List, Dict, Any
import sys

# Add parent directory to path to import utils
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))
from scanner_core.utils import Config<PERSON>oader, Logger, FileScanner, DownloadMonitor
from scanner_core.python.yara_wrapper import YaraScanner

class Orchestrator:
    """
    Main orchestrator for the SBARDS Project pre-scanning phase.
    Coordinates file scanning, YARA rule matching, and result reporting.
    """

    def __init__(self, config_path="config.json"):
        """
        Initialize the orchestrator.

        Args:
            config_path (str): Path to the configuration file
        """
        # Load configuration
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.get_config()

        # Set up logging
        log_dir = self.config["output"]["log_directory"]
        log_level = self.config["output"]["log_level"]
        self.logger_setup = Logger(log_dir, log_level)
        self.logger = self.logger_setup.get_logger()

        # Set up directories
        self.output_dir = os.path.abspath(self.config["output"]["output_directory"])
        os.makedirs(self.output_dir, exist_ok=True)

        # Initialize components
        self.file_scanner = FileScanner(self.config["scanner"])

        # Set up performance parameters
        self.threads = self.config["performance"]["threads"]
        self.batch_size = self.config["performance"]["batch_size"]

        # Initialize YARA scanner for reuse
        rule_files = self.config["rules"]["rule_files"]
        categories = self.config["rules"]["enable_categories"]
        self.yara_scanner = YaraScanner(rule_files, categories)

        # Initialize download monitor if enabled
        self.download_monitor = None
        if self.config.get("features", {}).get("monitor_downloads", False):
            self.logger.info("Download monitoring is enabled")
            self.download_monitor = DownloadMonitor(self.config, self.yara_scanner)

        self.logger.info(f"Orchestrator initialized on {platform.system()} platform")
        self.logger.info(f"Target directory: {self.config['scanner']['target_directory']}")
        self.logger.info(f"Rule files: {self.config['rules']['rule_files']}")

    def run_scan(self):
        """
        Run the complete scanning process.
        """
        self.logger.info("Starting scan process")

        # Start download monitoring if enabled
        if self.download_monitor:
            self.logger.info("Starting download monitoring")
            self.download_monitor.start_monitoring()

        try:
            # Get files to scan
            files_to_scan = self.file_scanner.scan()
            if not files_to_scan:
                self.logger.warning("No files found to scan")
                return {}

            # Scan files using the already initialized YARA scanner
            if self.threads > 1:
                self.logger.info(f"Using {self.threads} threads for scanning")
                scan_results = self._parallel_scan(self.yara_scanner, files_to_scan)
            else:
                self.logger.info("Using single-threaded scanning")
                scan_results = self._sequential_scan(self.yara_scanner, files_to_scan)

            # Generate reports
            self._generate_reports(scan_results)

            self.logger.info(f"Scan complete. Results saved to {self.output_dir}")
            return scan_results

        except Exception as e:
            self.logger.error(f"Error during scan process: {e}")
            return {}

    def start_download_monitoring(self):
        """
        Start monitoring downloads without running a full scan.
        """
        if self.download_monitor:
            self.logger.info("Starting download monitoring")
            self.download_monitor.start_monitoring()
            return True
        else:
            self.logger.warning("Download monitoring is not enabled in configuration")
            return False

    def stop_download_monitoring(self):
        """
        Stop monitoring downloads.
        """
        if self.download_monitor:
            self.logger.info("Stopping download monitoring")
            self.download_monitor.stop_monitoring()
            return True
        else:
            return False

    def _sequential_scan(self, scanner, files):
        """
        Scan files sequentially.

        Args:
            scanner (YaraScanner): YARA scanner instance
            files (List[str]): List of files to scan

        Returns:
            Dict[str, List[Dict]]: Dictionary mapping file paths to scan results
        """
        results = {}
        total_files = len(files)

        for i, file_path in enumerate(files):
            self.logger.info(f"Scanning file {i+1}/{total_files}: {file_path}")
            try:
                matches = scanner.scan_file(file_path)
                results[file_path] = matches

                if matches:
                    self.logger.info(f"Found {len(matches)} matches in {file_path}")
                    for match in matches:
                        self.logger.info(f"  - Rule: {match['rule']} (Namespace: {match['namespace']})")
            except Exception as e:
                self.logger.error(f"Error scanning {file_path}: {e}")

        return results

    def _parallel_scan(self, scanner, files):
        """
        Scan files in parallel using a thread pool with advanced memory management.

        Args:
            scanner (YaraScanner): YARA scanner instance
            files (List[str]): List of files to scan

        Returns:
            Dict[str, List[Dict]]: Dictionary mapping file paths to scan results
        """
        import psutil
        import time
        import gc

        results = {}
        total_files = len(files)
        processed_files = 0

        # Get performance configuration
        threads = self.threads
        batch_size = self.batch_size
        adaptive_threading = self.config["performance"].get("adaptive_threading", False)
        max_memory_mb = self.config["performance"].get("max_memory_mb", 1024)
        priority_extensions = self.config["performance"].get("priority_extensions", [])

        # Memory management settings
        memory_check_interval = 10  # Check memory every N files
        gc_threshold = 85  # Run garbage collection when memory usage exceeds this percentage
        critical_memory_threshold = 95  # Emergency measures when memory exceeds this percentage

        # Sort files by size and priority for better memory management
        file_info = []
        for file_path in files:
            try:
                size = os.path.getsize(file_path)
                _, ext = os.path.splitext(file_path.lower())
                priority = 1 if ext in priority_extensions else 0
                file_info.append((file_path, size, priority))
            except Exception as e:
                self.logger.warning(f"Error getting file size for {file_path}: {e}")
                file_info.append((file_path, 0, 0))

        # Sort files: first by priority (high to low), then by size (small to large)
        sorted_files = [f[0] for f in sorted(file_info, key=lambda x: (-x[2], x[1]))]

        if priority_extensions:
            priority_count = sum(1 for f in file_info if f[2] > 0)
            self.logger.info(f"Prioritizing {priority_count} files with extensions: {priority_extensions}")

        # Calculate dynamic batch size based on file sizes
        total_size = sum(f[1] for f in file_info)
        avg_size = total_size / len(files) if files else 0

        # Adjust batch size for very large files
        if avg_size > 10 * 1024 * 1024:  # If average file size > 10MB
            adjusted_batch_size = max(5, batch_size // 2)
            self.logger.info(f"Adjusting batch size from {batch_size} to {adjusted_batch_size} due to large average file size ({avg_size/1024/1024:.2f} MB)")
            batch_size = adjusted_batch_size

        # Process files in batches with dynamic memory management
        for i in range(0, total_files, batch_size):
            batch = sorted_files[i:i+batch_size]
            batch_results = {}

            # Check memory before starting a new batch
            memory_info = psutil.virtual_memory()
            memory_percent = memory_info.percent
            memory_available_mb = memory_info.available / (1024 * 1024)

            self.logger.info(f"Memory status before batch {i//batch_size + 1}: {memory_percent:.1f}% used, {memory_available_mb:.1f} MB available")

            # Determine if we need emergency memory measures
            if memory_percent > critical_memory_threshold:
                self.logger.critical(f"CRITICAL MEMORY USAGE: {memory_percent:.1f}%. Taking emergency measures.")
                # Force garbage collection
                gc.collect()
                # Reduce batch size dramatically
                current_batch_size = len(batch)
                reduced_batch_size = max(1, current_batch_size // 4)
                self.logger.warning(f"Reducing current batch from {current_batch_size} to {reduced_batch_size} files")
                batch = batch[:reduced_batch_size]
                # Use only 1 thread
                adjusted_threads = 1
                # Wait for memory to be freed
                time.sleep(10)
            # Adjust thread count based on memory usage
            elif adaptive_threading:
                if memory_percent > 90:
                    adjusted_threads = max(1, threads // 4)
                    self.logger.warning(f"High memory usage ({memory_percent:.1f}%). Reducing threads to {adjusted_threads}")
                elif memory_percent > 75:
                    adjusted_threads = max(2, threads // 2)
                    self.logger.warning(f"Elevated memory usage ({memory_percent:.1f}%). Reducing threads to {adjusted_threads}")
                else:
                    adjusted_threads = threads
            else:
                adjusted_threads = threads

            self.logger.info(f"Processing batch {i//batch_size + 1}/{(total_files + batch_size - 1)//batch_size} with {adjusted_threads} threads")

            # Calculate estimated memory needed for this batch
            batch_size_mb = sum(os.path.getsize(f) for f in batch if os.path.exists(f)) / (1024 * 1024)
            self.logger.info(f"Batch total size: {batch_size_mb:.2f} MB")

            # Process the batch with controlled parallelism
            with concurrent.futures.ThreadPoolExecutor(max_workers=adjusted_threads) as executor:
                # Create a dictionary mapping futures to file paths
                future_to_file = {}
                active_futures = set()
                max_concurrent = adjusted_threads * 2  # Control active futures

                # Submit and process tasks with controlled concurrency
                j = 0
                while j < len(batch) or active_futures:
                    # Submit new tasks if we have capacity and files left
                    while j < len(batch) and len(active_futures) < max_concurrent:
                        file_path = batch[j]
                        # Check file size before processing
                        try:
                            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
                            if file_size_mb > self.config["scanner"]["max_file_size_mb"]:
                                self.logger.warning(f"Skipping large file: {file_path} ({file_size_mb:.2f} MB)")
                                j += 1
                                continue
                        except Exception as e:
                            self.logger.warning(f"Error checking file size: {e}")

                        future = executor.submit(scanner.scan_file, file_path)
                        future_to_file[future] = file_path
                        active_futures.add(future)
                        j += 1

                    # Process completed futures
                    done_futures = set()
                    for future in list(active_futures):
                        if future.done():
                            file_path = future_to_file[future]
                            processed_files += 1

                            try:
                                matches = future.result()
                                batch_results[file_path] = matches

                                # Log progress
                                if processed_files % 10 == 0 or processed_files == total_files:
                                    self.logger.info(f"Progress: {processed_files}/{total_files} files scanned ({processed_files/total_files*100:.1f}%)")

                                # Log matches
                                if matches:
                                    self.logger.info(f"Found {len(matches)} matches in {file_path}")
                                    for match in matches:
                                        self.logger.info(f"  - Rule: {match['rule']} (Namespace: {match.get('namespace', 'N/A')})")
                            except Exception as e:
                                self.logger.error(f"Error scanning {file_path}: {e}")

                            done_futures.add(future)

                            # Check memory periodically
                            if processed_files % memory_check_interval == 0:
                                current_memory = psutil.virtual_memory().percent
                                if current_memory > gc_threshold:
                                    self.logger.warning(f"Memory usage at {current_memory:.1f}%. Running garbage collection.")
                                    gc.collect()

                    # Remove processed futures
                    active_futures -= done_futures

                    # If we still have active futures, wait a bit
                    if active_futures:
                        time.sleep(0.1)

            # Update results with batch results
            results.update(batch_results)

            # Memory management between batches
            if i + batch_size < total_files:
                # Force garbage collection between batches
                gc.collect()

                # Check memory after batch processing
                memory_percent = psutil.virtual_memory().percent
                self.logger.info(f"Memory usage after batch: {memory_percent:.1f}%")

                # Pause if memory usage is high
                if memory_percent > 85:
                    pause_seconds = min(30, int(memory_percent - 80) * 2)  # Scale pause time with memory pressure
                    self.logger.warning(f"High memory usage ({memory_percent:.1f}%). Pausing for {pause_seconds} seconds before next batch")
                    time.sleep(pause_seconds)

                    # Check if memory was freed
                    new_memory_percent = psutil.virtual_memory().percent
                    if new_memory_percent > memory_percent:
                        self.logger.critical(f"Memory usage increased during pause: {new_memory_percent:.1f}%. Taking emergency measures.")
                        # Emergency measures: reduce batch size for next batch
                        batch_size = max(1, batch_size // 2)
                        self.logger.warning(f"Reducing batch size to {batch_size} for next batch")
                        # Wait longer
                        time.sleep(30)

        # Final garbage collection
        gc.collect()

        return results

    def _generate_reports(self, scan_results):
        """
        Generate reports from scan results.

        Args:
            scan_results (Dict[str, List[Dict]]): Dictionary mapping file paths to scan results
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Generate JSON report
        if self.config["output"]["json_output"]:
            json_path = os.path.join(self.output_dir, f"scan_results_{timestamp}.json")
            self._save_json_report(scan_results, json_path)

        # Generate CSV report
        if self.config["output"]["csv_output"]:
            csv_path = os.path.join(self.output_dir, f"scan_results_{timestamp}.csv")
            self._save_csv_report(scan_results, csv_path)

        # Generate HTML report
        if self.config["output"]["html_report"]:
            html_path = os.path.join(self.output_dir, f"scan_report_{timestamp}.html")
            self._save_html_report(scan_results, html_path)

    def _save_json_report(self, scan_results, output_path):
        """
        Save scan results as a JSON file.

        Args:
            scan_results (Dict[str, List[Dict]]): Dictionary mapping file paths to scan results
            output_path (str): Path to save the JSON file
        """
        try:
            # Convert scan results to a more report-friendly format
            report_data = {
                "scan_time": datetime.now().isoformat(),
                "platform": platform.system(),
                "target_directory": self.config["scanner"]["target_directory"],
                "rule_files": self.config["rules"]["rule_files"],
                "total_files_scanned": len(scan_results),
                "files_with_matches": sum(1 for matches in scan_results.values() if matches),
                "results": {
                    file_path: matches for file_path, matches in scan_results.items() if matches
                }
            }

            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(report_data, f, indent=2)

            self.logger.info(f"JSON report saved to {output_path}")
        except Exception as e:
            self.logger.error(f"Error saving JSON report: {e}")

    def _save_csv_report(self, scan_results, output_path):
        """
        Save scan results as a CSV file.

        Args:
            scan_results (Dict[str, List[Dict]]): Dictionary mapping file paths to scan results
            output_path (str): Path to save the CSV file
        """
        try:
            with open(output_path, "w", encoding="utf-8", newline="") as f:
                writer = csv.writer(f)
                writer.writerow(["File", "Rule", "Namespace", "Category", "Severity", "Description"])

                for file_path, matches in scan_results.items():
                    if not matches:
                        continue

                    for match in matches:
                        writer.writerow([
                            file_path,
                            match["rule"],
                            match["namespace"],
                            match["meta"].get("category", ""),
                            match["meta"].get("severity", ""),
                            match["meta"].get("description", "")
                        ])

            self.logger.info(f"CSV report saved to {output_path}")
        except Exception as e:
            self.logger.error(f"Error saving CSV report: {e}")

    def _save_html_report(self, scan_results, output_path):
        """
        Save scan results as an enhanced HTML report with visualizations.

        Args:
            scan_results (Dict[str, List[Dict]]): Dictionary mapping file paths to scan results
            output_path (str): Path to save the HTML file
        """
        try:
            # Count matches by category
            categories = {}
            severities = {"high": 0, "medium": 0, "low": 0, "info": 0, "unknown": 0}
            rule_counts = {}
            extension_counts = {}
            directory_counts = {}

            # Count total matches
            total_matches = 0
            files_with_matches = 0

            for file_path, matches in scan_results.items():
                if matches:
                    files_with_matches += 1
                    total_matches += len(matches)

                    # Get file extension and directory
                    _, ext = os.path.splitext(file_path.lower())
                    directory = os.path.dirname(file_path)

                    # Count by extension
                    extension_counts[ext] = extension_counts.get(ext, 0) + len(matches)

                    # Count by directory (use only the last directory name)
                    dir_name = os.path.basename(directory) or directory
                    directory_counts[dir_name] = directory_counts.get(dir_name, 0) + len(matches)

                    # Count by category, severity, and rule
                    for match in matches:
                        category = match["meta"].get("category", "unknown")
                        severity = match["meta"].get("severity", "unknown").lower()
                        rule_name = match["rule"]

                        categories[category] = categories.get(category, 0) + 1
                        severities[severity if severity in severities else "unknown"] += 1
                        rule_counts[rule_name] = rule_counts.get(rule_name, 0) + 1

            # Sort dictionaries by value (descending)
            sorted_categories = sorted(categories.items(), key=lambda x: x[1], reverse=True)
            sorted_rules = sorted(rule_counts.items(), key=lambda x: x[1], reverse=True)
            sorted_extensions = sorted(extension_counts.items(), key=lambda x: x[1], reverse=True)
            sorted_directories = sorted(directory_counts.items(), key=lambda x: x[1], reverse=True)

            # Generate chart data
            category_data = ", ".join(str(count) for _, count in sorted_categories[:10])
            category_labels = ", ".join(f"'{category}'" for category, _ in sorted_categories[:10])

            severity_data = ", ".join(str(severities[sev]) for sev in ["high", "medium", "low", "info", "unknown"])
            severity_labels = "'High', 'Medium', 'Low', 'Info', 'Unknown'"

            rule_data = ", ".join(str(count) for _, count in sorted_rules[:10])
            rule_labels = ", ".join(f"'{rule}'" for rule, _ in sorted_rules[:10])

            # Generate HTML with charts
            html = f"""<!DOCTYPE html>
<html>
<head>
    <title>SBARDS Enhanced Scan Report</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; color: #333; background-color: #f8f9fa; }}
        h1, h2, h3 {{ color: #2c3e50; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .header {{ background-color: #3498db; color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .header h1 {{ margin: 0; }}
        .header p {{ margin: 5px 0; }}
        table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; background-color: white; }}
        th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
        th {{ background-color: #3498db; color: white; }}
        tr:nth-child(even) {{ background-color: #f2f2f2; }}
        tr:hover {{ background-color: #e9f7fe; }}
        .high {{ color: #e74c3c; font-weight: bold; }}
        .medium {{ color: #f39c12; }}
        .low {{ color: #3498db; }}
        .info {{ color: #2ecc71; }}
        .summary {{ display: flex; flex-wrap: wrap; justify-content: space-between; margin-bottom: 20px; }}
        .summary-box {{ border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 10px; flex: 1; min-width: 200px; background-color: white; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
        .summary-box h3 {{ margin-top: 0; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
        .chart-container {{ display: flex; flex-wrap: wrap; justify-content: space-between; margin-bottom: 20px; }}
        .chart-box {{ border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 10px; flex: 1; min-width: 45%; background-color: white; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
        .filters {{ margin-bottom: 20px; padding: 15px; background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
        .filters input, .filters select {{ padding: 8px; margin-right: 10px; border: 1px solid #ddd; border-radius: 3px; }}
        .badge {{ display: inline-block; padding: 3px 7px; border-radius: 10px; font-size: 12px; font-weight: bold; }}
        .badge-high {{ background-color: #e74c3c; color: white; }}
        .badge-medium {{ background-color: #f39c12; color: white; }}
        .badge-low {{ background-color: #3498db; color: white; }}
        .badge-info {{ background-color: #2ecc71; color: white; }}
        .badge-unknown {{ background-color: #95a5a6; color: white; }}
        .footer {{ text-align: center; margin-top: 30px; padding: 20px; color: #7f8c8d; font-size: 12px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SBARDS Enhanced Scan Report</h1>
            <p>Scan completed on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
            <p>Platform: {platform.system()}</p>
            <p>Target Directory: {self.config["scanner"]["target_directory"]}</p>
        </div>

        <div class="summary">
            <div class="summary-box">
                <h3>Scan Summary</h3>
                <p><strong>Total Files Scanned:</strong> {len(scan_results)}</p>
                <p><strong>Files with Matches:</strong> {files_with_matches}</p>
                <p><strong>Total Matches:</strong> {total_matches}</p>
                <p><strong>Scan Date:</strong> {datetime.now().strftime("%Y-%m-%d")}</p>
                <p><strong>Scan Time:</strong> {datetime.now().strftime("%H:%M:%S")}</p>
            </div>
            <div class="summary-box">
                <h3>Severity Breakdown</h3>
                <p><span class="badge badge-high">High</span> {severities["high"]}</p>
                <p><span class="badge badge-medium">Medium</span> {severities["medium"]}</p>
                <p><span class="badge badge-low">Low</span> {severities["low"]}</p>
                <p><span class="badge badge-info">Info</span> {severities["info"]}</p>
                <p><span class="badge badge-unknown">Unknown</span> {severities["unknown"]}</p>
            </div>
            <div class="summary-box">
                <h3>Top Categories</h3>
                {"".join(f"<p><strong>{category}:</strong> {count}</p>" for category, count in sorted_categories[:5])}
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-box">
                <h3>Matches by Category</h3>
                <canvas id="categoryChart"></canvas>
            </div>
            <div class="chart-box">
                <h3>Matches by Severity</h3>
                <canvas id="severityChart"></canvas>
            </div>
            <div class="chart-box">
                <h3>Top Rules</h3>
                <canvas id="ruleChart"></canvas>
            </div>
            <div class="chart-box">
                <h3>Matches by File Type</h3>
                <canvas id="extensionChart"></canvas>
            </div>
        </div>

        <div class="filters">
            <h3>Filter Results</h3>
            <input type="text" id="fileFilter" placeholder="Filter by filename" onkeyup="filterTable()">
            <select id="severityFilter" onchange="filterTable()">
                <option value="">All Severities</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
                <option value="info">Info</option>
            </select>
            <select id="categoryFilter" onchange="filterTable()">
                <option value="">All Categories</option>
                {"".join(f'<option value="{category}">{category}</option>' for category in categories.keys())}
            </select>
        </div>

        <h2>Detailed Results</h2>
        <table id="resultsTable">
            <thead>
                <tr>
                    <th onclick="sortTable(0)">File ↕</th>
                    <th onclick="sortTable(1)">Rule ↕</th>
                    <th onclick="sortTable(2)">Category ↕</th>
                    <th onclick="sortTable(3)">Severity ↕</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
    """

            # Add rows for each match
            for file_path, matches in scan_results.items():
                if not matches:
                    continue

                for match in matches:
                    category = match["meta"].get("category", "unknown")
                    severity = match["meta"].get("severity", "unknown").lower()
                    description = match["meta"].get("description", "")

                    severity_class = severity if severity in ["high", "medium", "low", "info"] else "unknown"

                    html += f"""
                <tr data-severity="{severity}" data-category="{category}">
                    <td>{os.path.basename(file_path)}<br><small>{os.path.dirname(file_path)}</small></td>
                    <td>{match["rule"]}</td>
                    <td>{category}</td>
                    <td class="{severity_class}"><span class="badge badge-{severity_class}">{severity.upper()}</span></td>
                    <td>{description}</td>
                </tr>"""

            html += """
            </tbody>
        </table>

        <div class="footer">
            <p>SBARDS Project - Pre-scanning Phase Report - Generated by the SBARDS Orchestrator</p>
        </div>
    </div>

    <script>
        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            // Category chart
            var ctxCategory = document.getElementById('categoryChart').getContext('2d');
            var categoryChart = new Chart(ctxCategory, {
                type: 'bar',
                data: {
                    labels: [""" + category_labels + """],
                    datasets: [{
                        label: 'Matches by Category',
                        data: [""" + category_data + """],
                        backgroundColor: [
                            '#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6',
                            '#1abc9c', '#d35400', '#34495e', '#16a085', '#c0392b'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Severity chart
            var ctxSeverity = document.getElementById('severityChart').getContext('2d');
            var severityChart = new Chart(ctxSeverity, {
                type: 'pie',
                data: {
                    labels: [""" + severity_labels + """],
                    datasets: [{
                        data: [""" + severity_data + """],
                        backgroundColor: [
                            '#e74c3c', '#f39c12', '#3498db', '#2ecc71', '#95a5a6'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true
                }
            });

            // Rule chart
            var ctxRule = document.getElementById('ruleChart').getContext('2d');
            var ruleChart = new Chart(ctxRule, {
                type: 'bar',
                data: {
                    labels: [""" + rule_labels + """],
                    datasets: [{
                        label: 'Matches by Rule',
                        data: [""" + rule_data + """],
                        backgroundColor: '#3498db',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Extension chart
            var ctxExtension = document.getElementById('extensionChart').getContext('2d');
            var extensionChart = new Chart(ctxExtension, {
                type: 'doughnut',
                data: {
                    labels: [""" + ", ".join(f"'{ext or 'no extension'}'" for ext, _ in sorted_extensions[:10]) + """],
                    datasets: [{
                        data: [""" + ", ".join(str(count) for _, count in sorted_extensions[:10]) + """],
                        backgroundColor: [
                            '#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6',
                            '#1abc9c', '#d35400', '#34495e', '#16a085', '#c0392b'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true
                }
            });
        });

        // Table filtering
        function filterTable() {
            var fileFilter = document.getElementById('fileFilter').value.toLowerCase();
            var severityFilter = document.getElementById('severityFilter').value.toLowerCase();
            var categoryFilter = document.getElementById('categoryFilter').value.toLowerCase();

            var rows = document.getElementById('resultsTable').getElementsByTagName('tbody')[0].rows;

            for (var i = 0; i < rows.length; i++) {
                var fileName = rows[i].cells[0].textContent.toLowerCase();
                var severity = rows[i].getAttribute('data-severity').toLowerCase();
                var category = rows[i].getAttribute('data-category').toLowerCase();

                var showRow = true;

                if (fileFilter && !fileName.includes(fileFilter)) {
                    showRow = false;
                }

                if (severityFilter && severity !== severityFilter) {
                    showRow = false;
                }

                if (categoryFilter && category !== categoryFilter) {
                    showRow = false;
                }

                rows[i].style.display = showRow ? '' : 'none';
            }
        }

        // Table sorting
        function sortTable(n) {
            var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
            table = document.getElementById('resultsTable');
            switching = true;
            dir = 'asc';

            while (switching) {
                switching = false;
                rows = table.rows;

                for (i = 1; i < (rows.length - 1); i++) {
                    shouldSwitch = false;
                    x = rows[i].getElementsByTagName('TD')[n];
                    y = rows[i + 1].getElementsByTagName('TD')[n];

                    if (dir == 'asc') {
                        if (x.textContent.toLowerCase() > y.textContent.toLowerCase()) {
                            shouldSwitch = true;
                            break;
                        }
                    } else if (dir == 'desc') {
                        if (x.textContent.toLowerCase() < y.textContent.toLowerCase()) {
                            shouldSwitch = true;
                            break;
                        }
                    }
                }

                if (shouldSwitch) {
                    rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                    switching = true;
                    switchcount++;
                } else {
                    if (switchcount == 0 && dir == 'asc') {
                        dir = 'desc';
                        switching = true;
                    }
                }
            }
        }
    </script>
</body>
</html>"""

            with open(output_path, "w", encoding="utf-8") as f:
                f.write(html)

            self.logger.info(f"Enhanced HTML report saved to {output_path}")
        except Exception as e:
            self.logger.error(f"Error saving HTML report: {e}")

def run_scan(monitor_only=False):
    """
    Run the orchestrator with default configuration.

    Args:
        monitor_only (bool): If True, only start download monitoring without scanning
    """
    orchestrator = Orchestrator()

    if monitor_only:
        # Start download monitoring only
        if orchestrator.start_download_monitoring():
            print("Download monitoring started. Press Ctrl+C to stop.")
            try:
                # Keep the script running
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("Stopping download monitoring...")
                orchestrator.stop_download_monitoring()
                print("Download monitoring stopped.")
        else:
            print("Download monitoring is not enabled in configuration.")
    else:
        # Run normal scan
        return orchestrator.run_scan()

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="SBARDS Pre-scanning Tool")
    parser.add_argument("--monitor-only", action="store_true", help="Only monitor downloads without scanning")
    args = parser.parse_args()

    run_scan(monitor_only=args.monitor_only)