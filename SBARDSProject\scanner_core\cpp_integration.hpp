#ifndef SBARDS_CPP_INTEGRATION_HPP
#define SBARDS_CPP_INTEGRATION_HPP

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <chrono>
#include <thread>
#include <mutex>
#include <atomic>
#include <fstream>
#include <sstream>
#include <iomanip>

// OpenSSL for cryptographic operations
#include <openssl/sha.h>
#include <openssl/md5.h>
#include <openssl/evp.h>
#include <openssl/aes.h>

// System monitoring includes
#ifdef _WIN32
    #include <windows.h>
    #include <psapi.h>
    #include <tlhelp32.h>
    #include <winsock2.h>
    #include <iphlpapi.h>
#else
    #include <unistd.h>
    #include <sys/types.h>
    #include <sys/stat.h>
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <dirent.h>
#endif

namespace SBARDS {

/**
 * High-performance file hash extraction with verification
 * استخراج الهاش عالي الأداء مع التحقق المزدوج
 */
class FileHashExtractor {
public:
    struct HashResult {
        std::string sha256;
        std::string sha1;
        std::string md5;
        bool verified;
        std::string error_message;
        double extraction_time_ms;
    };

    FileHashExtractor();
    ~FileHashExtractor();

    /**
     * Extract file hashes with double verification
     * استخراج هاش الملف مع التحقق المزدوج
     */
    HashResult extractHashes(const std::string& file_path);

    /**
     * Verify hash integrity by re-extraction
     * التحقق من سلامة الهاش عبر الاستخراج المتكرر
     */
    bool verifyHashIntegrity(const std::string& file_path, const HashResult& original_hash);

private:
    std::string calculateSHA256(const std::string& file_path);
    std::string calculateSHA1(const std::string& file_path);
    std::string calculateMD5(const std::string& file_path);
    std::string bytesToHex(const unsigned char* bytes, size_t length);
};

/**
 * Secure encrypted storage for sensitive data
 * التخزين المشفر الآمن للبيانات الحساسة
 */
class SecureStorage {
public:
    struct EncryptionResult {
        std::vector<uint8_t> encrypted_data;
        std::vector<uint8_t> iv;
        bool success;
        std::string error_message;
    };

    SecureStorage();
    ~SecureStorage();

    /**
     * Encrypt sensitive file data
     * تشفير بيانات الملف الحساسة
     */
    EncryptionResult encryptData(const std::vector<uint8_t>& data);

    /**
     * Decrypt stored data
     * فك تشفير البيانات المخزنة
     */
    std::vector<uint8_t> decryptData(const EncryptionResult& encrypted_data);

    /**
     * Secure file deletion with multiple overwrites
     * حذف آمن للملف مع الكتابة المتعددة
     */
    bool secureDelete(const std::string& file_path);

private:
    std::vector<uint8_t> encryption_key;
    void generateEncryptionKey();
    bool initializeOpenSSL();
};

/**
 * Real-time system monitoring for dynamic analysis
 * مراقبة النظام في الوقت الفعلي للتحليل الديناميكي
 */
class SystemMonitor {
public:
    struct ProcessInfo {
        uint32_t pid;
        std::string name;
        std::string command_line;
        uint32_t parent_pid;
        double cpu_usage;
        uint64_t memory_usage;
        std::chrono::system_clock::time_point start_time;
    };

    struct NetworkConnection {
        std::string local_address;
        uint16_t local_port;
        std::string remote_address;
        uint16_t remote_port;
        std::string protocol;
        std::string state;
        uint32_t pid;
        std::chrono::system_clock::time_point timestamp;
    };

    struct FileOperation {
        std::string file_path;
        std::string operation_type; // create, modify, delete, access
        uint32_t pid;
        std::string process_name;
        std::chrono::system_clock::time_point timestamp;
    };

    struct SystemResources {
        double cpu_usage_percent;
        uint64_t memory_total;
        uint64_t memory_used;
        uint64_t memory_available;
        double memory_usage_percent;
        uint64_t disk_read_bytes;
        uint64_t disk_write_bytes;
        uint64_t network_bytes_sent;
        uint64_t network_bytes_received;
        std::chrono::system_clock::time_point timestamp;
    };

    SystemMonitor();
    ~SystemMonitor();

    /**
     * Start comprehensive system monitoring
     * بدء مراقبة النظام الشاملة
     */
    bool startMonitoring();

    /**
     * Stop system monitoring
     * إيقاف مراقبة النظام
     */
    void stopMonitoring();

    /**
     * Get current running processes
     * الحصول على العمليات الجارية حالياً
     */
    std::vector<ProcessInfo> getCurrentProcesses();

    /**
     * Get active network connections
     * الحصول على الاتصالات الشبكية النشطة
     */
    std::vector<NetworkConnection> getNetworkConnections();

    /**
     * Get recent file operations
     * الحصول على عمليات الملفات الحديثة
     */
    std::vector<FileOperation> getFileOperations();

    /**
     * Get current system resource usage
     * الحصول على استخدام موارد النظام الحالي
     */
    SystemResources getSystemResources();

    /**
     * Check for suspicious activities
     * فحص الأنشطة المشبوهة
     */
    std::vector<std::string> detectSuspiciousActivities();

private:
    std::atomic<bool> monitoring_active;
    std::thread monitoring_thread;
    std::mutex data_mutex;

    // Monitoring data storage
    std::vector<ProcessInfo> process_history;
    std::vector<NetworkConnection> network_history;
    std::vector<FileOperation> file_operations_history;
    std::vector<SystemResources> resource_history;

    // Platform-specific monitoring functions
    void monitoringLoop();
    void updateProcessList();
    void updateNetworkConnections();
    void updateFileOperations();
    void updateSystemResources();

#ifdef _WIN32
    std::vector<ProcessInfo> getWindowsProcesses();
    std::vector<NetworkConnection> getWindowsNetworkConnections();
#else
    std::vector<ProcessInfo> getLinuxProcesses();
    std::vector<NetworkConnection> getLinuxNetworkConnections();
#endif
};

/**
 * Sandbox environment for safe file execution
 * بيئة الحماية لتشغيل الملفات بأمان
 */
class SandboxEnvironment {
public:
    struct SandboxConfig {
        std::string sandbox_type; // "container", "vm", "process"
        bool network_isolation;
        bool filesystem_isolation;
        uint32_t memory_limit_mb;
        uint32_t cpu_limit_percent;
        uint32_t execution_timeout_seconds;
        std::string working_directory;
        std::vector<std::string> allowed_directories;
        std::vector<std::string> blocked_directories;
    };

    struct ExecutionResult {
        bool success;
        int exit_code;
        std::string stdout_output;
        std::string stderr_output;
        double execution_time_seconds;
        std::vector<std::string> detected_behaviors;
        std::string error_message;
    };

    SandboxEnvironment(const SandboxConfig& config);
    ~SandboxEnvironment();

    /**
     * Execute file in isolated sandbox
     * تشغيل الملف في بيئة معزولة
     */
    ExecutionResult executeFile(const std::string& file_path);

    /**
     * Monitor sandbox for malicious behavior
     * مراقبة البيئة المعزولة للسلوك الخبيث
     */
    std::vector<std::string> monitorBehavior();

    /**
     * Clean up sandbox environment
     * تنظيف بيئة الحماية
     */
    bool cleanup();

private:
    SandboxConfig config;
    std::unique_ptr<SystemMonitor> monitor;
    std::string sandbox_id;
    bool sandbox_active;

    bool setupSandbox();
    bool teardownSandbox();
    std::vector<std::string> analyzeBehavior(const std::vector<SystemMonitor::ProcessInfo>& processes,
                                           const std::vector<SystemMonitor::NetworkConnection>& connections,
                                           const std::vector<SystemMonitor::FileOperation>& file_ops);
};

/**
 * Behavioral analysis engine for threat detection
 * محرك تحليل السلوك لكشف التهديدات
 */
class BehavioralAnalyzer {
public:
    struct ThreatIndicator {
        std::string indicator_type;
        std::string description;
        int severity_score; // 0-100
        std::vector<std::string> evidence;
        std::chrono::system_clock::time_point detection_time;
    };

    struct AnalysisResult {
        std::vector<ThreatIndicator> threats;
        int overall_risk_score; // 0-100
        std::string risk_level; // "LOW", "MEDIUM", "HIGH", "CRITICAL"
        std::string recommendation;
        bool is_ransomware;
        bool is_malware;
        bool is_suspicious;
    };

    BehavioralAnalyzer();
    ~BehavioralAnalyzer();

    /**
     * Analyze system behavior for threats
     * تحليل سلوك النظام للتهديدات
     */
    AnalysisResult analyzeBehavior(const std::vector<SystemMonitor::ProcessInfo>& processes,
                                 const std::vector<SystemMonitor::NetworkConnection>& connections,
                                 const std::vector<SystemMonitor::FileOperation>& file_operations,
                                 const std::vector<SystemMonitor::SystemResources>& resources);

    /**
     * Check for ransomware indicators
     * فحص مؤشرات فيروس الفدية
     */
    std::vector<ThreatIndicator> detectRansomwareIndicators(
        const std::vector<SystemMonitor::ProcessInfo>& processes,
        const std::vector<SystemMonitor::FileOperation>& file_operations);

    /**
     * Check for malware indicators
     * فحص مؤشرات البرمجيات الخبيثة
     */
    std::vector<ThreatIndicator> detectMalwareIndicators(
        const std::vector<SystemMonitor::ProcessInfo>& processes,
        const std::vector<SystemMonitor::NetworkConnection>& connections);

    /**
     * Analyze resource usage patterns
     * تحليل أنماط استخدام الموارد
     */
    std::vector<ThreatIndicator> analyzeResourcePatterns(
        const std::vector<SystemMonitor::SystemResources>& resources);

private:
    // Threat detection patterns
    std::vector<std::string> ransomware_file_extensions;
    std::vector<std::string> suspicious_process_names;
    std::vector<std::string> malicious_network_patterns;
    std::vector<uint16_t> suspicious_ports;

    void loadThreatPatterns();
    bool isRansomwareFileExtension(const std::string& extension);
    bool isSuspiciousProcess(const std::string& process_name);
    bool isSuspiciousNetworkActivity(const SystemMonitor::NetworkConnection& connection);
    int calculateRiskScore(const std::vector<ThreatIndicator>& threats);
    std::string determineRiskLevel(int risk_score);
    std::string generateRecommendation(const AnalysisResult& result);
};

} // namespace SBARDS

#endif // SBARDS_CPP_INTEGRATION_HPP
