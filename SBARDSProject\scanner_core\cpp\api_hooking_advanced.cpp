/**
 * SBARDS Advanced API Hooking Implementation
 * Comprehensive system call monitoring, file access tracking, network analysis
 */

#include "api_hooking.hpp"
#include <iostream>
#include <sstream>
#include <fstream>
#include <algorithm>
#include <regex>
#include <iomanip>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #include <iphlpapi.h>
    #include <wininet.h>
    #pragma comment(lib, "ws2_32.lib")
    #pragma comment(lib, "iphlpapi.lib")
    #pragma comment(lib, "wininet.lib")
#else
    #include <sys/socket.h>
    #include <sys/inotify.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <netdb.h>
    #include <linux/netfilter.h>
    #include <libnetfilter_queue/libnetfilter_queue.h>
#endif

namespace sbards {
namespace hooking {

// FileAccessMonitor Implementation
FileAccessMonitor::FileAccessMonitor(const HookConfig& config)
    : config_(config), monitoring_(false) {
    
    // Initialize sensitive file paths
    for (const auto& path : config_.sensitive_file_paths) {
        sensitive_files_.insert(path);
    }
    
    // Add default sensitive paths
    sensitive_files_.insert("/etc/passwd");
    sensitive_files_.insert("/etc/shadow");
    sensitive_files_.insert("/etc/hosts");
    sensitive_files_.insert("C:\\Windows\\System32\\config\\SAM");
    sensitive_files_.insert("C:\\Windows\\System32\\config\\SECURITY");
    sensitive_files_.insert("C:\\Windows\\System32\\config\\SYSTEM");
}

FileAccessMonitor::~FileAccessMonitor() {
    stop_monitoring();
}

bool FileAccessMonitor::start_monitoring() {
    if (monitoring_) {
        return true;
    }
    
    try {
#ifdef _WIN32
        monitor_windows_file_operations();
#else
        monitor_linux_file_operations();
#endif
        monitoring_ = true;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to start file monitoring: " << e.what() << std::endl;
        return false;
    }
}

void FileAccessMonitor::stop_monitoring() {
    monitoring_ = false;
}

bool FileAccessMonitor::is_monitoring() const {
    return monitoring_;
}

std::vector<FileAccessInfo> FileAccessMonitor::get_file_events() const {
    std::lock_guard<std::mutex> lock(events_mutex_);
    return events_;
}

void FileAccessMonitor::set_event_callback(std::function<void(const FileAccessInfo&)> callback) {
    callback_ = callback;
}

std::string FileAccessMonitor::analyze_access_pattern(const std::vector<FileAccessInfo>& events) {
    if (events.empty()) return "none";
    
    // Analyze file access patterns
    std::unordered_map<std::string, int> operation_counts;
    std::vector<size_t> file_sizes;
    std::vector<std::chrono::system_clock::time_point> timestamps;
    
    for (const auto& event : events) {
        operation_counts[event.operation]++;
        file_sizes.push_back(event.bytes_transferred);
    }
    
    // Detect bulk operations
    if (operation_counts["read"] > 100 || operation_counts["write"] > 100) {
        return "bulk";
    }
    
    // Detect sequential vs random access
    bool sequential = true;
    for (size_t i = 1; i < events.size(); ++i) {
        // Simple heuristic: if file paths are not in order, it's random
        if (events[i].file_path < events[i-1].file_path) {
            sequential = false;
            break;
        }
    }
    
    return sequential ? "sequential" : "random";
}

bool FileAccessMonitor::detect_encryption_activity(const std::vector<FileAccessInfo>& events) {
    int encryption_indicators = 0;
    
    for (const auto& event : events) {
        if (event.encryption_detected) {
            encryption_indicators++;
        }
        
        // Check for encryption-related file extensions
        std::string file_path = event.file_path;
        std::transform(file_path.begin(), file_path.end(), file_path.begin(), ::tolower);
        
        if (file_path.find(".encrypted") != std::string::npos ||
            file_path.find(".locked") != std::string::npos ||
            file_path.find(".crypto") != std::string::npos ||
            file_path.find(".crypt") != std::string::npos) {
            encryption_indicators++;
        }
        
        // Check for bulk write operations (potential encryption)
        if (event.operation == "write" && event.bytes_transferred > 1024 * 1024) {
            encryption_indicators++;
        }
    }
    
    // Threshold for encryption detection
    return encryption_indicators > 5;
}

bool FileAccessMonitor::detect_bulk_operations(const std::vector<FileAccessInfo>& events) {
    if (events.size() < 10) return false;
    
    // Count operations in time windows
    auto now = std::chrono::system_clock::now();
    auto window_start = now - std::chrono::seconds(60); // 1-minute window
    
    int operations_in_window = 0;
    size_t total_bytes = 0;
    
    for (const auto& event : events) {
        operations_in_window++;
        total_bytes += event.bytes_transferred;
    }
    
    // Bulk operation thresholds
    return operations_in_window > 50 || total_bytes > 100 * 1024 * 1024; // 100MB
}

bool FileAccessMonitor::is_sensitive_file(const std::string& file_path) {
    return sensitive_files_.find(file_path) != sensitive_files_.end();
}

std::string FileAccessMonitor::determine_access_pattern(const std::vector<FileAccessInfo>& accesses) {
    if (accesses.size() < 2) return "single";
    
    // Analyze temporal patterns
    std::vector<std::chrono::milliseconds> intervals;
    for (size_t i = 1; i < accesses.size(); ++i) {
        // Note: This is simplified - in real implementation, we'd track timestamps
        intervals.push_back(std::chrono::milliseconds(100)); // Placeholder
    }
    
    // Calculate average interval
    auto total = std::chrono::milliseconds(0);
    for (const auto& interval : intervals) {
        total += interval;
    }
    auto avg_interval = total / intervals.size();
    
    if (avg_interval < std::chrono::milliseconds(10)) {
        return "rapid";
    } else if (avg_interval < std::chrono::milliseconds(1000)) {
        return "normal";
    } else {
        return "slow";
    }
}

bool FileAccessMonitor::is_encryption_operation(const FileAccessInfo& access) {
    // Heuristics for detecting encryption operations
    
    // Check file size changes (encryption often changes file size)
    if (access.operation == "write" && access.bytes_transferred > 0) {
        // In real implementation, compare with original file size
        return true; // Simplified
    }
    
    // Check for encryption-related patterns in file names
    std::string file_path = access.file_path;
    std::transform(file_path.begin(), file_path.end(), file_path.begin(), ::tolower);
    
    std::vector<std::string> encryption_patterns = {
        ".encrypted", ".locked", ".crypto", ".crypt", ".enc", ".aes"
    };
    
    for (const auto& pattern : encryption_patterns) {
        if (file_path.find(pattern) != std::string::npos) {
            return true;
        }
    }
    
    return false;
}

#ifdef _WIN32
void FileAccessMonitor::monitor_windows_file_operations() {
    // Windows implementation using ReadDirectoryChangesW
    // This is a simplified implementation
    
    std::thread monitor_thread([this]() {
        while (monitoring_) {
            // Monitor file system changes
            // In real implementation, use ReadDirectoryChangesW API
            
            // Simulate file access event
            FileAccessInfo access_info;
            access_info.operation = "read";
            access_info.file_path = "C:\\temp\\test.txt";
            access_info.process_name = "test.exe";
            access_info.access_mode = 0x80000000; // GENERIC_READ
            access_info.bytes_transferred = 1024;
            access_info.is_sensitive_file = is_sensitive_file(access_info.file_path);
            access_info.access_pattern = "sequential";
            access_info.encryption_detected = is_encryption_operation(access_info);
            
            {
                std::lock_guard<std::mutex> lock(events_mutex_);
                events_.push_back(access_info);
                
                // Limit event history
                if (events_.size() > 10000) {
                    events_.erase(events_.begin(), events_.begin() + 1000);
                }
            }
            
            if (callback_) {
                callback_(access_info);
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    });
    
    monitor_thread.detach();
}

void CALLBACK FileAccessMonitor::file_completion_routine(DWORD error_code, DWORD bytes_transferred, LPOVERLAPPED overlapped) {
    // Handle file operation completion
    if (error_code == 0) {
        // Process successful file operation
        std::cout << "File operation completed: " << bytes_transferred << " bytes" << std::endl;
    }
}
#else
void FileAccessMonitor::monitor_linux_file_operations() {
    setup_inotify_monitoring();
}

void FileAccessMonitor::setup_inotify_monitoring() {
    std::thread monitor_thread([this]() {
        int inotify_fd = inotify_init();
        if (inotify_fd < 0) {
            std::cerr << "Failed to initialize inotify" << std::endl;
            return;
        }
        
        // Add watches for sensitive directories
        std::vector<std::string> watch_dirs = {"/etc", "/tmp", "/var", "/home"};
        std::vector<int> watch_descriptors;
        
        for (const auto& dir : watch_dirs) {
            int wd = inotify_add_watch(inotify_fd, dir.c_str(), 
                                      IN_CREATE | IN_DELETE | IN_MODIFY | IN_MOVED_FROM | IN_MOVED_TO);
            if (wd >= 0) {
                watch_descriptors.push_back(wd);
            }
        }
        
        char buffer[4096];
        while (monitoring_) {
            ssize_t length = read(inotify_fd, buffer, sizeof(buffer));
            if (length < 0) {
                continue;
            }
            
            int i = 0;
            while (i < length) {
                struct inotify_event* event = (struct inotify_event*)&buffer[i];
                
                FileAccessInfo access_info;
                access_info.file_path = event->name ? event->name : "unknown";
                access_info.process_name = "unknown"; // Would need additional tracking
                access_info.bytes_transferred = 0;
                access_info.is_sensitive_file = is_sensitive_file(access_info.file_path);
                access_info.encryption_detected = false;
                
                if (event->mask & IN_CREATE) {
                    access_info.operation = "create";
                } else if (event->mask & IN_DELETE) {
                    access_info.operation = "delete";
                } else if (event->mask & IN_MODIFY) {
                    access_info.operation = "modify";
                } else if (event->mask & (IN_MOVED_FROM | IN_MOVED_TO)) {
                    access_info.operation = "move";
                }
                
                {
                    std::lock_guard<std::mutex> lock(events_mutex_);
                    events_.push_back(access_info);
                    
                    if (events_.size() > 10000) {
                        events_.erase(events_.begin(), events_.begin() + 1000);
                    }
                }
                
                if (callback_) {
                    callback_(access_info);
                }
                
                i += sizeof(struct inotify_event) + event->len;
            }
        }
        
        // Cleanup
        for (int wd : watch_descriptors) {
            inotify_rm_watch(inotify_fd, wd);
        }
        close(inotify_fd);
    });
    
    monitor_thread.detach();
}
#endif

} // namespace hooking
} // namespace sbards
