{"response_data": {"engine_type": "cpp_core", "fallback_to_python": true, "base_directory": "response_data", "quarantine_directory": "response_data/quarantine", "honeypot_directory": "response_data/honeypot", "forensics_directory": "response_data/forensics", "ml_models_directory": "response_data/ml_models", "blockchain_directory": "response_data/blockchain", "backup_directory": "response_data/backup", "reports_directory": "response_data/reports", "safe_files_directory": "response_data/safe_files", "logs_directory": "response_data/logs", "encryption_enabled": true, "compression_enabled": false, "blockchain_logging": false, "forensic_integrity_check": true, "real_time_monitoring": true, "max_concurrent_operations": 10, "operation_timeout_seconds": 300, "security_level": "enhanced", "cross_platform_compatibility": true, "performance_optimization": true, "data_types": {"quarantine": {"encryption_required": true, "compression_enabled": false, "retention_days": 90, "access_level": "restricted"}, "honeypot": {"encryption_required": true, "compression_enabled": true, "retention_days": 365, "access_level": "monitored"}, "forensic": {"encryption_required": true, "compression_enabled": false, "retention_days": 2555, "access_level": "evidence_grade"}, "ml_model": {"encryption_required": false, "compression_enabled": true, "retention_days": 180, "access_level": "internal"}, "blockchain": {"encryption_required": true, "compression_enabled": false, "retention_days": -1, "access_level": "immutable"}, "backup": {"encryption_required": true, "compression_enabled": true, "retention_days": 30, "access_level": "recovery"}, "report": {"encryption_required": false, "compression_enabled": true, "retention_days": 365, "access_level": "public"}, "safe_file": {"encryption_required": false, "compression_enabled": false, "retention_days": 30, "access_level": "trusted"}}}, "cpp_data_core": {"library_paths": ["cpp_core/build/libsbards_data_engine.so", "cpp_core/build/lib/libsbards_data_engine.so", "cpp_core/libsbards_data_engine.so", "cpp_core/Release/sbards_data_engine.dll", "cpp_core/Debug/sbards_data_engine.dll", "cpp_core/build/libsbards_data_engine.dylib"], "security_level": 1, "encryption_algorithm": "AES-256-GCM", "hash_algorithm": "SHA-256", "compression_algorithm": "zlib", "secure_delete_passes": 3, "memory_protection": true, "thread_safety": true, "forensic_mode": true}, "enhanced_data": {"security_enabled": true, "encryption_enabled": true, "forensic_mode": true, "performance_monitoring": true, "data_integrity_check": true, "chain_of_custody": true, "audit_logging": true, "access_control": true}, "performance": {"enable_multithreading": true, "worker_threads": 10, "memory_optimization": true, "cache_enabled": true, "cache_size_mb": 512, "batch_processing": true, "async_operations": true, "performance_profiling": true, "compression_level": 6, "encryption_cache_size": 100}, "security": {"encryption_key_size": 256, "secure_memory_allocation": true, "memory_wiping": true, "access_control": true, "audit_logging": true, "integrity_checking": true, "secure_communication": true, "certificate_validation": true, "forensic_compliance": true, "chain_of_custody_tracking": true, "evidence_integrity": true, "tamper_detection": true}, "storage": {"auto_create_directories": true, "directory_permissions": "0750", "file_permissions": "0640", "backup_enabled": true, "backup_frequency_hours": 24, "cleanup_enabled": true, "cleanup_frequency_hours": 168, "storage_quota_gb": 100, "storage_warning_threshold": 0.8}, "monitoring": {"real_time_metrics": true, "performance_tracking": true, "error_tracking": true, "resource_monitoring": true, "storage_monitoring": true, "access_monitoring": true, "alert_thresholds": {"storage_usage_percent": 85, "operation_failure_rate": 5, "average_operation_time_ms": 1000, "memory_usage_percent": 80, "concurrent_operations": 15}, "log_levels": ["INFO", "WARNING", "ERROR", "CRITICAL"], "log_rotation": true, "log_retention_days": 90, "metrics_retention_days": 30}, "forensics": {"evidence_collection": true, "chain_of_custody": true, "digital_signatures": true, "timestamp_verification": true, "integrity_verification": true, "evidence_encryption": true, "evidence_compression": false, "evidence_retention_years": 7, "case_management": true, "evidence_export": true, "court_ready_format": true}, "ml_models": {"model_versioning": true, "model_backup": true, "model_validation": true, "performance_tracking": true, "auto_update": false, "model_encryption": false, "model_compression": true, "training_data_retention": true, "metrics_tracking": true, "model_registry": true}, "blockchain": {"enabled": false, "consensus_algorithm": "proof_of_work", "block_size_kb": 1024, "mining_difficulty": 4, "transaction_validation": true, "digital_signatures": true, "immutable_logging": true, "distributed_storage": false, "smart_contracts": false}, "compliance": {"gdpr_compliance": true, "hipaa_compliance": false, "sox_compliance": false, "pci_dss_compliance": false, "iso27001_compliance": true, "data_retention_policy": true, "audit_trail": true, "privacy_protection": true, "data_anonymization": true, "right_to_be_forgotten": true}, "advanced_features": {"data_deduplication": true, "intelligent_compression": true, "predictive_storage": false, "auto_classification": true, "content_analysis": true, "metadata_extraction": true, "similarity_detection": true, "anomaly_detection": true, "pattern_recognition": true, "machine_learning_integration": true}, "platform_specific": {"windows": {"use_windows_api": true, "ntfs_features": true, "windows_security": true, "event_log_integration": true, "registry_storage": false}, "linux": {"use_linux_api": true, "ext4_features": true, "linux_security": true, "syslog_integration": true, "xattr_support": true}, "macos": {"use_macos_api": true, "hfs_features": true, "macos_security": true, "unified_logging": true, "extended_attributes": true}}, "testing": {"unit_tests_enabled": true, "integration_tests_enabled": true, "performance_tests_enabled": true, "security_tests_enabled": true, "stress_tests_enabled": true, "data_integrity_tests": true, "encryption_tests": true, "forensic_tests": true, "test_data_directory": "test_data", "test_results_directory": "test_results", "automated_testing": true, "continuous_integration": true}, "deployment": {"containerization_support": true, "docker_integration": true, "kubernetes_support": true, "cloud_deployment": true, "on_premise_deployment": true, "hybrid_deployment": true, "auto_scaling": true, "load_balancing": true, "high_availability": true, "disaster_recovery": true, "backup_strategies": ["local", "cloud", "distributed"]}, "version_info": {"version": "2.0.0", "build_date": "2024-01-01", "cpp_data_core_version": "1.0.0", "python_interface_version": "2.0.0", "api_version": "v2", "compatibility_version": "1.0+", "minimum_requirements": {"python_version": "3.8+", "cpp_standard": "C++17", "cmake_version": "3.16+", "openssl_version": "1.1.1+", "zlib_version": "1.2.11+"}}}