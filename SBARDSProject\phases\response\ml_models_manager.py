#!/usr/bin/env python3
"""
SBARDS Advanced ML Models Manager for Response Layer
Multi-Model Machine Learning Integration for Enhanced Threat Response

This module provides:
- Multiple specialized ML models for different threat types
- Real-time model inference and adaptation
- Ensemble learning for improved accuracy
- Continuous learning and model updates
- Performance monitoring and optimization
"""

import os
import sys
import json
import pickle
import logging
import time
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Union
import asyncio
import threading
from dataclasses import dataclass
from sklearn.ensemble import (
    RandomForestClassifier, GradientBoostingClassifier,
    IsolationForest, VotingClassifier
)
from sklearn.neural_network import MLPClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
import joblib
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

@dataclass
class ModelMetrics:
    """Model performance metrics."""
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    auc_score: float
    last_updated: str
    training_samples: int
    prediction_count: int

class MLModelConfig:
    """Configuration for ML models."""

    # Model types and their configurations
    MODEL_CONFIGS = {
        "threat_classifier": {
            "type": "classification",
            "target": "threat_level",
            "classes": ["safe", "suspicious", "malicious", "critical"],
            "features": ["file_size", "entropy", "api_calls_count", "network_connections",
                        "file_operations", "registry_changes", "process_count", "suspicious_strings"],
            "algorithms": ["random_forest", "gradient_boosting", "neural_network", "svm"]
        },

        "malware_family_detector": {
            "type": "classification",
            "target": "malware_family",
            "classes": ["ransomware", "trojan", "backdoor", "rootkit", "spyware", "adware", "worm", "virus"],
            "features": ["pe_characteristics", "behavioral_patterns", "network_patterns",
                        "file_modifications", "encryption_indicators", "persistence_methods"],
            "algorithms": ["random_forest", "gradient_boosting", "neural_network"]
        },

        "anomaly_detector": {
            "type": "anomaly_detection",
            "target": "is_anomaly",
            "features": ["system_behavior", "network_behavior", "file_behavior", "process_behavior"],
            "algorithms": ["isolation_forest", "one_class_svm", "autoencoder"]
        },

        "behavioral_analyzer": {
            "type": "regression",
            "target": "behavior_score",
            "features": ["api_sequence", "system_calls", "network_patterns", "file_access_patterns"],
            "algorithms": ["gradient_boosting", "neural_network", "random_forest"]
        },

        "evasion_detector": {
            "type": "classification",
            "target": "evasion_technique",
            "classes": ["packing", "obfuscation", "anti_debug", "anti_vm", "code_injection", "none"],
            "features": ["entropy_variance", "section_characteristics", "import_anomalies",
                        "string_obfuscation", "control_flow_complexity"],
            "algorithms": ["random_forest", "svm", "neural_network"]
        },

        "response_optimizer": {
            "type": "classification",
            "target": "optimal_response",
            "classes": ["quarantine", "monitor", "block", "analyze", "ignore"],
            "features": ["threat_score", "confidence", "system_impact", "business_criticality",
                        "historical_patterns", "resource_availability"],
            "algorithms": ["gradient_boosting", "neural_network", "ensemble"]
        }
    }

class AdvancedMLModelsManager:
    """Advanced ML Models Manager for Response Layer."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize ML Models Manager."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.MLModelsManager")

        # Setup directories
        self.models_dir = Path(config.get("ml_models_directory", "response_data/ml_models"))
        self.training_data_dir = self.models_dir / "training_data"
        self.model_cache_dir = self.models_dir / "cache"
        self.metrics_dir = self.models_dir / "metrics"

        # Create directories
        for directory in [self.models_dir, self.training_data_dir, self.model_cache_dir, self.metrics_dir]:
            directory.mkdir(parents=True, exist_ok=True)

        # Model storage
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.model_metrics = {}

        # Configuration
        self.auto_retrain_enabled = config.get("auto_retrain_enabled", True)
        self.retrain_threshold_samples = config.get("retrain_threshold_samples", 1000)
        self.retrain_interval_hours = config.get("retrain_interval_hours", 24)
        self.ensemble_enabled = config.get("ensemble_enabled", True)

        # Performance monitoring
        self.prediction_cache = {}
        self.performance_history = {}

        # Threading for background tasks
        self.background_tasks_running = True
        self.retrain_thread = threading.Thread(target=self._background_retrain_loop, daemon=True)
        self.retrain_thread.start()

        # Initialize models
        self._initialize_models()

        self.logger.info("Advanced ML Models Manager initialized successfully")

    def _initialize_models(self):
        """Initialize all ML models."""
        try:
            for model_name, model_config in MLModelConfig.MODEL_CONFIGS.items():
                self.logger.info(f"Initializing model: {model_name}")

                # Load existing model or create new one
                model_path = self.models_dir / f"{model_name}.joblib"
                scaler_path = self.models_dir / f"{model_name}_scaler.joblib"
                encoder_path = self.models_dir / f"{model_name}_encoder.joblib"

                if model_path.exists():
                    # Load existing model
                    self.models[model_name] = joblib.load(model_path)
                    if scaler_path.exists():
                        self.scalers[model_name] = joblib.load(scaler_path)
                    if encoder_path.exists():
                        self.encoders[model_name] = joblib.load(encoder_path)

                    self.logger.info(f"Loaded existing model: {model_name}")
                else:
                    # Create new model
                    self._create_new_model(model_name, model_config)
                    self.logger.info(f"Created new model: {model_name}")

                # Load metrics
                self._load_model_metrics(model_name)

        except Exception as e:
            self.logger.error(f"Error initializing models: {e}")

    def _create_new_model(self, model_name: str, model_config: Dict[str, Any]):
        """Create a new ML model."""
        try:
            model_type = model_config["type"]
            algorithms = model_config["algorithms"]

            if model_type == "classification":
                if self.ensemble_enabled and len(algorithms) > 1:
                    # Create ensemble model
                    estimators = []
                    for algo in algorithms:
                        estimator = self._create_algorithm(algo, model_type)
                        estimators.append((algo, estimator))

                    model = VotingClassifier(estimators=estimators, voting='soft')
                else:
                    # Single algorithm
                    model = self._create_algorithm(algorithms[0], model_type)

            elif model_type == "anomaly_detection":
                model = self._create_algorithm(algorithms[0], model_type)

            elif model_type == "regression":
                if self.ensemble_enabled and len(algorithms) > 1:
                    # For regression, use the first algorithm as primary
                    model = self._create_algorithm(algorithms[0], model_type)
                else:
                    model = self._create_algorithm(algorithms[0], model_type)

            self.models[model_name] = model
            self.scalers[model_name] = StandardScaler()

            if model_type == "classification":
                self.encoders[model_name] = LabelEncoder()

        except Exception as e:
            self.logger.error(f"Error creating model {model_name}: {e}")

    def _create_algorithm(self, algorithm: str, model_type: str):
        """Create specific algorithm instance."""
        if algorithm == "random_forest":
            if model_type == "classification":
                return RandomForestClassifier(
                    n_estimators=100, max_depth=10, random_state=42,
                    class_weight='balanced', n_jobs=-1
                )
            else:
                from sklearn.ensemble import RandomForestRegressor
                return RandomForestRegressor(
                    n_estimators=100, max_depth=10, random_state=42, n_jobs=-1
                )

        elif algorithm == "gradient_boosting":
            if model_type == "classification":
                return GradientBoostingClassifier(
                    n_estimators=100, learning_rate=0.1, max_depth=6, random_state=42
                )
            else:
                from sklearn.ensemble import GradientBoostingRegressor
                return GradientBoostingRegressor(
                    n_estimators=100, learning_rate=0.1, max_depth=6, random_state=42
                )

        elif algorithm == "neural_network":
            if model_type == "classification":
                return MLPClassifier(
                    hidden_layer_sizes=(100, 50), max_iter=500, random_state=42,
                    early_stopping=True, validation_fraction=0.1
                )
            else:
                from sklearn.neural_network import MLPRegressor
                return MLPRegressor(
                    hidden_layer_sizes=(100, 50), max_iter=500, random_state=42,
                    early_stopping=True, validation_fraction=0.1
                )

        elif algorithm == "svm":
            if model_type == "classification":
                return SVC(probability=True, random_state=42, class_weight='balanced')
            else:
                from sklearn.svm import SVR
                return SVR()

        elif algorithm == "isolation_forest":
            return IsolationForest(contamination=0.1, random_state=42, n_jobs=-1)

        elif algorithm == "naive_bayes":
            return GaussianNB()

        elif algorithm == "logistic_regression":
            return LogisticRegression(random_state=42, class_weight='balanced', max_iter=1000)

        else:
            raise ValueError(f"Unknown algorithm: {algorithm}")

    async def predict_threat_level(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Predict threat level using threat classifier."""
        try:
            model_name = "threat_classifier"
            if model_name not in self.models:
                return {"error": f"Model {model_name} not available"}

            # Extract and prepare features
            feature_vector = self._extract_features(features, model_name)
            if feature_vector is None:
                return {"error": "Failed to extract features"}

            # Scale features
            if model_name in self.scalers:
                feature_vector = self.scalers[model_name].transform([feature_vector])
            else:
                feature_vector = [feature_vector]

            # Make prediction
            model = self.models[model_name]
            prediction = model.predict(feature_vector)[0]

            # Get prediction probabilities if available
            probabilities = {}
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(feature_vector)[0]
                classes = model.classes_ if hasattr(model, 'classes_') else MLModelConfig.MODEL_CONFIGS[model_name]["classes"]
                probabilities = dict(zip(classes, proba))

            # Update prediction count
            self._update_prediction_metrics(model_name)

            return {
                "prediction": prediction,
                "probabilities": probabilities,
                "confidence": max(probabilities.values()) if probabilities else 0.0,
                "model_used": model_name,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error in threat level prediction: {e}")
            return {"error": str(e)}

    async def detect_malware_family(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Detect malware family using specialized classifier."""
        try:
            model_name = "malware_family_detector"
            if model_name not in self.models:
                return {"error": f"Model {model_name} not available"}

            # Extract behavioral and structural features
            feature_vector = self._extract_malware_features(features)
            if feature_vector is None:
                return {"error": "Failed to extract malware features"}

            # Scale features
            if model_name in self.scalers:
                feature_vector = self.scalers[model_name].transform([feature_vector])
            else:
                feature_vector = [feature_vector]

            # Make prediction
            model = self.models[model_name]
            prediction = model.predict(feature_vector)[0]

            # Get confidence scores
            probabilities = {}
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(feature_vector)[0]
                classes = model.classes_ if hasattr(model, 'classes_') else MLModelConfig.MODEL_CONFIGS[model_name]["classes"]
                probabilities = dict(zip(classes, proba))

            self._update_prediction_metrics(model_name)

            return {
                "malware_family": prediction,
                "family_probabilities": probabilities,
                "confidence": max(probabilities.values()) if probabilities else 0.0,
                "model_used": model_name,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error in malware family detection: {e}")
            return {"error": str(e)}

    async def detect_anomalies(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Detect anomalies in system behavior."""
        try:
            model_name = "anomaly_detector"
            if model_name not in self.models:
                return {"error": f"Model {model_name} not available"}

            # Extract behavioral features
            feature_vector = self._extract_behavioral_features(features)
            if feature_vector is None:
                return {"error": "Failed to extract behavioral features"}

            # Scale features
            if model_name in self.scalers:
                feature_vector = self.scalers[model_name].transform([feature_vector])
            else:
                feature_vector = [feature_vector]

            # Make prediction
            model = self.models[model_name]
            prediction = model.predict(feature_vector)[0]

            # Get anomaly score
            anomaly_score = 0.0
            if hasattr(model, 'decision_function'):
                anomaly_score = model.decision_function(feature_vector)[0]
            elif hasattr(model, 'score_samples'):
                anomaly_score = model.score_samples(feature_vector)[0]

            self._update_prediction_metrics(model_name)

            return {
                "is_anomaly": prediction == -1,  # Isolation Forest returns -1 for anomalies
                "anomaly_score": float(anomaly_score),
                "confidence": abs(anomaly_score),
                "model_used": model_name,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error in anomaly detection: {e}")
            return {"error": str(e)}

    async def analyze_behavior(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze behavioral patterns and assign behavior score."""
        try:
            model_name = "behavioral_analyzer"
            if model_name not in self.models:
                return {"error": f"Model {model_name} not available"}

            # Extract behavioral sequence features
            feature_vector = self._extract_sequence_features(features)
            if feature_vector is None:
                return {"error": "Failed to extract sequence features"}

            # Scale features
            if model_name in self.scalers:
                feature_vector = self.scalers[model_name].transform([feature_vector])
            else:
                feature_vector = [feature_vector]

            # Make prediction
            model = self.models[model_name]
            behavior_score = model.predict(feature_vector)[0]

            self._update_prediction_metrics(model_name)

            return {
                "behavior_score": float(behavior_score),
                "risk_level": self._score_to_risk_level(behavior_score),
                "model_used": model_name,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error in behavioral analysis: {e}")
            return {"error": str(e)}

    async def detect_evasion_techniques(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Detect evasion techniques used by malware."""
        try:
            model_name = "evasion_detector"
            if model_name not in self.models:
                return {"error": f"Model {model_name} not available"}

            # Extract evasion-related features
            feature_vector = self._extract_evasion_features(features)
            if feature_vector is None:
                return {"error": "Failed to extract evasion features"}

            # Scale features
            if model_name in self.scalers:
                feature_vector = self.scalers[model_name].transform([feature_vector])
            else:
                feature_vector = [feature_vector]

            # Make prediction
            model = self.models[model_name]
            prediction = model.predict(feature_vector)[0]

            # Get technique probabilities
            probabilities = {}
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(feature_vector)[0]
                classes = model.classes_ if hasattr(model, 'classes_') else MLModelConfig.MODEL_CONFIGS[model_name]["classes"]
                probabilities = dict(zip(classes, proba))

            self._update_prediction_metrics(model_name)

            return {
                "evasion_technique": prediction,
                "technique_probabilities": probabilities,
                "confidence": max(probabilities.values()) if probabilities else 0.0,
                "model_used": model_name,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error in evasion detection: {e}")
            return {"error": str(e)}

    async def optimize_response(self, threat_data: Dict[str, Any], system_context: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize response strategy using ML."""
        try:
            model_name = "response_optimizer"
            if model_name not in self.models:
                return {"error": f"Model {model_name} not available"}

            # Extract optimization features
            feature_vector = self._extract_optimization_features(threat_data, system_context)
            if feature_vector is None:
                return {"error": "Failed to extract optimization features"}

            # Scale features
            if model_name in self.scalers:
                feature_vector = self.scalers[model_name].transform([feature_vector])
            else:
                feature_vector = [feature_vector]

            # Make prediction
            model = self.models[model_name]
            optimal_response = model.predict(feature_vector)[0]

            # Get response probabilities
            probabilities = {}
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(feature_vector)[0]
                classes = model.classes_ if hasattr(model, 'classes_') else MLModelConfig.MODEL_CONFIGS[model_name]["classes"]
                probabilities = dict(zip(classes, proba))

            self._update_prediction_metrics(model_name)

            return {
                "optimal_response": optimal_response,
                "response_probabilities": probabilities,
                "confidence": max(probabilities.values()) if probabilities else 0.0,
                "model_used": model_name,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error in response optimization: {e}")
            return {"error": str(e)}

    def _extract_features(self, features: Dict[str, Any], model_name: str) -> Optional[List[float]]:
        """Extract features for threat classifier."""
        try:
            config_features = MLModelConfig.MODEL_CONFIGS[model_name]["features"]
            feature_vector = []

            # File size
            file_size = features.get("file_info", {}).get("size", 0)
            feature_vector.append(float(file_size))

            # Entropy
            entropy = features.get("static_analysis", {}).get("entropy", 0.0)
            feature_vector.append(float(entropy))

            # API calls count
            api_calls = features.get("dynamic_analysis", {}).get("api_calls", [])
            feature_vector.append(float(len(api_calls)))

            # Network connections
            network_connections = features.get("dynamic_analysis", {}).get("network_connections", [])
            feature_vector.append(float(len(network_connections)))

            # File operations
            file_operations = features.get("dynamic_analysis", {}).get("file_operations", [])
            feature_vector.append(float(len(file_operations)))

            # Registry changes
            registry_changes = features.get("dynamic_analysis", {}).get("registry_changes", [])
            feature_vector.append(float(len(registry_changes)))

            # Process count
            processes = features.get("dynamic_analysis", {}).get("process_info", {})
            feature_vector.append(float(len(processes)))

            # Suspicious strings
            suspicious_strings = features.get("static_analysis", {}).get("suspicious_strings", [])
            feature_vector.append(float(len(suspicious_strings)))

            return feature_vector

        except Exception as e:
            self.logger.error(f"Error extracting features: {e}")
            return None

    def _extract_malware_features(self, features: Dict[str, Any]) -> Optional[List[float]]:
        """Extract features for malware family detection."""
        try:
            feature_vector = []

            # PE characteristics
            pe_analysis = features.get("static_analysis", {}).get("pe_analysis", {})
            feature_vector.extend([
                float(pe_analysis.get("packed", False)),
                float(pe_analysis.get("signed", False)),
                float(pe_analysis.get("obfuscated", False)),
                float(len(pe_analysis.get("sections", []))),
                float(len(pe_analysis.get("imports", [])))
            ])

            # Behavioral patterns
            dynamic_analysis = features.get("dynamic_analysis", {})
            feature_vector.extend([
                float(len(dynamic_analysis.get("api_calls", []))),
                float(len(dynamic_analysis.get("file_operations", []))),
                float(len(dynamic_analysis.get("registry_changes", []))),
                float(len(dynamic_analysis.get("network_connections", [])))
            ])

            # Network patterns
            network_analysis = features.get("network_analysis", {})
            feature_vector.extend([
                float(len(network_analysis.get("connections", []))),
                float(len(network_analysis.get("dns_queries", []))),
                float(len(network_analysis.get("suspicious_domains", [])))
            ])

            # Encryption indicators
            encryption_indicators = 0
            if "encrypt" in str(features).lower() or "crypt" in str(features).lower():
                encryption_indicators = 1
            feature_vector.append(float(encryption_indicators))

            # Persistence methods
            persistence_indicators = 0
            registry_changes = dynamic_analysis.get("registry_changes", [])
            for change in registry_changes:
                if any(key in str(change).lower() for key in ["run", "startup", "service"]):
                    persistence_indicators += 1
            feature_vector.append(float(persistence_indicators))

            return feature_vector

        except Exception as e:
            self.logger.error(f"Error extracting malware features: {e}")
            return None

    def _extract_behavioral_features(self, features: Dict[str, Any]) -> Optional[List[float]]:
        """Extract features for anomaly detection."""
        try:
            feature_vector = []

            # System behavior metrics
            dynamic_analysis = features.get("dynamic_analysis", {})

            # Process behavior
            processes = dynamic_analysis.get("process_info", {})
            feature_vector.extend([
                float(len(processes)),
                float(sum(1 for p in processes.values() if p.get("suspicious", False))),
                float(sum(p.get("cpu_usage", 0) for p in processes.values())),
                float(sum(p.get("memory_usage", 0) for p in processes.values()))
            ])

            # Network behavior
            network_connections = dynamic_analysis.get("network_connections", [])
            feature_vector.extend([
                float(len(network_connections)),
                float(sum(1 for conn in network_connections if conn.get("suspicious", False))),
                float(sum(conn.get("bytes_sent", 0) for conn in network_connections)),
                float(sum(conn.get("bytes_received", 0) for conn in network_connections))
            ])

            # File behavior
            file_operations = dynamic_analysis.get("file_operations", [])
            feature_vector.extend([
                float(len(file_operations)),
                float(sum(1 for op in file_operations if op.get("operation") == "write")),
                float(sum(1 for op in file_operations if op.get("operation") == "delete")),
                float(sum(1 for op in file_operations if op.get("suspicious", False)))
            ])

            # Registry behavior
            registry_changes = dynamic_analysis.get("registry_changes", [])
            feature_vector.extend([
                float(len(registry_changes)),
                float(sum(1 for change in registry_changes if change.get("suspicious", False)))
            ])

            return feature_vector

        except Exception as e:
            self.logger.error(f"Error extracting behavioral features: {e}")
            return None

    def _extract_sequence_features(self, features: Dict[str, Any]) -> Optional[List[float]]:
        """Extract sequence features for behavioral analysis."""
        try:
            feature_vector = []

            # API call sequences
            api_calls = features.get("dynamic_analysis", {}).get("api_calls", [])
            api_sequence_features = self._analyze_api_sequence(api_calls)
            feature_vector.extend(api_sequence_features)

            # System call patterns
            system_calls = features.get("dynamic_analysis", {}).get("system_calls", [])
            syscall_features = self._analyze_syscall_patterns(system_calls)
            feature_vector.extend(syscall_features)

            # Network communication patterns
            network_connections = features.get("dynamic_analysis", {}).get("network_connections", [])
            network_features = self._analyze_network_patterns(network_connections)
            feature_vector.extend(network_features)

            # File access patterns
            file_operations = features.get("dynamic_analysis", {}).get("file_operations", [])
            file_features = self._analyze_file_patterns(file_operations)
            feature_vector.extend(file_features)

            return feature_vector

        except Exception as e:
            self.logger.error(f"Error extracting sequence features: {e}")
            return None

    def _extract_evasion_features(self, features: Dict[str, Any]) -> Optional[List[float]]:
        """Extract features for evasion technique detection."""
        try:
            feature_vector = []

            # Entropy variance (packing indicator)
            static_analysis = features.get("static_analysis", {})
            entropy = static_analysis.get("entropy", 0.0)
            entropy_variance = static_analysis.get("entropy_variance", 0.0)
            feature_vector.extend([float(entropy), float(entropy_variance)])

            # Section characteristics
            pe_analysis = static_analysis.get("pe_analysis", {})
            sections = pe_analysis.get("sections", [])
            feature_vector.extend([
                float(len(sections)),
                float(sum(1 for s in sections if s.get("executable", False))),
                float(sum(1 for s in sections if s.get("writable", False))),
                float(sum(s.get("entropy", 0) for s in sections) / max(len(sections), 1))
            ])

            # Import anomalies
            imports = pe_analysis.get("imports", [])
            suspicious_imports = sum(1 for imp in imports if any(
                keyword in imp.lower() for keyword in ["debug", "virtual", "process", "thread"]
            ))
            feature_vector.append(float(suspicious_imports))

            # String obfuscation indicators
            strings = static_analysis.get("strings", [])
            obfuscated_strings = sum(1 for s in strings if len(s) > 50 or any(
                char in s for char in "!@#$%^&*()_+-=[]{}|;:,.<>?"
            ))
            feature_vector.append(float(obfuscated_strings))

            # Control flow complexity
            control_flow_complexity = static_analysis.get("control_flow_complexity", 0.0)
            feature_vector.append(float(control_flow_complexity))

            return feature_vector

        except Exception as e:
            self.logger.error(f"Error extracting evasion features: {e}")
            return None

    def _extract_optimization_features(self, threat_data: Dict[str, Any], system_context: Dict[str, Any]) -> Optional[List[float]]:
        """Extract features for response optimization."""
        try:
            feature_vector = []

            # Threat characteristics
            threat_score = threat_data.get("threat_assessment", {}).get("threat_score", 0.0)
            confidence = threat_data.get("threat_assessment", {}).get("confidence", 0.0)
            feature_vector.extend([float(threat_score), float(confidence)])

            # System impact assessment
            system_impact = self._assess_system_impact(threat_data, system_context)
            feature_vector.append(float(system_impact))

            # Business criticality
            business_criticality = system_context.get("business_criticality", 0.5)
            feature_vector.append(float(business_criticality))

            # Historical patterns
            historical_score = self._get_historical_pattern_score(threat_data)
            feature_vector.append(float(historical_score))

            # Resource availability
            resource_availability = self._assess_resource_availability(system_context)
            feature_vector.append(float(resource_availability))

            # File characteristics
            file_size = threat_data.get("file_info", {}).get("size", 0)
            file_type_risk = self._assess_file_type_risk(threat_data.get("file_info", {}))
            feature_vector.extend([float(file_size), float(file_type_risk)])

            return feature_vector

        except Exception as e:
            self.logger.error(f"Error extracting optimization features: {e}")
            return None

    def _analyze_api_sequence(self, api_calls: List[Dict[str, Any]]) -> List[float]:
        """Analyze API call sequences."""
        features = []

        # Basic statistics
        features.append(float(len(api_calls)))

        # Unique API count
        unique_apis = set(call.get("api_name", "") for call in api_calls)
        features.append(float(len(unique_apis)))

        # Suspicious API patterns
        suspicious_apis = ["CreateProcess", "WriteFile", "RegSetValue", "VirtualAlloc"]
        suspicious_count = sum(1 for call in api_calls
                             if any(api in call.get("api_name", "") for api in suspicious_apis))
        features.append(float(suspicious_count))

        # API call frequency patterns
        if api_calls:
            timestamps = [call.get("timestamp", 0) for call in api_calls]
            if len(timestamps) > 1:
                intervals = [timestamps[i+1] - timestamps[i] for i in range(len(timestamps)-1)]
                avg_interval = sum(intervals) / len(intervals)
                features.append(float(avg_interval))
            else:
                features.append(0.0)
        else:
            features.append(0.0)

        return features

    def _analyze_syscall_patterns(self, system_calls: List[Dict[str, Any]]) -> List[float]:
        """Analyze system call patterns."""
        features = []

        # Basic statistics
        features.append(float(len(system_calls)))

        # System call categories
        file_syscalls = sum(1 for call in system_calls if "file" in call.get("category", "").lower())
        network_syscalls = sum(1 for call in system_calls if "network" in call.get("category", "").lower())
        process_syscalls = sum(1 for call in system_calls if "process" in call.get("category", "").lower())

        features.extend([float(file_syscalls), float(network_syscalls), float(process_syscalls)])

        return features

    def _analyze_network_patterns(self, network_connections: List[Dict[str, Any]]) -> List[float]:
        """Analyze network communication patterns."""
        features = []

        # Connection statistics
        features.append(float(len(network_connections)))

        # Protocol distribution
        tcp_count = sum(1 for conn in network_connections if conn.get("protocol", "").lower() == "tcp")
        udp_count = sum(1 for conn in network_connections if conn.get("protocol", "").lower() == "udp")
        features.extend([float(tcp_count), float(udp_count)])

        # Port analysis
        common_ports = [80, 443, 53, 22, 21, 25]
        uncommon_port_count = sum(1 for conn in network_connections
                                if conn.get("port", 0) not in common_ports)
        features.append(float(uncommon_port_count))

        # Data transfer patterns
        total_bytes_sent = sum(conn.get("bytes_sent", 0) for conn in network_connections)
        total_bytes_received = sum(conn.get("bytes_received", 0) for conn in network_connections)
        features.extend([float(total_bytes_sent), float(total_bytes_received)])

        return features

    def _analyze_file_patterns(self, file_operations: List[Dict[str, Any]]) -> List[float]:
        """Analyze file access patterns."""
        features = []

        # Operation statistics
        features.append(float(len(file_operations)))

        # Operation types
        read_count = sum(1 for op in file_operations if op.get("operation", "").lower() == "read")
        write_count = sum(1 for op in file_operations if op.get("operation", "").lower() == "write")
        delete_count = sum(1 for op in file_operations if op.get("operation", "").lower() == "delete")

        features.extend([float(read_count), float(write_count), float(delete_count)])

        # File type patterns
        executable_files = sum(1 for op in file_operations
                             if op.get("file_path", "").lower().endswith(('.exe', '.dll', '.bat', '.cmd')))
        system_files = sum(1 for op in file_operations
                         if "system32" in op.get("file_path", "").lower() or
                            "windows" in op.get("file_path", "").lower())

        features.extend([float(executable_files), float(system_files)])

        return features

    def _score_to_risk_level(self, score: float) -> str:
        """Convert behavior score to risk level."""
        if score < 0.3:
            return "low"
        elif score < 0.6:
            return "medium"
        elif score < 0.8:
            return "high"
        else:
            return "critical"

    def _assess_system_impact(self, threat_data: Dict[str, Any], system_context: Dict[str, Any]) -> float:
        """Assess potential system impact."""
        impact_score = 0.0

        # File type impact
        file_info = threat_data.get("file_info", {})
        if file_info.get("mime_type", "").startswith("application/"):
            impact_score += 0.3

        # System file impact
        file_path = threat_data.get("file_path", "")
        if any(path in file_path.lower() for path in ["system32", "windows", "program files"]):
            impact_score += 0.4

        # Network activity impact
        network_connections = threat_data.get("dynamic_analysis", {}).get("network_connections", [])
        if len(network_connections) > 5:
            impact_score += 0.3

        return min(impact_score, 1.0)

    def _get_historical_pattern_score(self, threat_data: Dict[str, Any]) -> float:
        """Get historical pattern score."""
        # Simplified implementation - in production, this would query historical data
        file_hash = threat_data.get("file_hash", {}).get("sha256", "")

        # Check if we've seen similar threats
        if hasattr(self, 'historical_threats'):
            similar_count = sum(1 for threat in self.historical_threats
                              if threat.get("file_hash") == file_hash)
            return min(similar_count * 0.2, 1.0)

        return 0.5  # Default neutral score

    def _assess_resource_availability(self, system_context: Dict[str, Any]) -> float:
        """Assess system resource availability."""
        cpu_usage = system_context.get("cpu_usage", 50.0)
        memory_usage = system_context.get("memory_usage", 50.0)
        disk_usage = system_context.get("disk_usage", 50.0)

        # Higher resource usage = lower availability
        availability = 1.0 - ((cpu_usage + memory_usage + disk_usage) / 300.0)
        return max(availability, 0.0)

    def _assess_file_type_risk(self, file_info: Dict[str, Any]) -> float:
        """Assess risk based on file type."""
        mime_type = file_info.get("mime_type", "")
        file_extension = file_info.get("extension", "").lower()

        high_risk_extensions = [".exe", ".dll", ".bat", ".cmd", ".scr", ".pif"]
        medium_risk_extensions = [".zip", ".rar", ".7z", ".jar", ".msi"]

        if file_extension in high_risk_extensions:
            return 0.8
        elif file_extension in medium_risk_extensions:
            return 0.5
        elif mime_type.startswith("application/"):
            return 0.4
        else:
            return 0.2

    def _update_prediction_metrics(self, model_name: str):
        """Update prediction metrics for a model."""
        if model_name not in self.model_metrics:
            self.model_metrics[model_name] = ModelMetrics(
                accuracy=0.0, precision=0.0, recall=0.0, f1_score=0.0,
                auc_score=0.0, last_updated=datetime.now().isoformat(),
                training_samples=0, prediction_count=0
            )

        self.model_metrics[model_name].prediction_count += 1

    def _load_model_metrics(self, model_name: str):
        """Load model metrics from file."""
        metrics_file = self.metrics_dir / f"{model_name}_metrics.json"

        if metrics_file.exists():
            try:
                with open(metrics_file, 'r') as f:
                    metrics_data = json.load(f)

                self.model_metrics[model_name] = ModelMetrics(**metrics_data)

            except Exception as e:
                self.logger.warning(f"Failed to load metrics for {model_name}: {e}")
                self._create_default_metrics(model_name)
        else:
            self._create_default_metrics(model_name)

    def _create_default_metrics(self, model_name: str):
        """Create default metrics for a model."""
        self.model_metrics[model_name] = ModelMetrics(
            accuracy=0.0, precision=0.0, recall=0.0, f1_score=0.0,
            auc_score=0.0, last_updated=datetime.now().isoformat(),
            training_samples=0, prediction_count=0
        )

    def _save_model_metrics(self, model_name: str):
        """Save model metrics to file."""
        if model_name in self.model_metrics:
            metrics_file = self.metrics_dir / f"{model_name}_metrics.json"

            try:
                with open(metrics_file, 'w') as f:
                    json.dump(self.model_metrics[model_name].__dict__, f, indent=2)

            except Exception as e:
                self.logger.error(f"Failed to save metrics for {model_name}: {e}")

    def _background_retrain_loop(self):
        """Background loop for model retraining."""
        while self.background_tasks_running:
            try:
                # Check if retraining is needed
                for model_name in self.models.keys():
                    if self._should_retrain_model(model_name):
                        self.logger.info(f"Retraining model: {model_name}")
                        self._retrain_model(model_name)

                # Sleep for retrain interval
                time.sleep(self.retrain_interval_hours * 3600)

            except Exception as e:
                self.logger.error(f"Error in background retrain loop: {e}")
                time.sleep(300)  # Sleep 5 minutes on error

    def _should_retrain_model(self, model_name: str) -> bool:
        """Check if model should be retrained."""
        if not self.auto_retrain_enabled:
            return False

        if model_name not in self.model_metrics:
            return False

        metrics = self.model_metrics[model_name]

        # Check if enough new samples
        if metrics.prediction_count >= self.retrain_threshold_samples:
            return True

        # Check if model is old
        last_updated = datetime.fromisoformat(metrics.last_updated)
        if datetime.now() - last_updated > timedelta(hours=self.retrain_interval_hours):
            return True

        return False

    def _retrain_model(self, model_name: str):
        """Retrain a specific model."""
        try:
            # Load training data
            training_data = self._load_training_data(model_name)
            if training_data is None or len(training_data) < 10:
                self.logger.warning(f"Insufficient training data for {model_name}")
                return

            # Prepare data
            X, y = self._prepare_training_data(training_data, model_name)
            if X is None or y is None:
                self.logger.error(f"Failed to prepare training data for {model_name}")
                return

            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )

            # Scale features
            if model_name in self.scalers:
                X_train = self.scalers[model_name].fit_transform(X_train)
                X_test = self.scalers[model_name].transform(X_test)

            # Encode labels if needed
            if model_name in self.encoders:
                y_train = self.encoders[model_name].fit_transform(y_train)
                y_test = self.encoders[model_name].transform(y_test)

            # Train model
            model = self.models[model_name]
            model.fit(X_train, y_train)

            # Evaluate model
            y_pred = model.predict(X_test)
            metrics = self._calculate_metrics(y_test, y_pred, model_name)

            # Update metrics
            self.model_metrics[model_name] = metrics
            self._save_model_metrics(model_name)

            # Save model
            model_path = self.models_dir / f"{model_name}.joblib"
            joblib.dump(model, model_path)

            self.logger.info(f"Model {model_name} retrained successfully")

        except Exception as e:
            self.logger.error(f"Error retraining model {model_name}: {e}")

    def _load_training_data(self, model_name: str) -> Optional[List[Dict[str, Any]]]:
        """Load training data for a model."""
        training_file = self.training_data_dir / f"{model_name}_training.json"

        if training_file.exists():
            try:
                with open(training_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Error loading training data for {model_name}: {e}")

        return None

    def _prepare_training_data(self, training_data: List[Dict[str, Any]], model_name: str) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """Prepare training data for model."""
        try:
            X = []
            y = []

            for sample in training_data:
                features = sample.get("features", {})
                label = sample.get("label")

                if model_name == "threat_classifier":
                    feature_vector = self._extract_features(features, model_name)
                elif model_name == "malware_family_detector":
                    feature_vector = self._extract_malware_features(features)
                elif model_name == "anomaly_detector":
                    feature_vector = self._extract_behavioral_features(features)
                elif model_name == "behavioral_analyzer":
                    feature_vector = self._extract_sequence_features(features)
                elif model_name == "evasion_detector":
                    feature_vector = self._extract_evasion_features(features)
                elif model_name == "response_optimizer":
                    system_context = sample.get("system_context", {})
                    feature_vector = self._extract_optimization_features(features, system_context)
                else:
                    continue

                if feature_vector is not None and label is not None:
                    X.append(feature_vector)
                    y.append(label)

            if X and y:
                return np.array(X), np.array(y)
            else:
                return None, None

        except Exception as e:
            self.logger.error(f"Error preparing training data: {e}")
            return None, None

    def _calculate_metrics(self, y_true, y_pred, model_name: str) -> ModelMetrics:
        """Calculate model performance metrics."""
        try:
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

            accuracy = accuracy_score(y_true, y_pred)
            precision = precision_score(y_true, y_pred, average='weighted', zero_division=0)
            recall = recall_score(y_true, y_pred, average='weighted', zero_division=0)
            f1 = f1_score(y_true, y_pred, average='weighted', zero_division=0)

            # AUC score for binary classification
            auc = 0.0
            try:
                if len(np.unique(y_true)) == 2:
                    auc = roc_auc_score(y_true, y_pred)
            except:
                pass

            return ModelMetrics(
                accuracy=accuracy,
                precision=precision,
                recall=recall,
                f1_score=f1,
                auc_score=auc,
                last_updated=datetime.now().isoformat(),
                training_samples=len(y_true),
                prediction_count=0
            )

        except Exception as e:
            self.logger.error(f"Error calculating metrics: {e}")
            return self._create_default_metrics(model_name)

    async def get_model_statistics(self) -> Dict[str, Any]:
        """Get comprehensive model statistics."""
        try:
            statistics = {
                "models_loaded": len(self.models),
                "models_available": list(self.models.keys()),
                "auto_retrain_enabled": self.auto_retrain_enabled,
                "ensemble_enabled": self.ensemble_enabled,
                "model_details": {}
            }

            for model_name, metrics in self.model_metrics.items():
                statistics["model_details"][model_name] = {
                    "accuracy": metrics.accuracy,
                    "precision": metrics.precision,
                    "recall": metrics.recall,
                    "f1_score": metrics.f1_score,
                    "auc_score": metrics.auc_score,
                    "last_updated": metrics.last_updated,
                    "training_samples": metrics.training_samples,
                    "prediction_count": metrics.prediction_count,
                    "model_type": MLModelConfig.MODEL_CONFIGS.get(model_name, {}).get("type", "unknown")
                }

            return statistics

        except Exception as e:
            self.logger.error(f"Error getting model statistics: {e}")
            return {"error": str(e)}

    async def add_training_sample(self, model_name: str, features: Dict[str, Any],
                                label: Any, system_context: Dict[str, Any] = None):
        """Add a new training sample for a model."""
        try:
            training_file = self.training_data_dir / f"{model_name}_training.json"

            # Load existing data
            training_data = []
            if training_file.exists():
                with open(training_file, 'r') as f:
                    training_data = json.load(f)

            # Add new sample
            new_sample = {
                "features": features,
                "label": label,
                "timestamp": datetime.now().isoformat()
            }

            if system_context:
                new_sample["system_context"] = system_context

            training_data.append(new_sample)

            # Keep only recent samples (last 10000)
            if len(training_data) > 10000:
                training_data = training_data[-10000:]

            # Save updated data
            with open(training_file, 'w') as f:
                json.dump(training_data, f, indent=2)

            self.logger.debug(f"Added training sample for {model_name}")

        except Exception as e:
            self.logger.error(f"Error adding training sample: {e}")

    def shutdown(self):
        """Shutdown ML models manager."""
        self.background_tasks_running = False

        # Save all metrics
        for model_name in self.model_metrics.keys():
            self._save_model_metrics(model_name)

        # Save all models
        for model_name, model in self.models.items():
            try:
                model_path = self.models_dir / f"{model_name}.joblib"
                joblib.dump(model, model_path)

                if model_name in self.scalers:
                    scaler_path = self.models_dir / f"{model_name}_scaler.joblib"
                    joblib.dump(self.scalers[model_name], scaler_path)

                if model_name in self.encoders:
                    encoder_path = self.models_dir / f"{model_name}_encoder.joblib"
                    joblib.dump(self.encoders[model_name], encoder_path)

            except Exception as e:
                self.logger.error(f"Error saving model {model_name}: {e}")

        self.logger.info("ML Models Manager shutdown complete")