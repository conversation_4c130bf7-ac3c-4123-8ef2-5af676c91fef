#pragma once

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <chrono>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <queue>

#ifdef _WIN32
    #include <windows.h>
    #include <wtsapi32.h>
    #include <netapi32.h>
#else
    #include <unistd.h>
    #include <sys/types.h>
    #include <sys/stat.h>
    #include <fcntl.h>
    #include <signal.h>
#endif

// Forward declarations
class IsolationManager;
class NotificationSystem;
class QuarantineManager;
class PermissionManager;
class HoneypotManager;
class RecoverySystem;

/**
 * @brief Threat levels for response classification
 */
enum class ThreatLevel {
    SAFE = 0,
    SUSPICIOUS = 1,
    MALICIOUS = 2,
    CRITICAL = 3,
    ADVANCED_PERSISTENT = 4
};

/**
 * @brief Response action types
 */
enum class ResponseAction {
    ALLOW_ACCESS = 0,
    RESTRICT_ACCESS = 1,
    QUARANTINE = 2,
    ISOLATE = 3,
    TERMINATE = 4,
    HONEYPOT_REDIRECT = 5,
    DEEP_ANALYSIS = 6,
    NETWORK_ISOLATION = 7,
    PROCESS_TERMINATION = 8,
    SYSTEM_LOCKDOWN = 9
};

/**
 * @brief Analysis results structure
 */
struct AnalysisResults {
    std::string file_path;
    std::string file_hash_sha256;
    std::string file_hash_md5;
    std::string file_hash_sha1;
    ThreatLevel threat_level;
    double threat_score;
    std::vector<std::string> detected_threats;
    std::map<std::string, std::string> behavioral_indicators;
    std::map<std::string, std::string> static_indicators;
    std::map<std::string, std::string> network_indicators;
    std::chrono::system_clock::time_point analysis_timestamp;
    std::string analysis_engine_version;
    std::map<std::string, std::string> metadata;
};

/**
 * @brief Response execution result
 */
struct ResponseResult {
    bool success;
    std::vector<ResponseAction> actions_taken;
    std::string quarantine_location;
    std::string honeypot_location;
    std::string isolation_id;
    std::vector<std::string> notifications_sent;
    std::map<std::string, std::string> forensic_data;
    std::string error_message;
    std::chrono::system_clock::time_point execution_timestamp;
    double execution_time_ms;
};

/**
 * @brief Response configuration structure
 */
struct ResponseConfig {
    // General settings
    bool enabled;
    std::string base_directory;
    std::string log_level;
    
    // Isolation settings
    bool network_isolation_enabled;
    bool process_isolation_enabled;
    bool file_system_isolation_enabled;
    
    // Notification settings
    bool email_notifications_enabled;
    bool slack_notifications_enabled;
    bool sms_notifications_enabled;
    bool webhook_notifications_enabled;
    
    // Quarantine settings
    std::string quarantine_directory;
    bool encryption_enabled;
    std::string encryption_key;
    
    // Honeypot settings
    std::string honeypot_directory;
    bool honeypot_enabled;
    std::vector<std::string> honeypot_environments;
    
    // Permission settings
    bool dynamic_permissions_enabled;
    bool apploader_integration_enabled;
    bool selinux_integration_enabled;
    
    // Recovery settings
    bool auto_recovery_enabled;
    std::string backup_directory;
    int backup_retention_days;
    
    // Advanced settings
    int max_concurrent_responses;
    int response_timeout_seconds;
    bool blockchain_integration_enabled;
    bool ml_model_updates_enabled;
};

/**
 * @brief Main Response Engine Class
 * 
 * This class orchestrates all response activities after dynamic analysis.
 * It integrates multiple specialized managers to provide comprehensive
 * threat response capabilities with high security and performance standards.
 */
class ResponseEngine {
public:
    /**
     * @brief Constructor
     * @param config Response configuration
     */
    explicit ResponseEngine(const ResponseConfig& config);
    
    /**
     * @brief Destructor
     */
    ~ResponseEngine();
    
    /**
     * @brief Initialize the response engine
     * @return true if initialization successful, false otherwise
     */
    bool initialize();
    
    /**
     * @brief Shutdown the response engine
     */
    void shutdown();
    
    /**
     * @brief Check if the engine is running
     * @return true if running, false otherwise
     */
    bool is_running() const;
    
    /**
     * @brief Process analysis results and execute appropriate response
     * @param results Analysis results from dynamic analysis
     * @return Response execution result
     */
    ResponseResult process_analysis_results(const AnalysisResults& results);
    
    /**
     * @brief Execute specific response action
     * @param action Response action to execute
     * @param file_path Target file path
     * @param metadata Additional metadata
     * @return Response execution result
     */
    ResponseResult execute_response_action(ResponseAction action, 
                                         const std::string& file_path,
                                         const std::map<std::string, std::string>& metadata = {});
    
    /**
     * @brief Get current response statistics
     * @return Map of statistics
     */
    std::map<std::string, uint64_t> get_response_statistics() const;
    
    /**
     * @brief Get active response sessions
     * @return Vector of active session IDs
     */
    std::vector<std::string> get_active_sessions() const;
    
    /**
     * @brief Cancel active response session
     * @param session_id Session ID to cancel
     * @return true if cancelled successfully, false otherwise
     */
    bool cancel_response_session(const std::string& session_id);
    
    /**
     * @brief Update response configuration
     * @param new_config New configuration
     * @return true if updated successfully, false otherwise
     */
    bool update_configuration(const ResponseConfig& new_config);
    
    /**
     * @brief Get current configuration
     * @return Current configuration
     */
    ResponseConfig get_configuration() const;

private:
    // Configuration
    ResponseConfig config_;
    
    // Component managers
    std::unique_ptr<IsolationManager> isolation_manager_;
    std::unique_ptr<NotificationSystem> notification_system_;
    std::unique_ptr<QuarantineManager> quarantine_manager_;
    std::unique_ptr<PermissionManager> permission_manager_;
    std::unique_ptr<HoneypotManager> honeypot_manager_;
    std::unique_ptr<RecoverySystem> recovery_system_;
    
    // Threading and synchronization
    std::atomic<bool> running_;
    std::thread response_worker_thread_;
    std::mutex response_queue_mutex_;
    std::condition_variable response_queue_cv_;
    std::queue<std::pair<AnalysisResults, std::promise<ResponseResult>>> response_queue_;
    
    // Statistics and monitoring
    mutable std::mutex stats_mutex_;
    std::map<std::string, uint64_t> response_statistics_;
    std::map<std::string, std::chrono::system_clock::time_point> active_sessions_;
    
    // Private methods
    void response_worker_loop();
    ResponseResult execute_safe_file_strategy(const AnalysisResults& results);
    ResponseResult execute_suspicious_file_strategy(const AnalysisResults& results);
    ResponseResult execute_malicious_file_strategy(const AnalysisResults& results);
    ResponseResult execute_critical_threat_strategy(const AnalysisResults& results);
    ResponseResult execute_advanced_persistent_threat_strategy(const AnalysisResults& results);
    
    std::string generate_session_id() const;
    void update_statistics(const std::string& metric, uint64_t value = 1);
    void log_response_action(const ResponseResult& result, const AnalysisResults& analysis);
    
    bool initialize_components();
    void cleanup_components();
    
    // Platform-specific methods
#ifdef _WIN32
    bool initialize_windows_security();
    void cleanup_windows_security();
#else
    bool initialize_linux_security();
    void cleanup_linux_security();
#endif
};

/**
 * @brief Response Engine Factory
 */
class ResponseEngineFactory {
public:
    /**
     * @brief Create response engine instance
     * @param config Configuration
     * @return Unique pointer to response engine
     */
    static std::unique_ptr<ResponseEngine> create(const ResponseConfig& config);
    
    /**
     * @brief Create default configuration
     * @return Default response configuration
     */
    static ResponseConfig create_default_config();
    
    /**
     * @brief Load configuration from file
     * @param config_file Path to configuration file
     * @return Loaded configuration
     */
    static ResponseConfig load_config_from_file(const std::string& config_file);
    
    /**
     * @brief Save configuration to file
     * @param config Configuration to save
     * @param config_file Path to configuration file
     * @return true if saved successfully, false otherwise
     */
    static bool save_config_to_file(const ResponseConfig& config, const std::string& config_file);
};
