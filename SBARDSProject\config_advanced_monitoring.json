{"dynamic_analysis": {"enabled": true, "analysis_timeout_seconds": 300, "startup_timeout_seconds": 30, "debug": false, "monitoring": {"api_hooking": {"enabled": true, "hook_level": "kernel", "deep_call_analysis": true, "call_sequence_tracking": true, "parameter_validation": true, "return_value_analysis": true, "monitor_syscalls": true, "monitor_winapi": true, "log_parameters": true, "log_return_values": true, "max_call_depth": 10, "hook_timeout_ms": 5000, "target_functions": ["NtCreateFile", "NtWriteFile", "NtReadFile", "NtDeleteFile", "NtCreateProcess", "NtWriteVirtualMemory", "NtSetContextThread", "RegSetValueEx", "RegCreateKeyEx", "RegDeleteKey", "CryptEncrypt", "CryptDecrypt", "CryptGenKey", "socket", "connect", "send", "recv", "WSAStartup", "CreateRemoteThread", "SetWindowsHookEx", "WriteProcessMemory"], "target_modules": ["ntdll.dll", "kernel32.dll", "advapi32.dll", "ws2_32.dll", "crypt32.dll", "user32.dll", "wininet.dll"]}, "file_system": {"enabled": true, "monitor_creation": true, "monitor_modification": true, "monitor_deletion": true, "monitor_access_patterns": true, "detect_encryption": true, "track_file_patterns": true, "bulk_operation_detection": true, "sensitive_file_paths": ["/etc/passwd", "/etc/shadow", "/etc/hosts", "/etc/sudoers", "C:\\Windows\\System32\\config\\SAM", "C:\\Windows\\System32\\config\\SECURITY", "C:\\Windows\\System32\\config\\SYSTEM", "C:\\Windows\\System32\\drivers\\etc\\hosts", "C:\\Users\\<USER>\\Documents\\*", "C:\\Users\\<USER>\\Desktop\\*", "/home/<USER>/Documents/*", "/home/<USER>/.ssh/*", "*.wallet", "*.keystore", "*private.key*"], "monitored_extensions": [".exe", ".dll", ".bat", ".cmd", ".ps1", ".vbs", ".js", ".doc", ".docx", ".xls", ".xlsx", ".pdf", ".zip", ".rar", ".encrypted", ".locked", ".crypto", ".crypt"], "encryption_indicators": ["file_size_change_ratio > 0.1", "entropy_increase > 0.2", "extension_change_to_unknown", "bulk_rename_operations"]}, "network": {"enabled": true, "packet_capture": true, "protocol_analysis": true, "tls_inspection": true, "c2_detection": true, "dns_monitoring": true, "deep_packet_inspection": true, "connection_pattern_analysis": true, "suspicious_domains": ["*.bit", "*.onion", "tempuri.org", "example.com", "*.tk", "*.ml", "*.ga", "*.cf", "duckdns.org", "no-ip.com", "ddns.net"], "blocked_ips": ["127.0.0.1", "0.0.0.0", "***************"], "suspicious_ports": [4444, 5555, 6666, 7777, 8080, 9999, 1337, 31337, 6667, 6697, 1234, 12345, 54321], "c2_detection_patterns": ["regular_beacon_intervals", "encrypted_to_unusual_ports", "high_frequency_connections", "suspicious_user_agents", "base64_encoded_traffic"]}, "process": {"enabled": true, "monitor_creation": true, "monitor_injection": true, "monitor_hollowing": true, "track_parent_child": true, "detect_code_injection": true, "monitor_dll_loading": true, "track_thread_creation": true, "injection_techniques": ["dll_injection", "process_hollowing", "atom_bombing", "manual_dll_loading", "reflective_dll_loading", "process_doppelganging", "thread_execution_hijacking"]}, "memory": {"enabled": true, "periodic_dumps": false, "analyze_heap": true, "analyze_stack": true, "detect_injection": true, "volatility_analysis": false, "memory_scanning_interval": 30, "dump_on_suspicious_activity": true, "injection_detection": ["unexpected_executable_memory", "modified_process_memory", "suspicious_memory_patterns", "shellcode_signatures"]}, "registry": {"enabled": true, "monitor_modifications": true, "track_persistence": true, "detect_security_changes": true, "critical_registry_keys": ["HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce", "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services", "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon", "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies", "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\SafeBoot", "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows Defender", "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows Defender"], "persistence_patterns": ["Run", "RunOnce", "RunServices", "RunServicesOnce", "Winlogon", "Shell", "Userinit", "TaskMan", "AppInit_DLLs", "Image File Execution Options", "ServiceDll", "Notify", "SharedTaskScheduler"]}}, "behavioral_analysis": {"enabled": true, "ml_enabled": true, "real_time_analysis": true, "threat_scoring": true, "ioc_extraction": true, "ml_model_path": "models/behavioral_analysis.pkl", "ransomware_detection": {"enabled": true, "file_encryption_threshold": 10, "shadow_copy_monitoring": true, "backup_deletion_detection": true, "crypto_api_monitoring": true, "bulk_file_operations": true, "extension_change_detection": true, "ransom_note_detection": true, "indicators": ["bulk_file_encryption", "shadow_copy_deletion", "backup_file_deletion", "crypto_api_usage", "ransom_note_creation", "file_extension_changes"]}, "apt_detection": {"enabled": true, "lateral_movement": true, "persistence_mechanisms": true, "data_exfiltration": true, "c2_communication": true, "process_injection": true, "indicators": ["lateral_movement_attempts", "credential_harvesting", "data_staging", "encrypted_c2_traffic", "living_off_the_land_techniques"]}, "resource_analysis": {"cpu_monitoring": true, "memory_monitoring": true, "io_monitoring": true, "network_monitoring": true, "anomaly_detection": true, "performance_thresholds": {"cpu_usage_percent": 80, "memory_usage_mb": 2048, "disk_io_mbps": 100, "network_io_mbps": 50}}}, "sandbox": {"type": "hybrid", "docker_enabled": true, "vm_enabled": false, "container_image": "sbards/analysis:latest", "network_isolation": true, "file_isolation": true, "memory_isolation": true, "escape_prevention": true, "vm_snapshots": {"windows10": "win10_clean", "windows11": "win11_clean", "ubuntu20": "ubuntu20_clean", "ubuntu22": "ubuntu22_clean"}}, "user_simulation": {"enabled": true, "install_applications": true, "simulate_interactions": true, "time_delays": true, "realistic_data": true, "applications": ["office_suite", "web_browser", "pdf_reader", "media_player"], "interaction_patterns": ["mouse_movements", "keyboard_input", "window_operations", "file_operations", "application_usage"]}, "cpp_components": {"enabled": true, "sandbox_engine": "scanner_core/cpp/sandbox_engine", "api_hooking": "scanner_core/cpp/api_hooking", "memory_analyzer": "scanner_core/cpp/memory_analyzer", "performance_monitor": "scanner_core/cpp/performance_monitor", "build_on_startup": false, "library_paths": ["scanner_core/cpp/build/lib", "scanner_core/cpp/lib", "lib"]}, "cuckoo": {"enabled": false, "host": "localhost", "port": 8090, "api_token": "", "timeout": 300, "machines": {"windows10": "win10vm", "windows11": "win11vm", "ubuntu20": "ubuntu20vm"}, "enable_memory_dump": true, "enable_network_capture": true}, "performance": {"parallel_analysis": true, "max_concurrent_analyses": 4, "analysis_queue_size": 100, "event_buffer_size": 10000, "monitoring_interval_ms": 500, "cleanup_interval_seconds": 300, "resource_limits": {"max_memory_mb": 4096, "max_cpu_percent": 70, "max_disk_io_mbps": 100, "max_network_io_mbps": 50}, "optimization": {"enable_caching": true, "cache_size_mb": 512, "compress_events": true, "batch_processing": true, "async_io": true}}, "security": {"encryption": {"enabled": true, "algorithm": "AES-256-GCM", "key_rotation_hours": 24}, "access_control": {"enabled": true, "require_authentication": true, "role_based_access": true, "audit_logging": true}, "isolation": {"network_isolation": true, "process_isolation": true, "file_system_isolation": true, "memory_isolation": true}}, "logging": {"level": "INFO", "file_path": "logs/dynamic_analysis.log", "max_file_size_mb": 100, "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "enable_console": true, "enable_file": true, "enable_syslog": false}, "output": {"format": "json", "compression": true, "encryption": true, "retention_days": 30, "export_formats": ["json", "csv", "xml", "yaml"], "real_time_streaming": false}}}