#!/usr/bin/env python3
"""
SBARDS System Integration Module
Integration between Enhanced Response System and Main SBARDS Pipeline

This module provides:
- Seamless integration with main SBARDS system
- Cross-phase data sharing and correlation
- Performance optimization across phases
- Unified configuration management
- Enhanced logging and monitoring
"""

import os
import sys
import json
import logging
import asyncio
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple
import threading
from dataclasses import dataclass, asdict

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import enhanced response system
from .enhanced_response_system import EnhancedResponseSystem

# Import other SBARDS phases (when available)
try:
    from ..static_analysis import StaticAnalysisEngine
    STATIC_ANALYSIS_AVAILABLE = True
except ImportError:
    STATIC_ANALYSIS_AVAILABLE = False

try:
    from ..dynamic_analysis import DynamicAnalysisEngine
    DYNAMIC_ANALYSIS_AVAILABLE = True
except ImportError:
    DYNAMIC_ANALYSIS_AVAILABLE = False

try:
    from ..yara_rules import YaraRulesEngine
    YARA_RULES_AVAILABLE = True
except ImportError:
    YARA_RULES_AVAILABLE = False

@dataclass
class IntegrationMetrics:
    """Integration performance metrics."""
    total_files_processed: int = 0
    enhanced_responses: int = 0
    cross_phase_correlations: int = 0
    performance_improvements: int = 0
    accuracy_gains: float = 0.0
    processing_time_reduction: float = 0.0
    false_positive_reduction: float = 0.0

class SBARDSSystemIntegration:
    """SBARDS System Integration Manager."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize system integration."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.SystemIntegration")

        # Initialize enhanced response system
        self.enhanced_response = EnhancedResponseSystem(config)

        # Integration settings
        self.integration_config = config.get("system_integration", {})
        self.cross_phase_enabled = self.integration_config.get("cross_phase_enabled", True)
        self.performance_optimization = self.integration_config.get("performance_optimization", True)
        self.unified_logging = self.integration_config.get("unified_logging", True)

        # Phase engines (initialize if available)
        self.static_engine = None
        self.dynamic_engine = None
        self.yara_engine = None

        if STATIC_ANALYSIS_AVAILABLE and self.integration_config.get("static_analysis_enabled", True):
            self.static_engine = StaticAnalysisEngine(config.get("static_analysis", {}))

        if DYNAMIC_ANALYSIS_AVAILABLE and self.integration_config.get("dynamic_analysis_enabled", True):
            self.dynamic_engine = DynamicAnalysisEngine(config.get("dynamic_analysis", {}))

        if YARA_RULES_AVAILABLE and self.integration_config.get("yara_rules_enabled", True):
            self.yara_engine = YaraRulesEngine(config.get("yara_rules", {}))

        # Integration metrics
        self.metrics = IntegrationMetrics()

        # Cross-phase data cache
        self.cross_phase_cache = {}
        self.cache_lock = threading.RLock()

        # Performance monitoring
        self.performance_history = []

        self.logger.info("SBARDS System Integration initialized successfully")

    async def process_file_comprehensive(self, file_path: str) -> Dict[str, Any]:
        """Process file through complete SBARDS pipeline with enhanced integration."""
        try:
            start_time = datetime.now(timezone.utc)
            self.logger.info(f"Starting comprehensive analysis for: {file_path}")

            # Step 1: Initialize analysis context
            analysis_context = await self._initialize_analysis_context(file_path)

            # Step 2: Run analysis phases in optimized order
            analysis_results = await self._run_analysis_phases(file_path, analysis_context)

            # Step 3: Cross-phase correlation and enrichment
            if self.cross_phase_enabled:
                analysis_results = await self._perform_cross_phase_correlation(analysis_results)

            # Step 4: Enhanced response processing
            response_result = await self.enhanced_response.process_enhanced_analysis_results(analysis_results)

            # Step 5: Performance optimization feedback
            if self.performance_optimization:
                await self._update_performance_feedback(analysis_results, response_result)

            # Step 6: Update metrics
            end_time = datetime.now(timezone.utc)
            processing_time = (end_time - start_time).total_seconds()
            self._update_integration_metrics(analysis_results, response_result, processing_time)

            # Combine final results
            final_result = {
                "file_path": file_path,
                "analysis_results": analysis_results,
                "enhanced_response": response_result,
                "integration_metadata": {
                    "processing_time_seconds": processing_time,
                    "cross_phase_enabled": self.cross_phase_enabled,
                    "phases_used": self._get_phases_used(),
                    "performance_optimized": self.performance_optimization,
                    "timestamp": end_time.isoformat()
                },
                "success": True
            }

            self.logger.info(f"Comprehensive analysis completed for {file_path} in {processing_time:.2f}s")
            return final_result

        except Exception as e:
            self.logger.error(f"Error in comprehensive file processing: {e}")
            return {
                "file_path": file_path,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    async def _initialize_analysis_context(self, file_path: str) -> Dict[str, Any]:
        """Initialize analysis context with file metadata."""
        try:
            file_path_obj = Path(file_path)

            context = {
                "file_path": str(file_path_obj.absolute()),
                "file_name": file_path_obj.name,
                "file_size": file_path_obj.stat().st_size if file_path_obj.exists() else 0,
                "file_extension": file_path_obj.suffix.lower(),
                "analysis_start_time": datetime.now(timezone.utc).isoformat(),
                "analysis_id": f"SBARDS_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(file_path) % 10000:04d}",
                "phases_to_run": [],
                "optimization_hints": {}
            }

            # Determine phases to run based on file type and configuration
            if self.static_engine:
                context["phases_to_run"].append("static_analysis")

            if self.dynamic_engine and context["file_extension"] in [".exe", ".dll", ".bat", ".cmd", ".scr"]:
                context["phases_to_run"].append("dynamic_analysis")

            if self.yara_engine:
                context["phases_to_run"].append("yara_rules")

            # Always run enhanced response
            context["phases_to_run"].append("enhanced_response")

            # Add optimization hints based on file characteristics
            if context["file_size"] > 100 * 1024 * 1024:  # > 100MB
                context["optimization_hints"]["large_file"] = True
                context["optimization_hints"]["parallel_processing"] = True

            if context["file_extension"] in [".zip", ".rar", ".7z"]:
                context["optimization_hints"]["archive_file"] = True
                context["optimization_hints"]["extract_and_analyze"] = True

            return context

        except Exception as e:
            self.logger.error(f"Error initializing analysis context: {e}")
            return {"file_path": file_path, "error": str(e)}

    async def _run_analysis_phases(self, file_path: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Run analysis phases in optimized order."""
        try:
            analysis_results = {
                "file_path": file_path,
                "analysis_context": context,
                "phases_completed": [],
                "phases_failed": [],
                "cross_phase_data": {}
            }

            # Phase 1: Static Analysis (fast, provides foundation)
            if "static_analysis" in context["phases_to_run"] and self.static_engine:
                try:
                    self.logger.debug("Running static analysis...")
                    static_result = await self._run_static_analysis(file_path, context)
                    analysis_results["static_analysis"] = static_result
                    analysis_results["phases_completed"].append("static_analysis")

                    # Extract file info and hashes
                    if "file_info" in static_result:
                        analysis_results["file_info"] = static_result["file_info"]
                    if "file_hash" in static_result:
                        analysis_results["file_hash"] = static_result["file_hash"]

                except Exception as e:
                    self.logger.error(f"Static analysis failed: {e}")
                    analysis_results["phases_failed"].append("static_analysis")

            # Phase 2: YARA Rules (fast, uses static analysis results)
            if "yara_rules" in context["phases_to_run"] and self.yara_engine:
                try:
                    self.logger.debug("Running YARA rules analysis...")
                    yara_result = await self._run_yara_analysis(file_path, analysis_results, context)
                    analysis_results["yara_analysis"] = yara_result
                    analysis_results["phases_completed"].append("yara_rules")

                except Exception as e:
                    self.logger.error(f"YARA analysis failed: {e}")
                    analysis_results["phases_failed"].append("yara_rules")

            # Phase 3: Dynamic Analysis (slow, uses previous results for optimization)
            if "dynamic_analysis" in context["phases_to_run"] and self.dynamic_engine:
                try:
                    self.logger.debug("Running dynamic analysis...")
                    dynamic_result = await self._run_dynamic_analysis(file_path, analysis_results, context)
                    analysis_results["dynamic_analysis"] = dynamic_result
                    analysis_results["phases_completed"].append("dynamic_analysis")

                except Exception as e:
                    self.logger.error(f"Dynamic analysis failed: {e}")
                    analysis_results["phases_failed"].append("dynamic_analysis")

            # Phase 4: Threat Assessment (combines all previous results)
            threat_assessment = await self._generate_threat_assessment(analysis_results)
            analysis_results["threat_assessment"] = threat_assessment

            return analysis_results

        except Exception as e:
            self.logger.error(f"Error running analysis phases: {e}")
            return {"file_path": file_path, "error": str(e)}

    async def _run_static_analysis(self, file_path: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Run static analysis with integration optimizations."""
        try:
            # Use static engine if available, otherwise simulate
            if self.static_engine:
                return await self.static_engine.analyze_file(file_path)
            else:
                # Simulated static analysis for demo
                import hashlib

                with open(file_path, 'rb') as f:
                    content = f.read()

                return {
                    "file_info": {
                        "size": len(content),
                        "mime_type": "application/octet-stream",
                        "extension": Path(file_path).suffix
                    },
                    "file_hash": {
                        "md5": hashlib.md5(content).hexdigest(),
                        "sha1": hashlib.sha1(content).hexdigest(),
                        "sha256": hashlib.sha256(content).hexdigest()
                    },
                    "entropy": 6.5,  # Simulated entropy
                    "strings": ["sample string"],
                    "pe_analysis": {
                        "sections": [{"name": ".text", "entropy": 6.0}],
                        "imports": ["kernel32.dll"]
                    }
                }

        except Exception as e:
            self.logger.error(f"Error in static analysis: {e}")
            return {"error": str(e)}

    async def _run_yara_analysis(self, file_path: str, analysis_results: Dict[str, Any],
                                context: Dict[str, Any]) -> Dict[str, Any]:
        """Run YARA analysis with cross-phase optimization."""
        try:
            # Use YARA engine if available, otherwise simulate
            if self.yara_engine:
                return await self.yara_engine.scan_file(file_path)
            else:
                # Simulated YARA analysis
                static_analysis = analysis_results.get("static_analysis", {})
                entropy = static_analysis.get("entropy", 0)

                matches = []
                if entropy > 7.0:
                    matches.append({
                        "rule_name": "high_entropy_file",
                        "family": "packer",
                        "confidence": 0.8,
                        "description": "High entropy detected"
                    })

                return {
                    "matches": matches,
                    "scan_time": 0.5,
                    "rules_loaded": 1000
                }

        except Exception as e:
            self.logger.error(f"Error in YARA analysis: {e}")
            return {"error": str(e)}

    async def _run_dynamic_analysis(self, file_path: str, analysis_results: Dict[str, Any],
                                  context: Dict[str, Any]) -> Dict[str, Any]:
        """Run dynamic analysis with intelligent optimization."""
        try:
            # Use dynamic engine if available, otherwise simulate
            if self.dynamic_engine:
                # Optimize dynamic analysis based on static results
                static_analysis = analysis_results.get("static_analysis", {})
                yara_analysis = analysis_results.get("yara_analysis", {})

                # Adjust timeout based on file characteristics
                timeout = 60  # Default timeout
                if static_analysis.get("entropy", 0) > 7.0:
                    timeout = 120  # Longer timeout for packed files

                # Skip dynamic analysis for obviously safe files
                if (static_analysis.get("signed", False) and
                    len(yara_analysis.get("matches", [])) == 0):
                    timeout = 30  # Shorter timeout for likely safe files

                return await self.dynamic_engine.analyze_file(file_path, timeout=timeout)
            else:
                # Simulated dynamic analysis
                return {
                    "api_calls": [
                        {"api_name": "CreateFileA", "timestamp": 1000},
                        {"api_name": "WriteFile", "timestamp": 1100}
                    ],
                    "network_connections": [],
                    "file_operations": [
                        {"operation": "write", "file_path": "temp.txt"}
                    ],
                    "registry_changes": [],
                    "process_info": {
                        "main": {"pid": 1234, "name": "sample.exe"}
                    },
                    "execution_time": 30.0
                }

        except Exception as e:
            self.logger.error(f"Error in dynamic analysis: {e}")
            return {"error": str(e)}

    async def _generate_threat_assessment(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive threat assessment."""
        try:
            # Combine results from all phases
            static_analysis = analysis_results.get("static_analysis", {})
            yara_analysis = analysis_results.get("yara_analysis", {})
            dynamic_analysis = analysis_results.get("dynamic_analysis", {})

            # Calculate threat score
            threat_score = 0.0
            confidence = 0.0
            threat_level = "safe"
            risk_factors = []

            # Static analysis contribution
            entropy = static_analysis.get("entropy", 0)
            if entropy > 7.0:
                threat_score += 0.3
                risk_factors.append("high_entropy")

            if static_analysis.get("packed", False):
                threat_score += 0.2
                risk_factors.append("packed_executable")

            # YARA analysis contribution
            yara_matches = yara_analysis.get("matches", [])
            if yara_matches:
                threat_score += len(yara_matches) * 0.2
                risk_factors.extend([match.get("family", "unknown") for match in yara_matches])

            # Dynamic analysis contribution
            network_connections = dynamic_analysis.get("network_connections", [])
            if network_connections:
                threat_score += len(network_connections) * 0.1
                risk_factors.append("network_activity")

            registry_changes = dynamic_analysis.get("registry_changes", [])
            if registry_changes:
                threat_score += len(registry_changes) * 0.15
                risk_factors.append("registry_modification")

            # Determine threat level
            if threat_score < 0.3:
                threat_level = "safe"
                confidence = 0.8
            elif threat_score < 0.7:
                threat_level = "suspicious"
                confidence = 0.7
            elif threat_score < 0.9:
                threat_level = "malicious"
                confidence = 0.9
            else:
                threat_level = "critical"
                confidence = 0.95

            return {
                "overall_threat_level": threat_level,
                "threat_score": min(threat_score, 1.0),
                "confidence": confidence,
                "risk_factors": list(set(risk_factors)),
                "assessment_method": "integrated_multi_phase",
                "phases_used": analysis_results.get("phases_completed", [])
            }

        except Exception as e:
            self.logger.error(f"Error generating threat assessment: {e}")
            return {
                "overall_threat_level": "unknown",
                "threat_score": 0.5,
                "confidence": 0.0,
                "error": str(e)
            }

    async def _perform_cross_phase_correlation(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Perform cross-phase correlation and enrichment."""
        try:
            if not self.cross_phase_enabled:
                return analysis_results

            # Extract data from each phase
            static_data = analysis_results.get("static_analysis", {})
            yara_data = analysis_results.get("yara_analysis", {})
            dynamic_data = analysis_results.get("dynamic_analysis", {})

            # Cross-phase correlations
            correlations = {
                "static_yara_correlation": self._correlate_static_yara(static_data, yara_data),
                "static_dynamic_correlation": self._correlate_static_dynamic(static_data, dynamic_data),
                "yara_dynamic_correlation": self._correlate_yara_dynamic(yara_data, dynamic_data),
                "multi_phase_indicators": self._extract_multi_phase_indicators(static_data, yara_data, dynamic_data)
            }

            # Add correlations to results
            analysis_results["cross_phase_correlations"] = correlations

            # Enrich threat assessment with correlations
            if "threat_assessment" in analysis_results:
                analysis_results["threat_assessment"] = self._enrich_threat_assessment_with_correlations(
                    analysis_results["threat_assessment"], correlations
                )

            self.metrics.cross_phase_correlations += 1
            return analysis_results

        except Exception as e:
            self.logger.error(f"Error in cross-phase correlation: {e}")
            return analysis_results

    def _correlate_static_yara(self, static_data: Dict[str, Any], yara_data: Dict[str, Any]) -> Dict[str, Any]:
        """Correlate static analysis with YARA results."""
        correlation = {
            "entropy_yara_match": False,
            "packer_detection_consistency": False,
            "family_classification_confidence": 0.0
        }

        try:
            # Check if high entropy correlates with packer YARA rules
            entropy = static_data.get("entropy", 0)
            yara_matches = yara_data.get("matches", [])

            if entropy > 7.0:
                packer_matches = [m for m in yara_matches if "pack" in m.get("family", "").lower()]
                if packer_matches:
                    correlation["entropy_yara_match"] = True
                    correlation["packer_detection_consistency"] = True

            # Calculate family classification confidence
            if yara_matches:
                avg_confidence = sum(m.get("confidence", 0) for m in yara_matches) / len(yara_matches)
                correlation["family_classification_confidence"] = avg_confidence

        except Exception as e:
            self.logger.error(f"Error in static-YARA correlation: {e}")

        return correlation

    def _correlate_static_dynamic(self, static_data: Dict[str, Any], dynamic_data: Dict[str, Any]) -> Dict[str, Any]:
        """Correlate static analysis with dynamic analysis."""
        correlation = {
            "import_api_consistency": False,
            "behavior_prediction_accuracy": 0.0,
            "evasion_technique_confirmation": False
        }

        try:
            # Check if static imports match dynamic API calls
            pe_analysis = static_data.get("pe_analysis", {})
            imports = pe_analysis.get("imports", [])
            api_calls = dynamic_data.get("api_calls", [])

            if imports and api_calls:
                import_apis = set(imp.split("!")[-1] for imp in imports if "!" in imp)
                called_apis = set(call.get("api_name", "") for call in api_calls)

                if import_apis.intersection(called_apis):
                    correlation["import_api_consistency"] = True

            # Check for evasion techniques
            if static_data.get("packed", False):
                # Look for unpacking behavior in dynamic analysis
                for call in api_calls:
                    if call.get("api_name") in ["VirtualAlloc", "VirtualProtect", "WriteProcessMemory"]:
                        correlation["evasion_technique_confirmation"] = True
                        break

        except Exception as e:
            self.logger.error(f"Error in static-dynamic correlation: {e}")

        return correlation

    def _correlate_yara_dynamic(self, yara_data: Dict[str, Any], dynamic_data: Dict[str, Any]) -> Dict[str, Any]:
        """Correlate YARA results with dynamic analysis."""
        correlation = {
            "family_behavior_consistency": False,
            "malware_capability_confirmation": False,
            "attribution_confidence": 0.0
        }

        try:
            yara_matches = yara_data.get("matches", [])

            for match in yara_matches:
                family = match.get("family", "").lower()

                # Check if behavior matches family expectations
                if family == "ransomware":
                    file_ops = dynamic_data.get("file_operations", [])
                    if any(op.get("operation") == "write" for op in file_ops):
                        correlation["family_behavior_consistency"] = True

                elif family == "trojan":
                    network_conns = dynamic_data.get("network_connections", [])
                    if network_conns:
                        correlation["family_behavior_consistency"] = True

                elif family == "rootkit":
                    registry_changes = dynamic_data.get("registry_changes", [])
                    if registry_changes:
                        correlation["family_behavior_consistency"] = True

        except Exception as e:
            self.logger.error(f"Error in YARA-dynamic correlation: {e}")

        return correlation

    def _extract_multi_phase_indicators(self, static_data: Dict[str, Any],
                                      yara_data: Dict[str, Any],
                                      dynamic_data: Dict[str, Any]) -> List[str]:
        """Extract indicators that span multiple analysis phases."""
        indicators = []

        try:
            # High confidence malware indicators
            if (static_data.get("entropy", 0) > 7.0 and
                len(yara_data.get("matches", [])) > 0 and
                len(dynamic_data.get("network_connections", [])) > 0):
                indicators.append("multi_phase_malware_confirmation")

            # Evasion technique indicators
            if (static_data.get("packed", False) and
                any("pack" in m.get("family", "").lower() for m in yara_data.get("matches", [])) and
                any(call.get("api_name") in ["VirtualAlloc", "VirtualProtect"]
                    for call in dynamic_data.get("api_calls", []))):
                indicators.append("confirmed_packing_evasion")

            # Persistence indicators
            pe_analysis = static_data.get("pe_analysis", {})
            imports = pe_analysis.get("imports", [])
            registry_changes = dynamic_data.get("registry_changes", [])

            if (any("RegSetValue" in imp for imp in imports) and registry_changes):
                indicators.append("confirmed_persistence_mechanism")

            # Data exfiltration indicators
            if (any("WinInet" in imp for imp in imports) and
                len(dynamic_data.get("network_connections", [])) > 0):
                indicators.append("potential_data_exfiltration")

        except Exception as e:
            self.logger.error(f"Error extracting multi-phase indicators: {e}")

        return indicators

    def _enrich_threat_assessment_with_correlations(self, threat_assessment: Dict[str, Any],
                                                  correlations: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich threat assessment with cross-phase correlations."""
        try:
            # Adjust threat score based on correlations
            original_score = threat_assessment.get("threat_score", 0.0)
            correlation_bonus = 0.0

            # Static-YARA correlation bonus
            static_yara = correlations.get("static_yara_correlation", {})
            if static_yara.get("entropy_yara_match", False):
                correlation_bonus += 0.1
            if static_yara.get("packer_detection_consistency", False):
                correlation_bonus += 0.1

            # Multi-phase indicators bonus
            multi_phase = correlations.get("multi_phase_indicators", [])
            correlation_bonus += len(multi_phase) * 0.05

            # Update threat assessment
            new_score = min(original_score + correlation_bonus, 1.0)
            threat_assessment["threat_score"] = new_score

            # Increase confidence if correlations are strong
            if correlation_bonus > 0.1:
                original_confidence = threat_assessment.get("confidence", 0.0)
                threat_assessment["confidence"] = min(original_confidence + 0.1, 1.0)

            # Add correlation metadata
            threat_assessment["cross_phase_enrichment"] = {
                "correlation_bonus": correlation_bonus,
                "multi_phase_indicators": multi_phase,
                "enrichment_applied": True
            }

        except Exception as e:
            self.logger.error(f"Error enriching threat assessment: {e}")

        return threat_assessment

    async def _update_performance_feedback(self, analysis_results: Dict[str, Any],
                                         response_result: Dict[str, Any]):
        """Update performance feedback for optimization."""
        try:
            if not self.performance_optimization:
                return

            # Extract performance metrics
            phases_completed = analysis_results.get("phases_completed", [])
            phases_failed = analysis_results.get("phases_failed", [])

            # Calculate success rate
            total_phases = len(phases_completed) + len(phases_failed)
            success_rate = len(phases_completed) / total_phases if total_phases > 0 else 0.0

            # Extract processing times (if available)
            processing_times = {}
            for phase in phases_completed:
                phase_data = analysis_results.get(phase.replace("_", "_analysis"), {})
                if "processing_time" in phase_data:
                    processing_times[phase] = phase_data["processing_time"]

            # ML enhancement effectiveness
            ml_enhanced = response_result.get("ml_integration", False)
            ml_confidence = response_result.get("enhanced_assessment", {}).get("ml_confidence", 0.0)

            # Store performance feedback
            feedback = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "success_rate": success_rate,
                "phases_completed": phases_completed,
                "phases_failed": phases_failed,
                "processing_times": processing_times,
                "ml_enhanced": ml_enhanced,
                "ml_confidence": ml_confidence,
                "cross_phase_correlations": len(analysis_results.get("cross_phase_correlations", {}))
            }

            self.performance_history.append(feedback)

            # Keep only recent history (last 1000 entries)
            if len(self.performance_history) > 1000:
                self.performance_history = self.performance_history[-1000:]

        except Exception as e:
            self.logger.error(f"Error updating performance feedback: {e}")

    def _update_integration_metrics(self, analysis_results: Dict[str, Any],
                                  response_result: Dict[str, Any], processing_time: float):
        """Update integration performance metrics."""
        try:
            self.metrics.total_files_processed += 1

            if response_result.get("ml_integration", False):
                self.metrics.enhanced_responses += 1

            if "cross_phase_correlations" in analysis_results:
                self.metrics.cross_phase_correlations += 1

            # Calculate performance improvements (simplified)
            if processing_time < 10.0:  # Fast processing
                self.metrics.performance_improvements += 1

            # Update accuracy gains (simplified heuristic)
            ml_confidence = response_result.get("enhanced_assessment", {}).get("ml_confidence", 0.0)
            if ml_confidence > 0.8:
                self.metrics.accuracy_gains += 0.1

        except Exception as e:
            self.logger.error(f"Error updating integration metrics: {e}")

    def _get_phases_used(self) -> List[str]:
        """Get list of phases that are enabled and available."""
        phases = []

        if self.static_engine:
            phases.append("static_analysis")
        if self.dynamic_engine:
            phases.append("dynamic_analysis")
        if self.yara_engine:
            phases.append("yara_rules")

        phases.append("enhanced_response")

        return phases

    async def get_integration_statistics(self) -> Dict[str, Any]:
        """Get comprehensive integration statistics."""
        try:
            # Get enhanced response statistics
            enhanced_stats = await self.enhanced_response.get_enhanced_statistics()

            # Calculate performance metrics
            recent_performance = self.performance_history[-100:] if self.performance_history else []
            avg_success_rate = sum(p.get("success_rate", 0) for p in recent_performance) / max(len(recent_performance), 1)
            avg_ml_confidence = sum(p.get("ml_confidence", 0) for p in recent_performance) / max(len(recent_performance), 1)

            # Combine statistics
            integration_stats = {
                "integration_metrics": asdict(self.metrics),
                "enhanced_response_stats": enhanced_stats,
                "performance_metrics": {
                    "average_success_rate": avg_success_rate,
                    "average_ml_confidence": avg_ml_confidence,
                    "total_performance_entries": len(self.performance_history),
                    "phases_available": self._get_phases_used(),
                    "cross_phase_enabled": self.cross_phase_enabled,
                    "performance_optimization_enabled": self.performance_optimization
                },
                "system_configuration": {
                    "static_analysis_available": STATIC_ANALYSIS_AVAILABLE,
                    "dynamic_analysis_available": DYNAMIC_ANALYSIS_AVAILABLE,
                    "yara_rules_available": YARA_RULES_AVAILABLE,
                    "unified_logging": self.unified_logging
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            return integration_stats

        except Exception as e:
            self.logger.error(f"Error getting integration statistics: {e}")
            return {"error": str(e)}

    async def process_batch_files(self, file_paths: List[str],
                                max_concurrent: int = 5) -> List[Dict[str, Any]]:
        """Process multiple files concurrently with optimization."""
        try:
            self.logger.info(f"Processing batch of {len(file_paths)} files with max {max_concurrent} concurrent")

            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(max_concurrent)

            async def process_single_file(file_path: str) -> Dict[str, Any]:
                async with semaphore:
                    return await self.process_file_comprehensive(file_path)

            # Process files concurrently
            tasks = [process_single_file(file_path) for file_path in file_paths]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Handle exceptions
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append({
                        "file_path": file_paths[i],
                        "success": False,
                        "error": str(result)
                    })
                else:
                    processed_results.append(result)

            self.logger.info(f"Batch processing completed: {len(processed_results)} files processed")
            return processed_results

        except Exception as e:
            self.logger.error(f"Error in batch processing: {e}")
            return [{"error": str(e)}]

    def shutdown(self):
        """Shutdown system integration."""
        try:
            # Shutdown enhanced response system
            self.enhanced_response.shutdown()

            # Shutdown phase engines
            if self.static_engine and hasattr(self.static_engine, 'shutdown'):
                self.static_engine.shutdown()

            if self.dynamic_engine and hasattr(self.dynamic_engine, 'shutdown'):
                self.dynamic_engine.shutdown()

            if self.yara_engine and hasattr(self.yara_engine, 'shutdown'):
                self.yara_engine.shutdown()

            self.logger.info("SBARDS System Integration shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
