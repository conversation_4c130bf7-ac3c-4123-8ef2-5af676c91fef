#!/usr/bin/env python3
"""
SBARDS Response Data Engine - C++ Core Build Script
Cross-platform build automation for C++ Data Core

This script:
1. Detects platform and architecture
2. Checks for required dependencies
3. Builds C++ Data Engine using CMake
4. Installs Python integration
5. Runs tests and validation
"""

import os
import sys
import subprocess
import platform
import shutil
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CPPDataCoreBuildSystem:
    """C++ Data Core Build System for SBARDS Response Data Engine."""
    
    def __init__(self):
        """Initialize build system."""
        self.platform = platform.system().lower()
        self.architecture = platform.machine().lower()
        self.project_root = Path(__file__).parent
        self.cpp_core_dir = self.project_root / "cpp_core"
        self.build_dir = self.cpp_core_dir / "build"
        
        # Build configuration
        self.build_type = "Release"
        self.cmake_args = []
        self.make_args = []
        
        logger.info(f"Detected platform: {self.platform} ({self.architecture})")
    
    def check_dependencies(self) -> bool:
        """Check for required build dependencies."""
        logger.info("Checking build dependencies...")
        
        required_tools = {
            "cmake": "CMake build system",
            "git": "Git version control"
        }
        
        # Platform-specific compiler checks
        if self.platform == "windows":
            # Check for Visual Studio or MinGW
            if not self._check_windows_compiler():
                logger.error("No suitable C++ compiler found on Windows")
                return False
        else:
            # Check for GCC or Clang
            required_tools.update({
                "gcc": "GCC compiler",
                "g++": "G++ compiler"
            })
        
        # Check each required tool
        missing_tools = []
        for tool, description in required_tools.items():
            if not self._check_tool_available(tool):
                missing_tools.append(f"{tool} ({description})")
        
        if missing_tools:
            logger.error(f"Missing required tools: {', '.join(missing_tools)}")
            return False
        
        # Check for OpenSSL
        if not self._check_openssl():
            logger.warning("OpenSSL not found, some features may be disabled")
        
        # Check for zlib
        if not self._check_zlib():
            logger.warning("zlib not found, compression features may be disabled")
        
        logger.info("All dependencies satisfied")
        return True
    
    def _check_tool_available(self, tool: str) -> bool:
        """Check if a tool is available in PATH."""
        try:
            subprocess.run([tool, "--version"], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def _check_windows_compiler(self) -> bool:
        """Check for Windows C++ compiler."""
        # Check for Visual Studio
        vs_paths = [
            "C:/Program Files/Microsoft Visual Studio",
            "C:/Program Files (x86)/Microsoft Visual Studio"
        ]
        
        for vs_path in vs_paths:
            if Path(vs_path).exists():
                logger.info("Found Visual Studio")
                return True
        
        # Check for MinGW
        if self._check_tool_available("mingw32-gcc"):
            logger.info("Found MinGW")
            return True
        
        return False
    
    def _check_openssl(self) -> bool:
        """Check for OpenSSL installation."""
        # Common OpenSSL locations
        openssl_paths = []
        
        if self.platform == "windows":
            openssl_paths = [
                "C:/OpenSSL-Win64",
                "C:/OpenSSL-Win32",
                "C:/Program Files/OpenSSL-Win64",
                "C:/vcpkg/installed/x64-windows"
            ]
        elif self.platform == "darwin":
            openssl_paths = [
                "/usr/local/opt/openssl",
                "/opt/homebrew/opt/openssl",
                "/usr/local/ssl"
            ]
        else:  # Linux
            openssl_paths = [
                "/usr/include/openssl",
                "/usr/local/include/openssl"
            ]
        
        for path in openssl_paths:
            if Path(path).exists():
                logger.info(f"Found OpenSSL at: {path}")
                return True
        
        # Try pkg-config
        try:
            subprocess.run(["pkg-config", "--exists", "openssl"], 
                         check=True, capture_output=True)
            logger.info("Found OpenSSL via pkg-config")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass
        
        return False
    
    def _check_zlib(self) -> bool:
        """Check for zlib installation."""
        try:
            subprocess.run(["pkg-config", "--exists", "zlib"], 
                         check=True, capture_output=True)
            logger.info("Found zlib via pkg-config")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass
        
        # Check common locations
        zlib_paths = []
        if self.platform == "windows":
            zlib_paths = [
                "C:/vcpkg/installed/x64-windows/include/zlib.h"
            ]
        else:
            zlib_paths = [
                "/usr/include/zlib.h",
                "/usr/local/include/zlib.h"
            ]
        
        for path in zlib_paths:
            if Path(path).exists():
                logger.info(f"Found zlib at: {path}")
                return True
        
        return False
    
    def prepare_build_environment(self) -> bool:
        """Prepare build environment."""
        logger.info("Preparing build environment...")
        
        try:
            # Create build directory
            self.build_dir.mkdir(parents=True, exist_ok=True)
            
            # Setup CMake arguments
            self._setup_cmake_args()
            
            logger.info("Build environment prepared")
            return True
            
        except Exception as e:
            logger.error(f"Failed to prepare build environment: {e}")
            return False
    
    def _setup_cmake_args(self):
        """Setup CMake arguments based on platform."""
        self.cmake_args = [
            f"-DCMAKE_BUILD_TYPE={self.build_type}",
            "-DBUILD_DATA_TESTS=ON",
            "-DBUILD_DATA_PYTHON_MODULE=ON"
        ]
        
        # Platform-specific arguments
        if self.platform == "windows":
            # Use Visual Studio generator if available
            self.cmake_args.extend([
                "-G", "Visual Studio 16 2019",
                "-A", "x64"
            ])
        
        # OpenSSL hints
        if self.platform == "darwin":
            # macOS with Homebrew
            homebrew_openssl = "/opt/homebrew/opt/openssl"
            if Path(homebrew_openssl).exists():
                self.cmake_args.extend([
                    f"-DOPENSSL_ROOT_DIR={homebrew_openssl}",
                    f"-DOPENSSL_LIBRARIES={homebrew_openssl}/lib"
                ])
    
    def build_cpp_data_core(self) -> bool:
        """Build C++ data core engine."""
        logger.info("Building C++ data core engine...")
        
        try:
            # Change to build directory
            original_cwd = os.getcwd()
            os.chdir(self.build_dir)
            
            # Run CMake configure
            cmake_cmd = ["cmake", ".."] + self.cmake_args
            logger.info(f"Running: {' '.join(cmake_cmd)}")
            
            result = subprocess.run(cmake_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"CMake configure failed: {result.stderr}")
                return False
            
            # Run build
            build_cmd = ["cmake", "--build", ".", "--config", self.build_type]
            if self.platform != "windows":
                build_cmd.extend(["--", "-j", str(os.cpu_count())])
            
            logger.info(f"Running: {' '.join(build_cmd)}")
            result = subprocess.run(build_cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Build failed: {result.stderr}")
                return False
            
            logger.info("C++ data core engine built successfully")
            return True
            
        except Exception as e:
            logger.error(f"Build failed with exception: {e}")
            return False
        finally:
            os.chdir(original_cwd)
    
    def run_tests(self) -> bool:
        """Run C++ data tests."""
        logger.info("Running C++ data tests...")
        
        try:
            # Find test executable
            test_exe = self._find_test_executable()
            if not test_exe:
                logger.warning("Test executable not found, skipping tests")
                return True
            
            # Run tests
            result = subprocess.run([str(test_exe)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("All C++ data tests passed")
                logger.info(result.stdout)
                return True
            else:
                logger.error(f"Tests failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Test execution failed: {e}")
            return False
    
    def _find_test_executable(self) -> Optional[Path]:
        """Find test executable."""
        possible_names = [
            "data_engine_test",
            "data_engine_test.exe",
            "test_data_engine",
            "test_data_engine.exe"
        ]
        
        search_dirs = [
            self.build_dir,
            self.build_dir / "Debug",
            self.build_dir / "Release",
            self.build_dir / "bin"
        ]
        
        for search_dir in search_dirs:
            for name in possible_names:
                test_path = search_dir / name
                if test_path.exists():
                    return test_path
        
        return None
    
    def install_python_integration(self) -> bool:
        """Install Python integration components."""
        logger.info("Installing Python integration...")
        
        try:
            # Copy built library to Python module location
            lib_file = self._find_built_library()
            if not lib_file:
                logger.error("Built library not found")
                return False
            
            # Copy to Python module directory
            target_dir = self.project_root
            target_file = target_dir / lib_file.name
            
            shutil.copy2(lib_file, target_file)
            logger.info(f"Copied {lib_file.name} to {target_dir}")
            
            # Install Python dependencies
            self._install_python_dependencies()
            
            logger.info("Python integration installed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Python integration installation failed: {e}")
            return False
    
    def _find_built_library(self) -> Optional[Path]:
        """Find built shared library."""
        if self.platform == "windows":
            extensions = [".dll"]
        elif self.platform == "darwin":
            extensions = [".dylib", ".so"]
        else:
            extensions = [".so"]
        
        search_dirs = [
            self.build_dir,
            self.build_dir / "Debug",
            self.build_dir / "Release",
            self.build_dir / "lib"
        ]
        
        for search_dir in search_dirs:
            for ext in extensions:
                for lib_file in search_dir.glob(f"*sbards_data_engine*{ext}"):
                    return lib_file
        
        return None
    
    def _install_python_dependencies(self):
        """Install required Python dependencies."""
        dependencies = [
            "cryptography",
            "requests",
            "psutil"
        ]
        
        for dep in dependencies:
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                             check=True, capture_output=True)
                logger.info(f"Installed Python dependency: {dep}")
            except subprocess.CalledProcessError:
                logger.warning(f"Failed to install Python dependency: {dep}")
    
    def build_all(self) -> bool:
        """Build complete data system."""
        logger.info("Starting complete data build process...")
        
        steps = [
            ("Checking dependencies", self.check_dependencies),
            ("Preparing build environment", self.prepare_build_environment),
            ("Building C++ data core", self.build_cpp_data_core),
            ("Running tests", self.run_tests),
            ("Installing Python integration", self.install_python_integration)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"Step: {step_name}")
            if not step_func():
                logger.error(f"Build failed at step: {step_name}")
                return False
        
        logger.info("🎉 Data build completed successfully!")
        logger.info("C++ Data Engine is ready for use.")
        return True

def main():
    """Main build function."""
    builder = CPPDataCoreBuildSystem()
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--debug":
            builder.build_type = "Debug"
        elif sys.argv[1] == "--test-only":
            return builder.run_tests()
        elif sys.argv[1] == "--clean":
            if builder.build_dir.exists():
                shutil.rmtree(builder.build_dir)
                logger.info("Build directory cleaned")
            return True
    
    # Run complete build
    success = builder.build_all()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
