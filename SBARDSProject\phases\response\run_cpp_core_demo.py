#!/usr/bin/env python3
"""
SBARDS Response Engine - C++ Core Demo
Comprehensive demonstration of C++ core engine with Python integration

This demo showcases:
1. C++ Core Engine initialization
2. High-performance response processing
3. Security and encryption features
4. Cross-platform compatibility
5. Performance monitoring
6. Error handling and fallback
"""

import os
import sys
import json
import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, Any, List

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import response system
from phases.response.enhanced_response_system import EnhancedResponseSystem, CPP_CORE_AVAILABLE

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CPPCoreDemoRunner:
    """Demo runner for C++ Core Response Engine."""
    
    def __init__(self):
        """Initialize demo runner."""
        self.demo_config = self._load_demo_config()
        self.response_system = None
        self.demo_files = []
        
    def _load_demo_config(self) -> Dict[str, Any]:
        """Load demo configuration."""
        config_path = Path(__file__).parent / "config.json"
        
        if config_path.exists():
            with open(config_path, 'r') as f:
                return json.load(f)
        
        # Default configuration
        return {
            "response": {
                "engine_type": "cpp_core",
                "fallback_to_python": True,
                "base_directory": "demo_response_data",
                "quarantine_directory": "demo_response_data/quarantine",
                "backup_directory": "demo_response_data/backup",
                "log_directory": "demo_response_data/logs",
                "encryption_enabled": True,
                "max_concurrent_responses": 5,
                "security_level": "enhanced"
            }
        }
    
    def print_banner(self):
        """Print demo banner."""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🚀 SBARDS Response Engine - C++ Core Demo                 ║
║                                                                              ║
║  🔧 High-Performance C++ Core Engine                                        ║
║  🐍 Python Integration Layer                                                ║
║  🛡️ Advanced Security & Encryption                                          ║
║  🌍 Cross-Platform Compatibility                                            ║
║  📊 Real-time Performance Monitoring                                        ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def check_system_status(self):
        """Check system status and capabilities."""
        print("\n🔍 System Status Check:")
        print("=" * 50)
        
        # Check C++ core availability
        if CPP_CORE_AVAILABLE:
            print("✅ C++ Core Engine: Available")
        else:
            print("⚠️  C++ Core Engine: Not available (using Python fallback)")
        
        # Check platform
        import platform
        print(f"🖥️  Platform: {platform.system()} {platform.release()}")
        print(f"🏗️  Architecture: {platform.machine()}")
        print(f"🐍 Python Version: {platform.python_version()}")
        
        # Check dependencies
        dependencies = {
            "ctypes": "C++ Integration",
            "json": "Configuration",
            "asyncio": "Async Processing",
            "threading": "Multithreading"
        }
        
        print("\n📦 Dependencies:")
        for dep, desc in dependencies.items():
            try:
                __import__(dep)
                print(f"✅ {dep}: {desc}")
            except ImportError:
                print(f"❌ {dep}: {desc} - Missing")
    
    def create_demo_files(self):
        """Create demo files for testing."""
        print("\n📁 Creating Demo Files:")
        print("=" * 30)
        
        demo_dir = Path(self.demo_config["response"]["base_directory"])
        demo_dir.mkdir(parents=True, exist_ok=True)
        
        demo_files_data = [
            {
                "name": "safe_document.txt",
                "content": "This is a safe document with normal content.\nNo suspicious behavior detected.",
                "threat_level": "safe",
                "threat_score": 0.1
            },
            {
                "name": "suspicious_script.py",
                "content": "import os\nos.system('whoami')\n# Suspicious system call",
                "threat_level": "suspicious",
                "threat_score": 0.6
            },
            {
                "name": "malicious_executable.exe",
                "content": "MZ\x90\x00\x03\x00\x00\x00\x04\x00\x00\x00\xff\xff\x00\x00\nFake PE header with malicious indicators\nCreateRemoteThread\nWriteProcessMemory",
                "threat_level": "malicious",
                "threat_score": 0.9
            },
            {
                "name": "advanced_threat.dll",
                "content": "Advanced persistent threat simulation\nAnti-debug techniques\nCode injection\nKeylogger functionality",
                "threat_level": "critical",
                "threat_score": 0.95
            }
        ]
        
        for file_data in demo_files_data:
            file_path = demo_dir / file_data["name"]
            
            with open(file_path, 'w', encoding='utf-8', errors='ignore') as f:
                f.write(file_data["content"])
            
            self.demo_files.append({
                "path": str(file_path),
                "name": file_data["name"],
                "threat_level": file_data["threat_level"],
                "threat_score": file_data["threat_score"]
            })
            
            print(f"📄 Created: {file_data['name']} ({file_data['threat_level']})")
    
    async def initialize_response_system(self):
        """Initialize response system."""
        print("\n🚀 Initializing Response System:")
        print("=" * 40)
        
        try:
            self.response_system = EnhancedResponseSystem(self.demo_config)
            
            if CPP_CORE_AVAILABLE:
                print("✅ C++ Core Engine initialized successfully")
                print(f"🔧 Engine Handle: {self.response_system.cpp_interface.engine_handle is not None}")
                print(f"📚 Library Path: {self.response_system.cpp_interface.library_info.path if self.response_system.cpp_interface.library_info else 'Not loaded'}")
            else:
                print("⚠️  Using Python fallback mode")
            
            print("✅ Response System ready for processing")
            
        except Exception as e:
            print(f"❌ Failed to initialize response system: {e}")
            raise
    
    async def demonstrate_response_strategies(self):
        """Demonstrate different response strategies."""
        print("\n🛡️ Response Strategy Demonstration:")
        print("=" * 45)
        
        for i, demo_file in enumerate(self.demo_files, 1):
            print(f"\n📋 Test {i}/4: {demo_file['name']}")
            print("-" * 30)
            
            # Create analysis results
            analysis_results = {
                "file_path": demo_file["path"],
                "file_info": {
                    "name": demo_file["name"],
                    "size": os.path.getsize(demo_file["path"]),
                    "extension": Path(demo_file["path"]).suffix
                },
                "file_hash": {
                    "sha256": f"demo_hash_{i}_sha256",
                    "md5": f"demo_hash_{i}_md5",
                    "sha1": f"demo_hash_{i}_sha1"
                },
                "threat_assessment": {
                    "overall_threat_level": demo_file["threat_level"],
                    "threat_score": demo_file["threat_score"],
                    "confidence": 0.9
                },
                "detected_threats": [
                    f"threat_indicator_{i}",
                    f"suspicious_pattern_{i}"
                ]
            }
            
            # Process with response system
            start_time = time.time()
            
            try:
                result = await self.response_system.process_enhanced_analysis_results(analysis_results)
                
                processing_time = (time.time() - start_time) * 1000
                
                # Display results
                if result.get("success", True):
                    print(f"✅ Processing successful ({processing_time:.2f}ms)")
                    
                    if "response_result" in result:
                        response_result = result["response_result"]
                        print(f"🎯 Strategy: {response_result.get('strategy_used', 'Unknown')}")
                        print(f"🔧 Actions: {', '.join(response_result.get('actions_taken', []))}")
                        
                        if response_result.get("quarantine_path"):
                            print(f"🔒 Quarantined to: {response_result['quarantine_path']}")
                        
                        if response_result.get("backup_path"):
                            print(f"💾 Backup created: {response_result['backup_path']}")
                    
                    print(f"🔧 C++ Integration: {result.get('cpp_integration', False)}")
                    
                else:
                    print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
                
            except Exception as e:
                print(f"❌ Exception during processing: {e}")
            
            # Small delay between tests
            await asyncio.sleep(0.5)
    
    async def demonstrate_performance_monitoring(self):
        """Demonstrate performance monitoring."""
        print("\n📊 Performance Monitoring:")
        print("=" * 35)
        
        try:
            if CPP_CORE_AVAILABLE and self.response_system.cpp_interface.engine_handle:
                # Get C++ performance metrics
                with self.response_system.cpp_interface.lock:
                    metrics_ptr = self.response_system.cpp_interface.cpp_lib.GetPerformanceMetrics(
                        self.response_system.cpp_interface.engine_handle
                    )
                    
                    if metrics_ptr:
                        import ctypes
                        metrics_json = ctypes.string_at(metrics_ptr).decode('utf-8')
                        self.response_system.cpp_interface.cpp_lib.FreeMemory(metrics_ptr)
                        
                        metrics = json.loads(metrics_json)
                        
                        print("🔧 C++ Core Engine Metrics:")
                        print(f"  📁 Files Processed: {metrics.get('total_files_processed', 0)}")
                        print(f"  ✅ Successful Responses: {metrics.get('successful_responses', 0)}")
                        print(f"  ❌ Failed Responses: {metrics.get('failed_responses', 0)}")
                        print(f"  ⏱️  Average Response Time: {metrics.get('average_response_time_ms', 0):.2f}ms")
                        print(f"  🚨 Threats Detected: {metrics.get('threats_detected', 0)}")
                        print(f"  ⚠️  False Positives: {metrics.get('false_positives', 0)}")
                    else:
                        print("⚠️  Could not retrieve C++ metrics")
            
            # Python metrics
            python_metrics = self.response_system.response_metrics
            print("\n🐍 Python Integration Metrics:")
            print(f"  📊 Total Responses: {python_metrics.get('total_responses', 0)}")
            print(f"  🔧 C++ Enhanced: {python_metrics.get('cpp_enhanced_responses', 0)}")
            print(f"  📈 Accuracy Improvements: {python_metrics.get('accuracy_improvements', 0)}")
            print(f"  📉 False Positive Reductions: {python_metrics.get('false_positive_reductions', 0)}")
            
        except Exception as e:
            print(f"❌ Error retrieving performance metrics: {e}")
    
    def demonstrate_security_features(self):
        """Demonstrate security features."""
        print("\n🔒 Security Features Demonstration:")
        print("=" * 40)
        
        security_features = [
            "🔐 AES-256-GCM Encryption",
            "🔑 RSA-4096 Digital Signatures", 
            "🗑️ Secure File Deletion (DoD 5220.22-M)",
            "🛡️ Memory Protection",
            "🔒 Access Control",
            "📝 Audit Logging",
            "🔍 Integrity Checking"
        ]
        
        print("Enabled Security Features:")
        for feature in security_features:
            print(f"  ✅ {feature}")
        
        # Show encryption status
        encryption_enabled = self.demo_config["response"].get("encryption_enabled", False)
        print(f"\n🔐 Encryption Status: {'Enabled' if encryption_enabled else 'Disabled'}")
        
        if encryption_enabled:
            print("  🔧 Algorithm: AES-256-GCM")
            print("  🔑 Key Size: 256-bit")
            print("  🛡️ Authentication: GMAC")
    
    def cleanup_demo_files(self):
        """Cleanup demo files."""
        print("\n🧹 Cleaning up demo files:")
        print("=" * 30)
        
        try:
            demo_dir = Path(self.demo_config["response"]["base_directory"])
            if demo_dir.exists():
                import shutil
                shutil.rmtree(demo_dir)
                print("✅ Demo files cleaned up successfully")
            else:
                print("ℹ️  No demo files to clean up")
                
        except Exception as e:
            print(f"⚠️  Error during cleanup: {e}")
    
    async def run_complete_demo(self):
        """Run complete demo."""
        try:
            self.print_banner()
            self.check_system_status()
            self.create_demo_files()
            await self.initialize_response_system()
            await self.demonstrate_response_strategies()
            await self.demonstrate_performance_monitoring()
            self.demonstrate_security_features()
            
            print("\n🎉 Demo completed successfully!")
            print("=" * 40)
            print("✅ C++ Core Engine demonstrated")
            print("✅ Response strategies tested")
            print("✅ Performance monitoring shown")
            print("✅ Security features highlighted")
            
        except Exception as e:
            print(f"\n❌ Demo failed: {e}")
            logger.exception("Demo execution failed")
        finally:
            self.cleanup_demo_files()

async def main():
    """Main demo function."""
    demo_runner = CPPCoreDemoRunner()
    await demo_runner.run_complete_demo()

if __name__ == "__main__":
    asyncio.run(main())
