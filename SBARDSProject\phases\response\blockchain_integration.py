#!/usr/bin/env python3
"""
SBARDS Blockchain Integration for Response Layer
High-security blockchain implementation for immutable threat response logging

This module provides a secure blockchain implementation for:
- Immutable threat response logging
- Forensic evidence chain of custody
- Audit trail preservation
- Distributed threat intelligence sharing
- Cryptographic proof of response actions
"""

import os
import sys
import json
import hashlib
import time
import logging
import threading
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
from pathlib import Path
import sqlite3
from dataclasses import dataclass, asdict
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import secrets
import base64

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

@dataclass
class BlockchainTransaction:
    """Blockchain transaction for response actions."""
    transaction_id: str
    timestamp: str
    transaction_type: str
    file_hash: str
    threat_level: str
    response_actions: List[str]
    user_id: str
    system_id: str
    evidence_hash: str
    metadata: Dict[str, Any]
    digital_signature: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """Convert transaction to dictionary."""
        return asdict(self)

    def get_hash_data(self) -> str:
        """Get data for hashing (excluding signature)."""
        data = self.to_dict()
        data.pop('digital_signature', None)
        return json.dumps(data, sort_keys=True)

@dataclass
class BlockchainBlock:
    """Blockchain block containing multiple transactions."""
    block_number: int
    timestamp: str
    previous_hash: str
    merkle_root: str
    transactions: List[BlockchainTransaction]
    nonce: int
    difficulty: int
    block_hash: str = ""
    miner_signature: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """Convert block to dictionary."""
        return {
            'block_number': self.block_number,
            'timestamp': self.timestamp,
            'previous_hash': self.previous_hash,
            'merkle_root': self.merkle_root,
            'transactions': [tx.to_dict() for tx in self.transactions],
            'nonce': self.nonce,
            'difficulty': self.difficulty,
            'block_hash': self.block_hash,
            'miner_signature': self.miner_signature
        }

    def get_hash_data(self) -> str:
        """Get data for hashing (excluding hash and signature)."""
        data = self.to_dict()
        data.pop('block_hash', None)
        data.pop('miner_signature', None)
        return json.dumps(data, sort_keys=True)

class CryptographicManager:
    """Advanced cryptographic operations for blockchain security."""

    def __init__(self, key_directory: str):
        """Initialize cryptographic manager."""
        self.key_directory = Path(key_directory)
        self.key_directory.mkdir(parents=True, exist_ok=True)

        # Initialize keys
        self.private_key = None
        self.public_key = None
        self._load_or_generate_keys()

        # Encryption settings
        self.encryption_algorithm = algorithms.AES
        self.encryption_mode = modes.GCM

    def _load_or_generate_keys(self):
        """Load existing keys or generate new ones."""
        private_key_path = self.key_directory / "blockchain_private.pem"
        public_key_path = self.key_directory / "blockchain_public.pem"

        if private_key_path.exists() and public_key_path.exists():
            self._load_keys(private_key_path, public_key_path)
        else:
            self._generate_keys(private_key_path, public_key_path)

    def _generate_keys(self, private_path: Path, public_path: Path):
        """Generate new RSA key pair."""
        # Generate private key
        self.private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=4096,
            backend=default_backend()
        )

        # Get public key
        self.public_key = self.private_key.public_key()

        # Save private key
        private_pem = self.private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.BestAvailableEncryption(
                self._get_key_password().encode()
            )
        )

        with open(private_path, 'wb') as f:
            f.write(private_pem)

        # Save public key
        public_pem = self.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )

        with open(public_path, 'wb') as f:
            f.write(public_pem)

        # Set secure permissions
        os.chmod(private_path, 0o600)
        os.chmod(public_path, 0o644)

    def _load_keys(self, private_path: Path, public_path: Path):
        """Load existing keys."""
        # Load private key
        with open(private_path, 'rb') as f:
            self.private_key = serialization.load_pem_private_key(
                f.read(),
                password=self._get_key_password().encode(),
                backend=default_backend()
            )

        # Load public key
        with open(public_path, 'rb') as f:
            self.public_key = serialization.load_pem_public_key(
                f.read(),
                backend=default_backend()
            )

    def _get_key_password(self) -> str:
        """Get key password from environment or generate."""
        password = os.environ.get('SBARDS_BLOCKCHAIN_KEY_PASSWORD')
        if not password:
            # Generate secure password
            password = base64.b64encode(secrets.token_bytes(32)).decode()
            print(f"Generated blockchain key password: {password}")
            print("Please set SBARDS_BLOCKCHAIN_KEY_PASSWORD environment variable")
        return password

    def sign_data(self, data: str) -> str:
        """Create digital signature for data."""
        signature = self.private_key.sign(
            data.encode(),
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        return base64.b64encode(signature).decode()

    def verify_signature(self, data: str, signature: str, public_key=None) -> bool:
        """Verify digital signature."""
        try:
            key = public_key or self.public_key
            signature_bytes = base64.b64decode(signature.encode())

            key.verify(
                signature_bytes,
                data.encode(),
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
        except Exception:
            return False

    def encrypt_data(self, data: str) -> Tuple[str, str, str]:
        """Encrypt data with AES-GCM."""
        # Generate random key and IV
        key = secrets.token_bytes(32)  # 256-bit key
        iv = secrets.token_bytes(12)   # 96-bit IV for GCM

        # Encrypt data
        cipher = Cipher(
            self.encryption_algorithm(key),
            self.encryption_mode(iv),
            backend=default_backend()
        )
        encryptor = cipher.encryptor()

        ciphertext = encryptor.update(data.encode()) + encryptor.finalize()

        # Get authentication tag
        tag = encryptor.tag

        # Encrypt the key with RSA
        encrypted_key = self.public_key.encrypt(
            key,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )

        return (
            base64.b64encode(ciphertext + tag).decode(),
            base64.b64encode(encrypted_key).decode(),
            base64.b64encode(iv).decode()
        )

    def decrypt_data(self, encrypted_data: str, encrypted_key: str, iv: str) -> str:
        """Decrypt AES-GCM encrypted data."""
        # Decode components
        ciphertext_with_tag = base64.b64decode(encrypted_data.encode())
        encrypted_key_bytes = base64.b64decode(encrypted_key.encode())
        iv_bytes = base64.b64decode(iv.encode())

        # Split ciphertext and tag
        ciphertext = ciphertext_with_tag[:-16]
        tag = ciphertext_with_tag[-16:]

        # Decrypt the key
        key = self.private_key.decrypt(
            encrypted_key_bytes,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )

        # Decrypt data
        cipher = Cipher(
            self.encryption_algorithm(key),
            self.encryption_mode(iv_bytes, tag),
            backend=default_backend()
        )
        decryptor = cipher.decryptor()

        plaintext = decryptor.update(ciphertext) + decryptor.finalize()
        return plaintext.decode()

class BlockchainDatabase:
    """Secure database for blockchain storage."""

    def __init__(self, db_path: str):
        """Initialize blockchain database."""
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Initialize database
        self._init_database()

        # Database lock for thread safety
        self.db_lock = threading.RLock()

    def _init_database(self):
        """Initialize database tables."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS blocks (
                    block_number INTEGER PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    previous_hash TEXT NOT NULL,
                    merkle_root TEXT NOT NULL,
                    nonce INTEGER NOT NULL,
                    difficulty INTEGER NOT NULL,
                    block_hash TEXT NOT NULL UNIQUE,
                    miner_signature TEXT NOT NULL,
                    block_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.execute('''
                CREATE TABLE IF NOT EXISTS transactions (
                    transaction_id TEXT PRIMARY KEY,
                    block_number INTEGER,
                    timestamp TEXT NOT NULL,
                    transaction_type TEXT NOT NULL,
                    file_hash TEXT NOT NULL,
                    threat_level TEXT NOT NULL,
                    response_actions TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    system_id TEXT NOT NULL,
                    evidence_hash TEXT NOT NULL,
                    metadata TEXT NOT NULL,
                    digital_signature TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (block_number) REFERENCES blocks (block_number)
                )
            ''')

            conn.execute('''
                CREATE TABLE IF NOT EXISTS blockchain_config (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create indexes for performance
            conn.execute('CREATE INDEX IF NOT EXISTS idx_blocks_hash ON blocks(block_hash)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_transactions_file_hash ON transactions(file_hash)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_transactions_timestamp ON transactions(timestamp)')

            conn.commit()

    def store_block(self, block: BlockchainBlock) -> bool:
        """Store block in database."""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    # Store block
                    conn.execute('''
                        INSERT INTO blocks
                        (block_number, timestamp, previous_hash, merkle_root, nonce,
                         difficulty, block_hash, miner_signature, block_data)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        block.block_number,
                        block.timestamp,
                        block.previous_hash,
                        block.merkle_root,
                        block.nonce,
                        block.difficulty,
                        block.block_hash,
                        block.miner_signature,
                        json.dumps(block.to_dict())
                    ))

                    # Store transactions
                    for tx in block.transactions:
                        conn.execute('''
                            INSERT INTO transactions
                            (transaction_id, block_number, timestamp, transaction_type,
                             file_hash, threat_level, response_actions, user_id, system_id,
                             evidence_hash, metadata, digital_signature)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            tx.transaction_id,
                            block.block_number,
                            tx.timestamp,
                            tx.transaction_type,
                            tx.file_hash,
                            tx.threat_level,
                            json.dumps(tx.response_actions),
                            tx.user_id,
                            tx.system_id,
                            tx.evidence_hash,
                            json.dumps(tx.metadata),
                            tx.digital_signature
                        ))

                    conn.commit()
                    return True

        except Exception as e:
            logging.error(f"Error storing block: {e}")
            return False

    def get_latest_block(self) -> Optional[BlockchainBlock]:
        """Get the latest block from database."""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute('''
                        SELECT block_data FROM blocks
                        ORDER BY block_number DESC LIMIT 1
                    ''')

                    row = cursor.fetchone()
                    if row:
                        block_data = json.loads(row[0])
                        return self._dict_to_block(block_data)

                    return None

        except Exception as e:
            logging.error(f"Error getting latest block: {e}")
            return None

    def get_block_by_hash(self, block_hash: str) -> Optional[BlockchainBlock]:
        """Get block by hash."""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute('''
                        SELECT block_data FROM blocks WHERE block_hash = ?
                    ''', (block_hash,))

                    row = cursor.fetchone()
                    if row:
                        block_data = json.loads(row[0])
                        return self._dict_to_block(block_data)

                    return None

        except Exception as e:
            logging.error(f"Error getting block by hash: {e}")
            return None

    def _dict_to_block(self, block_data: Dict[str, Any]) -> BlockchainBlock:
        """Convert dictionary to BlockchainBlock."""
        transactions = []
        for tx_data in block_data['transactions']:
            tx = BlockchainTransaction(**tx_data)
            transactions.append(tx)

        block_data['transactions'] = transactions
        return BlockchainBlock(**block_data)

class BlockchainMiner:
    """Proof-of-Work mining for blockchain security."""

    def __init__(self, difficulty: int = 4):
        """Initialize miner with difficulty level."""
        self.difficulty = difficulty
        self.target = "0" * difficulty

    def calculate_hash(self, data: str) -> str:
        """Calculate SHA-256 hash."""
        return hashlib.sha256(data.encode()).hexdigest()

    def calculate_merkle_root(self, transactions: List[BlockchainTransaction]) -> str:
        """Calculate Merkle root of transactions."""
        if not transactions:
            return self.calculate_hash("")

        # Get transaction hashes
        tx_hashes = [self.calculate_hash(tx.get_hash_data()) for tx in transactions]

        # Build Merkle tree
        while len(tx_hashes) > 1:
            if len(tx_hashes) % 2 == 1:
                tx_hashes.append(tx_hashes[-1])  # Duplicate last hash if odd number

            new_hashes = []
            for i in range(0, len(tx_hashes), 2):
                combined = tx_hashes[i] + tx_hashes[i + 1]
                new_hashes.append(self.calculate_hash(combined))

            tx_hashes = new_hashes

        return tx_hashes[0]

    def mine_block(self, block: BlockchainBlock) -> BlockchainBlock:
        """Mine block using Proof-of-Work."""
        block.merkle_root = self.calculate_merkle_root(block.transactions)
        block.difficulty = self.difficulty

        nonce = 0
        start_time = time.time()

        while True:
            block.nonce = nonce
            block_hash = self.calculate_hash(block.get_hash_data())

            if block_hash.startswith(self.target):
                block.block_hash = block_hash
                mining_time = time.time() - start_time
                logging.info(f"Block mined! Hash: {block_hash}, Nonce: {nonce}, Time: {mining_time:.2f}s")
                break

            nonce += 1

            # Prevent infinite loops in testing
            if nonce > 1000000:
                logging.warning("Mining timeout reached, reducing difficulty")
                self.difficulty = max(1, self.difficulty - 1)
                self.target = "0" * self.difficulty
                nonce = 0

        return block

class BlockchainIntegration:
    """Main blockchain integration class for SBARDS Response Layer."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize blockchain integration."""
        self.config = config.get("blockchain", {})
        self.logger = logging.getLogger("SBARDS.BlockchainIntegration")

        # Initialize components
        self.enabled = self.config.get("enabled", False)
        if not self.enabled:
            self.logger.info("Blockchain integration disabled")
            return

        # Setup directories
        self.blockchain_dir = Path(config.get("comprehensive_response", {}).get("base_directory", "response_data")) / "blockchain"
        self.blockchain_dir.mkdir(parents=True, exist_ok=True)

        # Initialize cryptographic manager
        self.crypto_manager = CryptographicManager(str(self.blockchain_dir / "keys"))

        # Initialize database
        self.database = BlockchainDatabase(str(self.blockchain_dir / "blockchain.db"))

        # Initialize miner
        self.miner = BlockchainMiner(difficulty=self.config.get("mining_difficulty", 4))

        # Transaction pool
        self.transaction_pool = []
        self.pool_lock = threading.RLock()

        # Block creation settings
        self.max_transactions_per_block = self.config.get("max_transactions_per_block", 10)
        self.block_creation_interval = self.config.get("block_creation_interval_seconds", 300)  # 5 minutes

        # Start background processes
        self.running = True
        self.block_creation_thread = threading.Thread(target=self._block_creation_loop, daemon=True)
        self.block_creation_thread.start()

        # Initialize genesis block if needed
        self._ensure_genesis_block()

        self.logger.info("Blockchain integration initialized successfully")

    def _ensure_genesis_block(self):
        """Create genesis block if blockchain is empty."""
        latest_block = self.database.get_latest_block()
        if latest_block is None:
            self._create_genesis_block()

    def _create_genesis_block(self):
        """Create the genesis block."""
        genesis_transaction = BlockchainTransaction(
            transaction_id="genesis",
            timestamp=datetime.now(timezone.utc).isoformat(),
            transaction_type="genesis",
            file_hash="0" * 64,
            threat_level="none",
            response_actions=["blockchain_initialized"],
            user_id="system",
            system_id="sbards",
            evidence_hash="0" * 64,
            metadata={"description": "SBARDS Blockchain Genesis Block"}
        )

        # Sign genesis transaction
        genesis_transaction.digital_signature = self.crypto_manager.sign_data(
            genesis_transaction.get_hash_data()
        )

        genesis_block = BlockchainBlock(
            block_number=0,
            timestamp=datetime.now(timezone.utc).isoformat(),
            previous_hash="0" * 64,
            merkle_root="",
            transactions=[genesis_transaction],
            nonce=0,
            difficulty=0
        )

        # Mine genesis block
        genesis_block = self.miner.mine_block(genesis_block)

        # Sign block
        genesis_block.miner_signature = self.crypto_manager.sign_data(
            genesis_block.get_hash_data()
        )

        # Store genesis block
        if self.database.store_block(genesis_block):
            self.logger.info("Genesis block created successfully")
        else:
            self.logger.error("Failed to create genesis block")

    def add_response_transaction(self, file_path: str, file_hash: str, threat_level: str,
                               response_actions: List[str], user_id: str = "system",
                               evidence_data: Dict[str, Any] = None) -> str:
        """Add a response transaction to the blockchain."""
        if not self.enabled:
            return ""

        try:
            # Generate transaction ID
            transaction_id = hashlib.sha256(
                f"{file_hash}{time.time()}{secrets.token_hex(16)}".encode()
            ).hexdigest()

            # Create evidence hash
            evidence_hash = self._create_evidence_hash(evidence_data or {})

            # Create transaction
            transaction = BlockchainTransaction(
                transaction_id=transaction_id,
                timestamp=datetime.now(timezone.utc).isoformat(),
                transaction_type="response_action",
                file_hash=file_hash,
                threat_level=threat_level,
                response_actions=response_actions,
                user_id=user_id,
                system_id="sbards",
                evidence_hash=evidence_hash,
                metadata={
                    "file_path": file_path,
                    "evidence_data": evidence_data or {},
                    "blockchain_version": "1.0"
                }
            )

            # Sign transaction
            transaction.digital_signature = self.crypto_manager.sign_data(
                transaction.get_hash_data()
            )

            # Add to transaction pool
            with self.pool_lock:
                self.transaction_pool.append(transaction)

            self.logger.info(f"Transaction added to pool: {transaction_id}")
            return transaction_id

        except Exception as e:
            self.logger.error(f"Error adding transaction: {e}")
            return ""

    def _create_evidence_hash(self, evidence_data: Dict[str, Any]) -> str:
        """Create hash of evidence data."""
        evidence_json = json.dumps(evidence_data, sort_keys=True)
        return hashlib.sha256(evidence_json.encode()).hexdigest()

    def _block_creation_loop(self):
        """Background loop for creating blocks."""
        while self.running:
            try:
                time.sleep(self.block_creation_interval)

                with self.pool_lock:
                    if len(self.transaction_pool) >= self.max_transactions_per_block:
                        self._create_new_block()

            except Exception as e:
                self.logger.error(f"Error in block creation loop: {e}")

    def _create_new_block(self):
        """Create a new block from transaction pool."""
        try:
            # Get transactions from pool
            with self.pool_lock:
                transactions = self.transaction_pool[:self.max_transactions_per_block]
                self.transaction_pool = self.transaction_pool[self.max_transactions_per_block:]

            if not transactions:
                return

            # Get previous block
            previous_block = self.database.get_latest_block()
            previous_hash = previous_block.block_hash if previous_block else "0" * 64
            block_number = (previous_block.block_number + 1) if previous_block else 1

            # Create new block
            new_block = BlockchainBlock(
                block_number=block_number,
                timestamp=datetime.now(timezone.utc).isoformat(),
                previous_hash=previous_hash,
                merkle_root="",
                transactions=transactions,
                nonce=0,
                difficulty=0
            )

            # Mine block
            new_block = self.miner.mine_block(new_block)

            # Sign block
            new_block.miner_signature = self.crypto_manager.sign_data(
                new_block.get_hash_data()
            )

            # Store block
            if self.database.store_block(new_block):
                self.logger.info(f"New block created: {new_block.block_number}")
            else:
                self.logger.error("Failed to store new block")

                # Return transactions to pool
                with self.pool_lock:
                    self.transaction_pool.extend(transactions)

        except Exception as e:
            self.logger.error(f"Error creating new block: {e}")

    def verify_blockchain_integrity(self) -> bool:
        """Verify the integrity of the entire blockchain."""
        if not self.enabled:
            return True

        try:
            # Get all blocks
            with sqlite3.connect(self.database.db_path) as conn:
                cursor = conn.execute('''
                    SELECT block_data FROM blocks ORDER BY block_number ASC
                ''')

                blocks_data = [json.loads(row[0]) for row in cursor.fetchall()]

            if not blocks_data:
                return True

            # Verify each block
            for i, block_data in enumerate(blocks_data):
                block = self.database._dict_to_block(block_data)

                # Verify block hash
                calculated_hash = self.miner.calculate_hash(block.get_hash_data())
                if calculated_hash != block.block_hash:
                    self.logger.error(f"Block {block.block_number} hash verification failed")
                    return False

                # Verify previous hash (except genesis)
                if i > 0:
                    previous_block = self.database._dict_to_block(blocks_data[i-1])
                    if block.previous_hash != previous_block.block_hash:
                        self.logger.error(f"Block {block.block_number} previous hash verification failed")
                        return False

                # Verify transactions
                for tx in block.transactions:
                    if not self.crypto_manager.verify_signature(tx.get_hash_data(), tx.digital_signature):
                        self.logger.error(f"Transaction {tx.transaction_id} signature verification failed")
                        return False

                # Verify merkle root
                calculated_merkle = self.miner.calculate_merkle_root(block.transactions)
                if calculated_merkle != block.merkle_root:
                    self.logger.error(f"Block {block.block_number} merkle root verification failed")
                    return False

            self.logger.info("Blockchain integrity verification passed")
            return True

        except Exception as e:
            self.logger.error(f"Error verifying blockchain integrity: {e}")
            return False

    def get_transaction_history(self, file_hash: str) -> List[Dict[str, Any]]:
        """Get transaction history for a specific file."""
        if not self.enabled:
            return []

        try:
            with sqlite3.connect(self.database.db_path) as conn:
                cursor = conn.execute('''
                    SELECT * FROM transactions WHERE file_hash = ? ORDER BY timestamp ASC
                ''', (file_hash,))

                columns = [description[0] for description in cursor.description]
                transactions = []

                for row in cursor.fetchall():
                    tx_dict = dict(zip(columns, row))
                    tx_dict['response_actions'] = json.loads(tx_dict['response_actions'])
                    tx_dict['metadata'] = json.loads(tx_dict['metadata'])
                    transactions.append(tx_dict)

                return transactions

        except Exception as e:
            self.logger.error(f"Error getting transaction history: {e}")
            return []

    def export_blockchain_data(self, output_path: str, encrypt: bool = True) -> bool:
        """Export blockchain data for backup or sharing."""
        if not self.enabled:
            return False

        try:
            # Get all blockchain data
            with sqlite3.connect(self.database.db_path) as conn:
                cursor = conn.execute('SELECT * FROM blocks ORDER BY block_number ASC')
                blocks = cursor.fetchall()

                cursor = conn.execute('SELECT * FROM transactions ORDER BY timestamp ASC')
                transactions = cursor.fetchall()

            export_data = {
                "export_timestamp": datetime.now(timezone.utc).isoformat(),
                "blockchain_version": "1.0",
                "blocks": blocks,
                "transactions": transactions,
                "integrity_verified": self.verify_blockchain_integrity()
            }

            export_json = json.dumps(export_data, indent=2)

            if encrypt:
                # Encrypt export data
                encrypted_data, encrypted_key, iv = self.crypto_manager.encrypt_data(export_json)

                export_package = {
                    "encrypted": True,
                    "data": encrypted_data,
                    "key": encrypted_key,
                    "iv": iv,
                    "export_timestamp": export_data["export_timestamp"]
                }

                with open(output_path, 'w') as f:
                    json.dump(export_package, f, indent=2)
            else:
                with open(output_path, 'w') as f:
                    f.write(export_json)

            self.logger.info(f"Blockchain data exported to: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error exporting blockchain data: {e}")
            return False

    def get_blockchain_statistics(self) -> Dict[str, Any]:
        """Get blockchain statistics."""
        if not self.enabled:
            return {"enabled": False}

        try:
            with sqlite3.connect(self.database.db_path) as conn:
                # Get block count
                cursor = conn.execute('SELECT COUNT(*) FROM blocks')
                block_count = cursor.fetchone()[0]

                # Get transaction count
                cursor = conn.execute('SELECT COUNT(*) FROM transactions')
                transaction_count = cursor.fetchone()[0]

                # Get latest block info
                cursor = conn.execute('''
                    SELECT block_number, timestamp, block_hash
                    FROM blocks ORDER BY block_number DESC LIMIT 1
                ''')
                latest_block = cursor.fetchone()

                # Get pending transactions
                pending_count = len(self.transaction_pool)

            return {
                "enabled": True,
                "block_count": block_count,
                "transaction_count": transaction_count,
                "pending_transactions": pending_count,
                "latest_block": {
                    "number": latest_block[0] if latest_block else 0,
                    "timestamp": latest_block[1] if latest_block else "",
                    "hash": latest_block[2] if latest_block else ""
                },
                "mining_difficulty": self.miner.difficulty,
                "integrity_verified": self.verify_blockchain_integrity()
            }

        except Exception as e:
            self.logger.error(f"Error getting blockchain statistics: {e}")
            return {"enabled": True, "error": str(e)}

    def shutdown(self):
        """Shutdown blockchain integration."""
        if self.enabled:
            self.running = False
            if hasattr(self, 'block_creation_thread'):
                self.block_creation_thread.join(timeout=5)
            self.logger.info("Blockchain integration shutdown complete")
