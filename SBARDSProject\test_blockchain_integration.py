#!/usr/bin/env python3
"""
SBARDS Blockchain Integration Test Suite
Comprehensive testing for blockchain functionality in response layer

This test suite validates:
- Blockchain initialization and configuration
- Transaction creation and validation
- Block mining and storage
- Cryptographic operations
- Database integrity
- Performance under load
"""

import os
import sys
import json
import asyncio
import logging
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import blockchain components
try:
    from phases.response.blockchain_integration import (
        BlockchainIntegration,
        BlockchainTransaction,
        BlockchainBlock,
        CryptographicManager,
        BlockchainDatabase,
        BlockchainMiner
    )
    BLOCKCHAIN_AVAILABLE = True
except ImportError as e:
    print(f"Blockchain integration not available: {e}")
    BLOCKCHAIN_AVAILABLE = False

class BlockchainIntegrationTest:
    """Comprehensive blockchain integration test suite."""

    def __init__(self):
        """Initialize test suite."""
        self.logger = logging.getLogger("SBARDS.BlockchainTest")
        self.test_dir = None
        self.blockchain_integration = None
        self.test_results = []

    def setup_test_environment(self):
        """Setup test environment."""
        # Create temporary directory for testing
        self.test_dir = Path(tempfile.mkdtemp(prefix="sbards_blockchain_test_"))

        # Create test configuration
        test_config = {
            "blockchain": {
                "enabled": True,
                "mining_difficulty": 2,  # Lower difficulty for testing
                "max_transactions_per_block": 5,
                "block_creation_interval_seconds": 10
            },
            "comprehensive_response": {
                "base_directory": str(self.test_dir / "response_data")
            }
        }

        return test_config

    def cleanup_test_environment(self):
        """Cleanup test environment."""
        if self.test_dir and self.test_dir.exists():
            shutil.rmtree(self.test_dir)

    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result."""
        status = "PASSED" if success else "FAILED"
        self.test_results.append({
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat()
        })

        if success:
            self.logger.info(f"✓ {test_name}: {status}")
        else:
            self.logger.error(f"✗ {test_name}: {status} - {details}")

    def test_cryptographic_manager(self) -> bool:
        """Test cryptographic operations."""
        try:
            self.logger.info("Testing Cryptographic Manager...")

            # Initialize crypto manager
            crypto_manager = CryptographicManager(str(self.test_dir / "crypto_keys"))

            # Test digital signatures
            test_data = "This is test data for signing"
            signature = crypto_manager.sign_data(test_data)

            if not signature:
                self.log_test_result("crypto_signature_creation", False, "Failed to create signature")
                return False

            # Test signature verification
            is_valid = crypto_manager.verify_signature(test_data, signature)
            if not is_valid:
                self.log_test_result("crypto_signature_verification", False, "Signature verification failed")
                return False

            # Test encryption/decryption
            test_plaintext = "This is sensitive data to encrypt"
            encrypted_data, encrypted_key, iv = crypto_manager.encrypt_data(test_plaintext)

            if not all([encrypted_data, encrypted_key, iv]):
                self.log_test_result("crypto_encryption", False, "Encryption failed")
                return False

            # Test decryption
            decrypted_data = crypto_manager.decrypt_data(encrypted_data, encrypted_key, iv)
            if decrypted_data != test_plaintext:
                self.log_test_result("crypto_decryption", False, "Decryption failed or data mismatch")
                return False

            self.log_test_result("cryptographic_manager", True, "All crypto operations successful")
            return True

        except Exception as e:
            self.log_test_result("cryptographic_manager", False, str(e))
            return False

    def test_blockchain_database(self) -> bool:
        """Test blockchain database operations."""
        try:
            self.logger.info("Testing Blockchain Database...")

            # Initialize database
            db_path = self.test_dir / "test_blockchain.db"
            database = BlockchainDatabase(str(db_path))

            # Create test transaction
            test_transaction = BlockchainTransaction(
                transaction_id="test_tx_001",
                timestamp=datetime.now().isoformat(),
                transaction_type="test",
                file_hash="a" * 64,
                threat_level="safe",
                response_actions=["test_action"],
                user_id="test_user",
                system_id="test_system",
                evidence_hash="b" * 64,
                metadata={"test": True},
                digital_signature="test_signature"
            )

            # Create test block
            test_block = BlockchainBlock(
                block_number=1,
                timestamp=datetime.now().isoformat(),
                previous_hash="0" * 64,
                merkle_root="c" * 64,
                transactions=[test_transaction],
                nonce=12345,
                difficulty=2,
                block_hash="d" * 64,
                miner_signature="test_miner_signature"
            )

            # Test block storage
            success = database.store_block(test_block)
            if not success:
                self.log_test_result("database_block_storage", False, "Failed to store block")
                return False

            # Test block retrieval
            retrieved_block = database.get_latest_block()
            if not retrieved_block or retrieved_block.block_number != 1:
                self.log_test_result("database_block_retrieval", False, "Failed to retrieve block")
                return False

            # Test block retrieval by hash
            retrieved_by_hash = database.get_block_by_hash("d" * 64)
            if not retrieved_by_hash or retrieved_by_hash.block_hash != "d" * 64:
                self.log_test_result("database_block_retrieval_by_hash", False, "Failed to retrieve block by hash")
                return False

            self.log_test_result("blockchain_database", True, "All database operations successful")
            return True

        except Exception as e:
            self.log_test_result("blockchain_database", False, str(e))
            return False

    def test_blockchain_miner(self) -> bool:
        """Test blockchain mining operations."""
        try:
            self.logger.info("Testing Blockchain Miner...")

            # Initialize miner with low difficulty for testing
            miner = BlockchainMiner(difficulty=2)

            # Create test transactions
            transactions = []
            for i in range(3):
                tx = BlockchainTransaction(
                    transaction_id=f"test_tx_{i:03d}",
                    timestamp=datetime.now().isoformat(),
                    transaction_type="test",
                    file_hash=f"{'a' * 60}{i:04d}",
                    threat_level="safe",
                    response_actions=[f"test_action_{i}"],
                    user_id="test_user",
                    system_id="test_system",
                    evidence_hash=f"{'b' * 60}{i:04d}",
                    metadata={"test": True, "index": i},
                    digital_signature=f"test_signature_{i}"
                )
                transactions.append(tx)

            # Test Merkle root calculation
            merkle_root = miner.calculate_merkle_root(transactions)
            if not merkle_root or len(merkle_root) != 64:
                self.log_test_result("miner_merkle_root", False, "Invalid Merkle root")
                return False

            # Create test block for mining
            test_block = BlockchainBlock(
                block_number=1,
                timestamp=datetime.now().isoformat(),
                previous_hash="0" * 64,
                merkle_root="",
                transactions=transactions,
                nonce=0,
                difficulty=0
            )

            # Test block mining
            mined_block = miner.mine_block(test_block)

            # Verify mining results
            if not mined_block.block_hash.startswith("00"):  # difficulty = 2
                self.log_test_result("miner_proof_of_work", False, "Mining did not meet difficulty requirement")
                return False

            if mined_block.merkle_root != merkle_root:
                self.log_test_result("miner_merkle_verification", False, "Merkle root mismatch")
                return False

            self.log_test_result("blockchain_miner", True, "All mining operations successful")
            return True

        except Exception as e:
            self.log_test_result("blockchain_miner", False, str(e))
            return False

    async def test_blockchain_integration_basic(self) -> bool:
        """Test basic blockchain integration functionality."""
        try:
            self.logger.info("Testing Basic Blockchain Integration...")

            # Setup test configuration
            test_config = self.setup_test_environment()

            # Initialize blockchain integration
            self.blockchain_integration = BlockchainIntegration(test_config)

            if not self.blockchain_integration.enabled:
                self.log_test_result("blockchain_initialization", False, "Blockchain not enabled")
                return False

            # Test transaction addition
            transaction_id = self.blockchain_integration.add_response_transaction(
                file_path="test_file.exe",
                file_hash="test_hash_" + "a" * 56,
                threat_level="safe",
                response_actions=["whitelist_addition"],
                user_id="test_user",
                evidence_data={"test": "evidence"}
            )

            if not transaction_id:
                self.log_test_result("blockchain_transaction_addition", False, "Failed to add transaction")
                return False

            # Wait a moment for processing
            await asyncio.sleep(1)

            # Test blockchain statistics
            stats = self.blockchain_integration.get_blockchain_statistics()
            if not stats.get("enabled", False):
                self.log_test_result("blockchain_statistics", False, "Statistics indicate blockchain not enabled")
                return False

            self.log_test_result("blockchain_integration_basic", True, f"Transaction added: {transaction_id}")
            return True

        except Exception as e:
            self.log_test_result("blockchain_integration_basic", False, str(e))
            return False

    async def test_blockchain_integration_advanced(self) -> bool:
        """Test advanced blockchain integration functionality."""
        try:
            self.logger.info("Testing Advanced Blockchain Integration...")

            if not self.blockchain_integration:
                self.log_test_result("blockchain_integration_advanced", False, "Blockchain not initialized")
                return False

            # Add multiple transactions
            transaction_ids = []
            for i in range(7):  # More than max_transactions_per_block (5)
                tx_id = self.blockchain_integration.add_response_transaction(
                    file_path=f"test_file_{i}.exe",
                    file_hash=f"test_hash_{i}_" + "a" * 50,
                    threat_level=["safe", "suspicious", "malicious"][i % 3],
                    response_actions=[f"action_{i}"],
                    user_id="test_user",
                    evidence_data={"test": f"evidence_{i}"}
                )
                transaction_ids.append(tx_id)

            # Wait for block creation
            await asyncio.sleep(15)  # Wait longer than block_creation_interval

            # Force block creation
            if hasattr(self.blockchain_integration, '_create_new_block'):
                self.blockchain_integration._create_new_block()

            # Test blockchain integrity
            integrity_check = self.blockchain_integration.verify_blockchain_integrity()
            if not integrity_check:
                self.log_test_result("blockchain_integrity", False, "Blockchain integrity check failed")
                return False

            # Test transaction history
            test_hash = f"test_hash_0_" + "a" * 50
            history = self.blockchain_integration.get_transaction_history(test_hash)
            if not history:
                self.log_test_result("blockchain_transaction_history", False, "No transaction history found")
                return False

            # Test blockchain export
            export_path = self.test_dir / "blockchain_export.json"
            export_success = self.blockchain_integration.export_blockchain_data(str(export_path), encrypt=True)
            if not export_success or not export_path.exists():
                self.log_test_result("blockchain_export", False, "Blockchain export failed")
                return False

            self.log_test_result("blockchain_integration_advanced", True, f"Added {len(transaction_ids)} transactions")
            return True

        except Exception as e:
            self.log_test_result("blockchain_integration_advanced", False, str(e))
            return False

    async def test_blockchain_performance(self) -> bool:
        """Test blockchain performance under load."""
        try:
            self.logger.info("Testing Blockchain Performance...")

            if not self.blockchain_integration:
                self.log_test_result("blockchain_performance", False, "Blockchain not initialized")
                return False

            # Performance test: Add many transactions quickly
            start_time = datetime.now()
            transaction_count = 50

            for i in range(transaction_count):
                self.blockchain_integration.add_response_transaction(
                    file_path=f"perf_test_{i}.exe",
                    file_hash=f"perf_hash_{i}_" + "a" * 50,
                    threat_level="safe",
                    response_actions=["performance_test"],
                    user_id="perf_test_user",
                    evidence_data={"performance_test": True, "index": i}
                )

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # Calculate performance metrics
            transactions_per_second = transaction_count / duration

            self.logger.info(f"Performance: {transactions_per_second:.2f} transactions/second")

            # Performance should be reasonable (at least 10 tx/sec)
            if transactions_per_second < 10:
                self.log_test_result("blockchain_performance", False, f"Poor performance: {transactions_per_second:.2f} tx/sec")
                return False

            self.log_test_result("blockchain_performance", True, f"{transactions_per_second:.2f} tx/sec")
            return True

        except Exception as e:
            self.log_test_result("blockchain_performance", False, str(e))
            return False

    async def run_all_tests(self) -> bool:
        """Run all blockchain tests."""
        try:
            self.logger.info("🚀 Starting SBARDS Blockchain Integration Test Suite")

            if not BLOCKCHAIN_AVAILABLE:
                self.logger.error("❌ Blockchain integration not available")
                return False

            # Setup test environment
            test_config = self.setup_test_environment()

            # Run tests in sequence
            tests = [
                ("Cryptographic Manager", self.test_cryptographic_manager),
                ("Blockchain Database", self.test_blockchain_database),
                ("Blockchain Miner", self.test_blockchain_miner),
                ("Basic Integration", self.test_blockchain_integration_basic),
                ("Advanced Integration", self.test_blockchain_integration_advanced),
                ("Performance Test", self.test_blockchain_performance)
            ]

            passed_tests = 0
            total_tests = len(tests)

            for test_name, test_func in tests:
                self.logger.info(f"\n📋 Running {test_name}...")
                try:
                    if asyncio.iscoroutinefunction(test_func):
                        result = await test_func()
                    else:
                        result = test_func()

                    if result:
                        passed_tests += 1

                except Exception as e:
                    self.log_test_result(test_name.lower().replace(" ", "_"), False, str(e))

            # Print summary
            self.logger.info("\n" + "="*60)
            self.logger.info("BLOCKCHAIN INTEGRATION TEST SUMMARY")
            self.logger.info("="*60)

            for result in self.test_results:
                status_symbol = "✓" if result["status"] == "PASSED" else "✗"
                self.logger.info(f"{status_symbol} {result['test']:<30} {result['status']}")
                if result["details"] and result["status"] == "FAILED":
                    self.logger.info(f"    Details: {result['details']}")

            self.logger.info(f"\nOverall: {passed_tests}/{total_tests} tests passed")

            if passed_tests == total_tests:
                self.logger.info("🎉 All blockchain tests passed!")
                return True
            else:
                self.logger.error(f"❌ {total_tests - passed_tests} tests failed")
                return False

        except Exception as e:
            self.logger.error(f"Test suite failed: {e}")
            return False
        finally:
            # Cleanup
            if self.blockchain_integration:
                self.blockchain_integration.shutdown()
            self.cleanup_test_environment()


async def main():
    """Main test function."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('blockchain_test.log')
        ]
    )

    # Run tests
    test_suite = BlockchainIntegrationTest()
    success = await test_suite.run_all_tests()

    if success:
        print("\n✅ Blockchain integration tests completed successfully!")
        print("📋 Check 'blockchain_test.log' for detailed logs")
        return 0
    else:
        print("\n❌ Blockchain integration tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
