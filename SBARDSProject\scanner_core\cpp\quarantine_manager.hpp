#pragma once

#include "response_engine.hpp"
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <atomic>
#include <mutex>
#include <chrono>
#include <filesystem>

#ifdef _WIN32
    #include <windows.h>
    #include <wincrypt.h>
#else
    #include <openssl/evp.h>
    #include <openssl/aes.h>
    #include <openssl/rand.h>
#endif

/**
 * @brief Quarantine levels for different threat types
 */
enum class QuarantineLevel {
    STANDARD = 0,
    HIGH_SECURITY = 1,
    MAXIMUM_SECURITY = 2,
    FORENSIC_PRESERVATION = 3
};

/**
 * @brief Quarantine entry status
 */
enum class QuarantineStatus {
    ACTIVE = 0,
    RELEASED = 1,
    DELETED = 2,
    CORRUPTED = 3,
    UNDER_ANALYSIS = 4
};

/**
 * @brief Encryption algorithms supported
 */
enum class EncryptionAlgorithm {
    AES_256_CBC = 0,
    AES_256_GCM = 1,
    CHACHA20_POLY1305 = 2
};

/**
 * @brief Quarantine entry information
 */
struct QuarantineEntry {
    std::string entry_id;
    std::string original_file_path;
    std::string quarantine_file_path;
    std::string file_hash_sha256;
    std::string file_hash_md5;
    std::string file_hash_sha1;
    uint64_t file_size;
    std::string mime_type;
    QuarantineLevel quarantine_level;
    QuarantineStatus status;
    EncryptionAlgorithm encryption_algorithm;
    std::string encryption_key_id;
    std::chrono::system_clock::time_point quarantine_time;
    std::chrono::system_clock::time_point expiry_time;
    std::string reason;
    std::string threat_classification;
    std::map<std::string, std::string> metadata;
    std::vector<std::string> access_log;
    bool integrity_verified;
    std::string integrity_hash;
};

/**
 * @brief Quarantine vault configuration
 */
struct QuarantineVaultConfig {
    std::string vault_path;
    QuarantineLevel default_level;
    EncryptionAlgorithm default_encryption;
    bool compression_enabled;
    bool integrity_checking_enabled;
    int retention_days;
    uint64_t max_vault_size_bytes;
    bool auto_cleanup_enabled;
    std::string master_key_path;
};

/**
 * @brief Encryption key information
 */
struct EncryptionKey {
    std::string key_id;
    EncryptionAlgorithm algorithm;
    std::vector<uint8_t> key_data;
    std::vector<uint8_t> iv;
    std::chrono::system_clock::time_point created_time;
    std::chrono::system_clock::time_point expiry_time;
    bool active;
};

/**
 * @brief Advanced Quarantine Manager
 * 
 * This class provides comprehensive quarantine capabilities including:
 * - Multi-level quarantine with different security levels
 * - Strong encryption for quarantined files
 * - Integrity verification and monitoring
 * - Automated retention and cleanup policies
 * - Forensic preservation capabilities
 * - Secure key management
 * - Audit logging and compliance
 */
class QuarantineManager {
public:
    /**
     * @brief Constructor
     * @param config Response configuration
     */
    explicit QuarantineManager(const ResponseConfig& config);
    
    /**
     * @brief Destructor
     */
    ~QuarantineManager();
    
    /**
     * @brief Initialize the quarantine manager
     * @return true if initialization successful, false otherwise
     */
    bool initialize();
    
    /**
     * @brief Shutdown the quarantine manager
     */
    void shutdown();
    
    /**
     * @brief Check if the manager is running
     * @return true if running, false otherwise
     */
    bool is_running() const;
    
    // Quarantine operations
    ResponseResult quarantine_file(const std::string& file_path, 
                                 const std::map<std::string, std::string>& metadata,
                                 QuarantineLevel level = QuarantineLevel::STANDARD);
    
    ResponseResult immediate_quarantine(const std::string& file_path,
                                      const std::map<std::string, std::string>& metadata);
    
    ResponseResult secure_quarantine(const std::string& file_path,
                                   const std::map<std::string, std::string>& metadata);
    
    ResponseResult forensic_quarantine(const std::string& file_path,
                                     const std::map<std::string, std::string>& metadata);
    
    // Release and recovery operations
    ResponseResult release_quarantined_file(const std::string& entry_id,
                                           const std::string& destination_path,
                                           bool verify_integrity = true);
    
    ResponseResult delete_quarantined_file(const std::string& entry_id,
                                         bool secure_delete = true);
    
    ResponseResult restore_quarantined_file(const std::string& entry_id,
                                           const std::string& destination_path);
    
    // Information and management
    std::vector<QuarantineEntry> list_quarantined_files(QuarantineStatus status = QuarantineStatus::ACTIVE) const;
    QuarantineEntry get_quarantine_entry(const std::string& entry_id) const;
    bool update_quarantine_entry(const std::string& entry_id, const QuarantineEntry& entry);
    
    // Search and filtering
    std::vector<QuarantineEntry> search_by_hash(const std::string& hash) const;
    std::vector<QuarantineEntry> search_by_threat_type(const std::string& threat_type) const;
    std::vector<QuarantineEntry> search_by_date_range(const std::chrono::system_clock::time_point& start,
                                                     const std::chrono::system_clock::time_point& end) const;
    
    // Vault management
    bool create_quarantine_vault(const std::string& vault_name, const QuarantineVaultConfig& config);
    bool delete_quarantine_vault(const std::string& vault_name);
    std::vector<std::string> list_quarantine_vaults() const;
    
    // Maintenance operations
    bool verify_vault_integrity();
    bool cleanup_expired_entries();
    bool compact_vault();
    uint64_t get_vault_size() const;
    
    // Statistics and monitoring
    std::map<std::string, uint64_t> get_quarantine_statistics() const;
    std::vector<std::string> get_recent_quarantine_activity(int count = 100) const;
    
    // Configuration management
    bool update_vault_config(const QuarantineVaultConfig& config);
    QuarantineVaultConfig get_vault_config() const;

private:
    // Configuration and state
    ResponseConfig config_;
    QuarantineVaultConfig vault_config_;
    std::atomic<bool> running_;
    
    // Quarantine entries management
    mutable std::mutex entries_mutex_;
    std::map<std::string, QuarantineEntry> quarantine_entries_;
    
    // Encryption key management
    mutable std::mutex keys_mutex_;
    std::map<std::string, EncryptionKey> encryption_keys_;
    std::string master_key_id_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    std::map<std::string, uint64_t> quarantine_statistics_;
    
    // Activity logging
    mutable std::mutex activity_mutex_;
    std::vector<std::string> activity_log_;
    
    // Private methods
    std::string generate_entry_id() const;
    std::string generate_key_id() const;
    void update_statistics(const std::string& metric, uint64_t value = 1);
    
    // File operations
    bool move_file_to_quarantine(const std::string& source_path,
                                const std::string& quarantine_path,
                                const QuarantineEntry& entry);
    
    bool encrypt_quarantined_file(const std::string& file_path,
                                 const EncryptionKey& key,
                                 EncryptionAlgorithm algorithm);
    
    bool decrypt_quarantined_file(const std::string& file_path,
                                 const EncryptionKey& key,
                                 EncryptionAlgorithm algorithm);
    
    // Encryption operations
    EncryptionKey generate_encryption_key(EncryptionAlgorithm algorithm);
    bool store_encryption_key(const EncryptionKey& key);
    EncryptionKey retrieve_encryption_key(const std::string& key_id);
    bool delete_encryption_key(const std::string& key_id);
    
    // Integrity verification
    bool calculate_file_integrity_hash(const std::string& file_path, std::string& hash);
    bool verify_file_integrity(const QuarantineEntry& entry);
    
    // Vault operations
    bool create_vault_directory_structure();
    bool initialize_vault_database();
    bool load_quarantine_entries_from_database();
    bool save_quarantine_entry_to_database(const QuarantineEntry& entry);
    bool remove_quarantine_entry_from_database(const std::string& entry_id);
    
    // Cleanup and maintenance
    bool is_entry_expired(const QuarantineEntry& entry) const;
    bool secure_delete_file(const std::string& file_path);
    bool cleanup_orphaned_files();
    
    // Platform-specific implementations
#ifdef _WIN32
    bool initialize_windows_crypto();
    void cleanup_windows_crypto();
    bool encrypt_file_windows(const std::string& file_path, const EncryptionKey& key);
    bool decrypt_file_windows(const std::string& file_path, const EncryptionKey& key);
#else
    bool initialize_openssl_crypto();
    void cleanup_openssl_crypto();
    bool encrypt_file_openssl(const std::string& file_path, const EncryptionKey& key);
    bool decrypt_file_openssl(const std::string& file_path, const EncryptionKey& key);
#endif
    
    // Utility methods
    std::string get_quarantine_file_path(const std::string& entry_id) const;
    std::string get_vault_path_for_level(QuarantineLevel level) const;
    std::string calculate_file_hash(const std::string& file_path, const std::string& algorithm) const;
    
    // Logging and auditing
    void log_quarantine_event(const std::string& event_type,
                             const std::string& entry_id,
                             const std::string& details);
    
    void add_access_log_entry(const std::string& entry_id,
                             const std::string& operation,
                             const std::string& user);
};
