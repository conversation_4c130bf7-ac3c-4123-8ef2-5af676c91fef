#!/bin/bash

# SBARDS Dynamic Analysis Script
# Comprehensive malware analysis in isolated environment

set -e

# Configuration
ANALYSIS_ID=${ANALYSIS_ID:-$(date +%Y%m%d_%H%M%S)}
ANALYSIS_TIMEOUT=${ANALYSIS_TIMEOUT:-300}
SAMPLE_FILE=${1:-""}
OUTPUT_DIR="/analysis/output"
LOGS_DIR="/analysis/logs"
RULES_DIR="/analysis/rules"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "${LOGS_DIR}/analysis_${ANALYSIS_ID}.log"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "${LOGS_DIR}/analysis_${ANALYSIS_ID}.log"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "${LOGS_DIR}/analysis_${ANALYSIS_ID}.log"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "${LOGS_DIR}/analysis_${ANALYSIS_ID}.log"
}

# Check if sample file is provided
if [ -z "$SAMPLE_FILE" ]; then
    error "No sample file provided"
    echo "Usage: $0 <sample_file>"
    exit 1
fi

# Check if sample file exists
if [ ! -f "$SAMPLE_FILE" ]; then
    error "Sample file not found: $SAMPLE_FILE"
    exit 1
fi

# Create output directories
mkdir -p "$OUTPUT_DIR" "$LOGS_DIR"

log "Starting SBARDS Dynamic Analysis"
log "Analysis ID: $ANALYSIS_ID"
log "Sample: $SAMPLE_FILE"
log "Timeout: $ANALYSIS_TIMEOUT seconds"
log "Output Directory: $OUTPUT_DIR"

# Initialize analysis report
REPORT_FILE="${OUTPUT_DIR}/analysis_report_${ANALYSIS_ID}.json"
cat > "$REPORT_FILE" << EOF
{
    "analysis_id": "$ANALYSIS_ID",
    "sample_file": "$SAMPLE_FILE",
    "start_time": "$(date -Iseconds)",
    "timeout": $ANALYSIS_TIMEOUT,
    "environment": {
        "hostname": "$(hostname)",
        "os": "$(uname -a)",
        "user": "$(whoami)",
        "working_dir": "$(pwd)"
    },
    "analysis_stages": {}
}
EOF

# Function to update report
update_report() {
    local stage="$1"
    local status="$2"
    local data="$3"
    
    python3 << EOF
import json
import sys

try:
    with open('$REPORT_FILE', 'r') as f:
        report = json.load(f)
    
    report['analysis_stages']['$stage'] = {
        'status': '$status',
        'timestamp': '$(date -Iseconds)',
        'data': $data
    }
    
    with open('$REPORT_FILE', 'w') as f:
        json.dump(report, f, indent=2)
except Exception as e:
    print(f"Error updating report: {e}", file=sys.stderr)
EOF
}

# Stage 1: File Information Analysis
log "Stage 1: File Information Analysis"
FILE_INFO_FILE="${OUTPUT_DIR}/file_info_${ANALYSIS_ID}.json"

python3 << EOF
import json
import hashlib
import os
import magic
import stat
from datetime import datetime

def get_file_hashes(filepath):
    hashes = {}
    try:
        with open(filepath, 'rb') as f:
            content = f.read()
            hashes['md5'] = hashlib.md5(content).hexdigest()
            hashes['sha1'] = hashlib.sha1(content).hexdigest()
            hashes['sha256'] = hashlib.sha256(content).hexdigest()
    except Exception as e:
        hashes['error'] = str(e)
    return hashes

def get_file_info(filepath):
    info = {}
    try:
        stat_info = os.stat(filepath)
        info['size'] = stat_info.st_size
        info['permissions'] = oct(stat_info.st_mode)
        info['created'] = datetime.fromtimestamp(stat_info.st_ctime).isoformat()
        info['modified'] = datetime.fromtimestamp(stat_info.st_mtime).isoformat()
        info['accessed'] = datetime.fromtimestamp(stat_info.st_atime).isoformat()
        
        # File type detection
        try:
            info['mime_type'] = magic.from_file(filepath, mime=True)
            info['file_type'] = magic.from_file(filepath)
        except:
            info['mime_type'] = 'unknown'
            info['file_type'] = 'unknown'
            
    except Exception as e:
        info['error'] = str(e)
    return info

# Analyze file
file_data = {
    'filepath': '$SAMPLE_FILE',
    'filename': os.path.basename('$SAMPLE_FILE'),
    'hashes': get_file_hashes('$SAMPLE_FILE'),
    'file_info': get_file_info('$SAMPLE_FILE'),
    'analysis_timestamp': datetime.now().isoformat()
}

with open('$FILE_INFO_FILE', 'w') as f:
    json.dump(file_data, f, indent=2)

print("File information analysis completed")
EOF

if [ $? -eq 0 ]; then
    success "File information analysis completed"
    update_report "file_info" "completed" "$(cat $FILE_INFO_FILE)"
else
    error "File information analysis failed"
    update_report "file_info" "failed" "{}"
fi

# Stage 2: YARA Scanning
log "Stage 2: YARA Rule Scanning"
YARA_RESULTS_FILE="${OUTPUT_DIR}/yara_results_${ANALYSIS_ID}.json"

if [ -d "$RULES_DIR" ] && [ "$(ls -A $RULES_DIR/*.yar 2>/dev/null)" ]; then
    log "Running YARA scan with rules from $RULES_DIR"
    
    python3 << EOF
import yara
import json
import os
import glob

def scan_with_yara(filepath, rules_dir):
    results = {
        'matches': [],
        'rules_used': [],
        'scan_time': '$(date -Iseconds)',
        'errors': []
    }
    
    try:
        # Compile all YARA rules
        rule_files = glob.glob(os.path.join(rules_dir, '*.yar'))
        results['rules_used'] = rule_files
        
        for rule_file in rule_files:
            try:
                rules = yara.compile(rule_file)
                matches = rules.match(filepath)
                
                for match in matches:
                    match_data = {
                        'rule': match.rule,
                        'namespace': match.namespace,
                        'tags': list(match.tags),
                        'meta': dict(match.meta),
                        'strings': []
                    }
                    
                    for string in match.strings:
                        match_data['strings'].append({
                            'identifier': string.identifier,
                            'instances': [
                                {
                                    'offset': instance.offset,
                                    'matched_data': instance.matched_data.decode('utf-8', errors='ignore')[:100]
                                }
                                for instance in string.instances
                            ]
                        })
                    
                    results['matches'].append(match_data)
                    
            except Exception as e:
                results['errors'].append(f"Error with rule file {rule_file}: {str(e)}")
                
    except Exception as e:
        results['errors'].append(f"YARA scanning error: {str(e)}")
    
    return results

# Perform YARA scan
yara_results = scan_with_yara('$SAMPLE_FILE', '$RULES_DIR')

with open('$YARA_RESULTS_FILE', 'w') as f:
    json.dump(yara_results, f, indent=2)

print(f"YARA scan completed. Found {len(yara_results['matches'])} matches.")
EOF

    if [ $? -eq 0 ]; then
        success "YARA scanning completed"
        update_report "yara_scan" "completed" "$(cat $YARA_RESULTS_FILE)"
    else
        error "YARA scanning failed"
        update_report "yara_scan" "failed" "{}"
    fi
else
    warning "No YARA rules found, skipping YARA scan"
    update_report "yara_scan" "skipped" "{\"reason\": \"No rules found\"}"
fi

# Stage 3: Static Analysis
log "Stage 3: Static Analysis"
STATIC_ANALYSIS_FILE="${OUTPUT_DIR}/static_analysis_${ANALYSIS_ID}.json"

python3 << EOF
import json
import subprocess
import os
from datetime import datetime

def run_strings_analysis(filepath):
    try:
        result = subprocess.run(['strings', filepath], capture_output=True, text=True, timeout=30)
        strings_list = result.stdout.split('\n')
        # Filter and limit strings
        filtered_strings = [s for s in strings_list if len(s) >= 4 and len(s) <= 100][:500]
        return {
            'count': len(filtered_strings),
            'strings': filtered_strings,
            'suspicious_strings': [s for s in filtered_strings if any(keyword in s.lower() for keyword in 
                ['password', 'admin', 'root', 'cmd', 'shell', 'exec', 'system', 'registry', 'bitcoin', 'crypto'])]
        }
    except Exception as e:
        return {'error': str(e)}

def run_file_analysis(filepath):
    try:
        result = subprocess.run(['file', '-b', filepath], capture_output=True, text=True, timeout=10)
        return {'file_type': result.stdout.strip()}
    except Exception as e:
        return {'error': str(e)}

def run_hexdump_analysis(filepath):
    try:
        result = subprocess.run(['hexdump', '-C', filepath], capture_output=True, text=True, timeout=30)
        lines = result.stdout.split('\n')[:50]  # First 50 lines
        return {'hexdump': lines}
    except Exception as e:
        return {'error': str(e)}

# Perform static analysis
static_data = {
    'filepath': '$SAMPLE_FILE',
    'analysis_time': datetime.now().isoformat(),
    'strings_analysis': run_strings_analysis('$SAMPLE_FILE'),
    'file_analysis': run_file_analysis('$SAMPLE_FILE'),
    'hexdump_analysis': run_hexdump_analysis('$SAMPLE_FILE')
}

with open('$STATIC_ANALYSIS_FILE', 'w') as f:
    json.dump(static_data, f, indent=2)

print("Static analysis completed")
EOF

if [ $? -eq 0 ]; then
    success "Static analysis completed"
    update_report "static_analysis" "completed" "$(cat $STATIC_ANALYSIS_FILE)"
else
    error "Static analysis failed"
    update_report "static_analysis" "failed" "{}"
fi

# Stage 4: Dynamic Execution Analysis
log "Stage 4: Dynamic Execution Analysis"
DYNAMIC_ANALYSIS_FILE="${OUTPUT_DIR}/dynamic_analysis_${ANALYSIS_ID}.json"

# Record initial system state
INITIAL_PROCESSES_FILE="${OUTPUT_DIR}/initial_processes_${ANALYSIS_ID}.txt"
FINAL_PROCESSES_FILE="${OUTPUT_DIR}/final_processes_${ANALYSIS_ID}.txt"
EXECUTION_LOG_FILE="${OUTPUT_DIR}/execution_log_${ANALYSIS_ID}.txt"

ps aux > "$INITIAL_PROCESSES_FILE"
netstat -tuln > "${OUTPUT_DIR}/initial_network_${ANALYSIS_ID}.txt" 2>/dev/null || true

log "Executing sample with monitoring..."

# Make file executable if it's a binary
chmod +x "$SAMPLE_FILE" 2>/dev/null || true

# Execute with timeout and capture output
timeout "$ANALYSIS_TIMEOUT" strace -f -e trace=all -o "${OUTPUT_DIR}/strace_${ANALYSIS_ID}.txt" "$SAMPLE_FILE" > "$EXECUTION_LOG_FILE" 2>&1 || {
    EXECUTION_EXIT_CODE=$?
    log "Sample execution completed with exit code: $EXECUTION_EXIT_CODE"
}

# Record final system state
ps aux > "$FINAL_PROCESSES_FILE"
netstat -tuln > "${OUTPUT_DIR}/final_network_${ANALYSIS_ID}.txt" 2>/dev/null || true

# Analyze execution results
python3 << EOF
import json
import os
import subprocess
from datetime import datetime

def analyze_process_changes(initial_file, final_file):
    try:
        with open(initial_file, 'r') as f:
            initial_processes = set(f.readlines())
        with open(final_file, 'r') as f:
            final_processes = set(f.readlines())
        
        new_processes = final_processes - initial_processes
        return {
            'initial_count': len(initial_processes),
            'final_count': len(final_processes),
            'new_processes': list(new_processes)[:20]  # Limit output
        }
    except Exception as e:
        return {'error': str(e)}

def analyze_strace_output(strace_file):
    try:
        if not os.path.exists(strace_file):
            return {'error': 'strace file not found'}
            
        with open(strace_file, 'r') as f:
            lines = f.readlines()
        
        syscalls = {}
        file_operations = []
        network_operations = []
        
        for line in lines[:1000]:  # Limit analysis
            if '(' in line and ')' in line:
                try:
                    syscall = line.split('(')[0].split()[-1]
                    syscalls[syscall] = syscalls.get(syscall, 0) + 1
                    
                    if any(op in syscall for op in ['open', 'read', 'write', 'unlink']):
                        file_operations.append(line.strip())
                    if any(op in syscall for op in ['socket', 'connect', 'send', 'recv']):
                        network_operations.append(line.strip())
                except:
                    continue
        
        return {
            'total_syscalls': len(lines),
            'unique_syscalls': len(syscalls),
            'syscall_counts': dict(sorted(syscalls.items(), key=lambda x: x[1], reverse=True)[:20]),
            'file_operations': file_operations[:50],
            'network_operations': network_operations[:50]
        }
    except Exception as e:
        return {'error': str(e)}

# Analyze dynamic execution
dynamic_data = {
    'execution_time': datetime.now().isoformat(),
    'timeout_used': $ANALYSIS_TIMEOUT,
    'process_analysis': analyze_process_changes('$INITIAL_PROCESSES_FILE', '$FINAL_PROCESSES_FILE'),
    'strace_analysis': analyze_strace_output('${OUTPUT_DIR}/strace_${ANALYSIS_ID}.txt'),
    'execution_completed': True
}

with open('$DYNAMIC_ANALYSIS_FILE', 'w') as f:
    json.dump(dynamic_data, f, indent=2)

print("Dynamic analysis completed")
EOF

if [ $? -eq 0 ]; then
    success "Dynamic execution analysis completed"
    update_report "dynamic_analysis" "completed" "$(cat $DYNAMIC_ANALYSIS_FILE)"
else
    error "Dynamic execution analysis failed"
    update_report "dynamic_analysis" "failed" "{}"
fi

# Stage 5: Final Report Generation
log "Stage 5: Generating Final Report"

python3 << EOF
import json
from datetime import datetime

# Load all analysis results
try:
    with open('$REPORT_FILE', 'r') as f:
        report = json.load(f)
    
    # Add completion information
    report['end_time'] = datetime.now().isoformat()
    report['status'] = 'completed'
    report['output_files'] = [
        '$FILE_INFO_FILE',
        '$YARA_RESULTS_FILE',
        '$STATIC_ANALYSIS_FILE',
        '$DYNAMIC_ANALYSIS_FILE'
    ]
    
    # Calculate threat score (simple heuristic)
    threat_score = 0.0
    
    # Check YARA matches
    if 'yara_scan' in report['analysis_stages'] and report['analysis_stages']['yara_scan']['status'] == 'completed':
        yara_data = report['analysis_stages']['yara_scan']['data']
        if yara_data.get('matches'):
            threat_score += 0.5
    
    # Check for suspicious strings
    if 'static_analysis' in report['analysis_stages'] and report['analysis_stages']['static_analysis']['status'] == 'completed':
        static_data = report['analysis_stages']['static_analysis']['data']
        suspicious_strings = static_data.get('strings_analysis', {}).get('suspicious_strings', [])
        if suspicious_strings:
            threat_score += 0.3
    
    # Check for system calls
    if 'dynamic_analysis' in report['analysis_stages'] and report['analysis_stages']['dynamic_analysis']['status'] == 'completed':
        dynamic_data = report['analysis_stages']['dynamic_analysis']['data']
        strace_data = dynamic_data.get('strace_analysis', {})
        if strace_data.get('network_operations') or strace_data.get('file_operations'):
            threat_score += 0.2
    
    report['threat_score'] = min(threat_score, 1.0)
    report['threat_level'] = 'HIGH' if threat_score > 0.7 else 'MEDIUM' if threat_score > 0.3 else 'LOW'
    
    # Save final report
    with open('$REPORT_FILE', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"Analysis completed. Threat score: {threat_score:.2f} ({report['threat_level']})")
    
except Exception as e:
    print(f"Error generating final report: {e}")
EOF

success "Analysis completed successfully"
log "Analysis ID: $ANALYSIS_ID"
log "Report file: $REPORT_FILE"
log "Output directory: $OUTPUT_DIR"

# Display summary
echo ""
echo "=== ANALYSIS SUMMARY ==="
echo "Analysis ID: $ANALYSIS_ID"
echo "Sample: $SAMPLE_FILE"
echo "Report: $REPORT_FILE"
echo "Output Directory: $OUTPUT_DIR"
echo ""

# Show threat assessment if available
if [ -f "$REPORT_FILE" ]; then
    python3 -c "
import json
try:
    with open('$REPORT_FILE', 'r') as f:
        report = json.load(f)
    print(f\"Threat Level: {report.get('threat_level', 'UNKNOWN')}\")
    print(f\"Threat Score: {report.get('threat_score', 0.0):.2f}\")
except:
    pass
"
fi

echo "========================="
