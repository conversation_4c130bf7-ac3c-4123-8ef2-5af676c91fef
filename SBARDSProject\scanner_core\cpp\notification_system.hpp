#pragma once

#include "response_engine.hpp"
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <queue>
#include <chrono>

#ifdef _WIN32
    #include <windows.h>
    #include <wininet.h>
#else
    #include <curl/curl.h>
    #include <unistd.h>
    #include <sys/socket.h>
#endif

/**
 * @brief Notification priority levels
 */
enum class NotificationPriority {
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    CRITICAL = 3,
    EMERGENCY = 4
};

/**
 * @brief Notification channels
 */
enum class NotificationChannel {
    EMAIL = 0,
    SMS = 1,
    SLACK = 2,
    WEBHOOK = 3,
    DESKTOP = 4,
    SYSLOG = 5,
    SNMP = 6
};

/**
 * @brief Notification template types
 */
enum class NotificationTemplate {
    SAFE_FILE = 0,
    SUSPICIOUS_FILE = 1,
    MALICIOUS_FILE = 2,
    CRITICAL_THREAT = 3,
    EMERGENCY_RESPONSE = 4,
    SYSTEM_STATUS = 5,
    ISOLATION_ALERT = 6,
    RECOVERY_STATUS = 7
};

/**
 * @brief Notification message structure
 */
struct NotificationMessage {
    std::string message_id;
    NotificationTemplate template_type;
    NotificationPriority priority;
    std::vector<NotificationChannel> channels;
    std::string subject;
    std::string body;
    std::map<std::string, std::string> variables;
    std::vector<std::string> recipients;
    std::chrono::system_clock::time_point created_time;
    std::chrono::system_clock::time_point scheduled_time;
    bool sent;
    std::vector<std::string> delivery_status;
    std::string error_message;
};

/**
 * @brief Email configuration
 */
struct EmailConfig {
    std::string smtp_server;
    uint16_t smtp_port;
    std::string username;
    std::string password;
    bool use_tls;
    bool use_ssl;
    std::string from_address;
    std::string from_name;
    std::vector<std::string> default_recipients;
};

/**
 * @brief Slack configuration
 */
struct SlackConfig {
    std::string webhook_url;
    std::string bot_token;
    std::string channel;
    std::string username;
    std::string icon_emoji;
    std::vector<std::string> mention_users;
};

/**
 * @brief SMS configuration
 */
struct SMSConfig {
    std::string provider;
    std::string api_key;
    std::string api_secret;
    std::string sender_id;
    std::vector<std::string> default_recipients;
};

/**
 * @brief Webhook configuration
 */
struct WebhookConfig {
    std::string url;
    std::string method;
    std::map<std::string, std::string> headers;
    std::string auth_token;
    std::string content_type;
    int timeout_seconds;
};

/**
 * @brief Advanced Notification System
 * 
 * This class provides comprehensive notification capabilities including:
 * - Multi-channel notifications (Email, SMS, Slack, Webhooks)
 * - Priority-based message routing
 * - Template-based message generation
 * - Delivery tracking and retry mechanisms
 * - Rate limiting and throttling
 * - Escalation procedures
 */
class NotificationSystem {
public:
    /**
     * @brief Constructor
     * @param config Response configuration
     */
    explicit NotificationSystem(const ResponseConfig& config);
    
    /**
     * @brief Destructor
     */
    ~NotificationSystem();
    
    /**
     * @brief Initialize the notification system
     * @return true if initialization successful, false otherwise
     */
    bool initialize();
    
    /**
     * @brief Shutdown the notification system
     */
    void shutdown();
    
    /**
     * @brief Check if the system is running
     * @return true if running, false otherwise
     */
    bool is_running() const;
    
    // Notification methods for different threat levels
    ResponseResult send_safe_file_notification(const AnalysisResults& results);
    ResponseResult send_suspicious_file_alert(const AnalysisResults& results);
    ResponseResult send_malicious_file_alert(const AnalysisResults& results);
    ResponseResult send_critical_threat_alert(const AnalysisResults& results);
    ResponseResult send_emergency_response_alert(const AnalysisResults& results);
    
    // System status notifications
    ResponseResult send_system_status_notification(const std::string& status, 
                                                  const std::map<std::string, std::string>& details);
    
    ResponseResult send_isolation_alert(const std::string& session_id,
                                      const std::string& file_path,
                                      const std::string& isolation_type);
    
    ResponseResult send_recovery_status_notification(const std::string& recovery_id,
                                                   const std::string& status,
                                                   const std::map<std::string, std::string>& details);
    
    // Custom notification methods
    ResponseResult send_custom_notification(const NotificationMessage& message);
    
    ResponseResult send_notification_to_channels(const std::string& subject,
                                                const std::string& body,
                                                const std::vector<NotificationChannel>& channels,
                                                NotificationPriority priority = NotificationPriority::NORMAL);
    
    // Configuration management
    bool update_email_config(const EmailConfig& config);
    bool update_slack_config(const SlackConfig& config);
    bool update_sms_config(const SMSConfig& config);
    bool update_webhook_config(const WebhookConfig& config);
    
    // Statistics and monitoring
    std::map<std::string, uint64_t> get_notification_statistics() const;
    std::vector<NotificationMessage> get_recent_notifications(int count = 100) const;
    std::vector<NotificationMessage> get_failed_notifications() const;
    
    // Message management
    bool retry_failed_notification(const std::string& message_id);
    bool cancel_scheduled_notification(const std::string& message_id);
    
private:
    // Configuration and state
    ResponseConfig config_;
    std::atomic<bool> running_;
    
    // Channel configurations
    EmailConfig email_config_;
    SlackConfig slack_config_;
    SMSConfig sms_config_;
    WebhookConfig webhook_config_;
    
    // Message queue and processing
    mutable std::mutex queue_mutex_;
    std::queue<NotificationMessage> message_queue_;
    std::condition_variable queue_cv_;
    std::thread notification_worker_thread_;
    
    // Message tracking
    mutable std::mutex messages_mutex_;
    std::map<std::string, NotificationMessage> sent_messages_;
    std::vector<NotificationMessage> failed_messages_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    std::map<std::string, uint64_t> notification_statistics_;
    
    // Rate limiting
    mutable std::mutex rate_limit_mutex_;
    std::map<NotificationChannel, std::chrono::system_clock::time_point> last_sent_times_;
    std::map<NotificationChannel, int> rate_limits_;
    
    // Private methods
    std::string generate_message_id() const;
    void update_statistics(const std::string& metric, uint64_t value = 1);
    
    // Worker thread
    void notification_worker_loop();
    
    // Message processing
    bool process_notification_message(NotificationMessage& message);
    bool send_to_channel(const NotificationMessage& message, NotificationChannel channel);
    
    // Channel implementations
    bool send_email_notification(const NotificationMessage& message);
    bool send_sms_notification(const NotificationMessage& message);
    bool send_slack_notification(const NotificationMessage& message);
    bool send_webhook_notification(const NotificationMessage& message);
    bool send_desktop_notification(const NotificationMessage& message);
    bool send_syslog_notification(const NotificationMessage& message);
    
    // Template processing
    std::string generate_message_subject(NotificationTemplate template_type, 
                                       const std::map<std::string, std::string>& variables);
    
    std::string generate_message_body(NotificationTemplate template_type,
                                    const std::map<std::string, std::string>& variables);
    
    std::string process_template_variables(const std::string& template_text,
                                         const std::map<std::string, std::string>& variables);
    
    // Rate limiting
    bool check_rate_limit(NotificationChannel channel);
    void update_rate_limit(NotificationChannel channel);
    
    // Utility methods
    std::vector<std::string> get_default_recipients(NotificationChannel channel);
    NotificationPriority determine_priority_from_threat_level(ThreatLevel threat_level);
    std::vector<NotificationChannel> get_channels_for_priority(NotificationPriority priority);
    
    // Platform-specific implementations
#ifdef _WIN32
    bool initialize_windows_notifications();
    void cleanup_windows_notifications();
    bool send_windows_desktop_notification(const NotificationMessage& message);
#else
    bool initialize_linux_notifications();
    void cleanup_linux_notifications();
    bool send_linux_desktop_notification(const NotificationMessage& message);
#endif
    
    // HTTP client methods
    bool send_http_request(const std::string& url,
                          const std::string& method,
                          const std::string& data,
                          const std::map<std::string, std::string>& headers,
                          std::string& response);
    
    // Logging
    void log_notification_event(const std::string& event_type,
                              const std::string& message_id,
                              const std::string& details);
};
