# SBARDS Project Documentation

## Overview

The SBARDS (Security-Based Automated Recursive Directory Scanner) Project is designed to scan directories recursively for potential security threats using YARA rules. This documentation provides comprehensive information about the project, with a focus on the pre-scanning phase.

## Documentation Structure

The documentation is organized into the following sections:

1. **[Pre-scanning Documentation](prescanning_documentation.md)**: Overview of the pre-scanning phase architecture, workflow, and components.
2. **[Implementation Guide](prescanning_implementation_guide.md)**: Technical details about the implementation of the pre-scanning phase.
3. **[Workflow Diagrams](prescanning_workflow.md)**: Visual representations of the pre-scanning process and component interactions.
4. **[Troubleshooting Guide](prescanning_troubleshooting.md)**: Solutions to common issues and problems.
5. **[Advanced Features Documentation](advanced_features_documentation.md)**: Detailed information about advanced ransomware detection, download monitoring, and hash-based optimization.

## Quick Start

### Prerequisites

- Python 3.8+ (3.10+ recommended)
- YARA 4.1.0+
- Required Python packages (see `requirements.txt`)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/sbards-project.git
   cd sbards-project
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Configure the project:
   - Copy `config.json.example` to `config.json`
   - Update the configuration with your settings

### Running the Pre-scanning Phase

The pre-scanning phase includes multiple components, including file scanning and download monitoring:

```bash
# Run the complete pre-scanning phase
python run_prescanning.py

# Run only the download monitoring component
python run_prescanning.py --download-monitor-only

# Run pre-scanning without download monitoring
python run_prescanning.py --no-download-monitor

# Skip the legacy scan component
python run_prescanning.py --skip-legacy

# Skip the C++ scanner test
python run_prescanning.py --skip-cpp-test

# Use a custom configuration file
python run_prescanning.py --config custom_config.json
```

## Project Structure

```
SBARDS Project
├── config.json                 # Configuration file
├── run_prescanning.py          # Main entry point for pre-scanning (includes download monitoring)
├── rules/                      # YARA rules directory
│   ├── custom_rules.yar        # Custom YARA rules
│   ├── malware_rules.yar       # Malware detection rules
│   ├── enhanced_rules.yar      # Enhanced detection rules
│   ├── permission_rules.yar    # Permission-based detection rules
│   └── ransomware_advanced_rules.yar # Advanced ransomware rules
├── scanner_core/               # Core scanning components
│   ├── cpp/                    # C++ scanner components
│   │   ├── mock_scanner.cpp    # C++ scanner implementation
│   │   ├── mock_scanner.py     # Python wrapper for C++ scanner
│   │   └── yara_scanner.cpp    # C++ YARA scanner implementation
│   ├── python/                 # Python scanner components
│   │   ├── orchestrator.py     # Main orchestrator for scanning
│   │   └── yara_wrapper.py     # Python YARA scanner wrapper
│   └── utils/                  # Utility functions
│       ├── config_loader.py    # Configuration loader
│       ├── logger.py           # Logging utilities
│       ├── file_scanner.py     # File scanning utilities
│       └── download_monitor.py # Download monitoring system
├── output/                     # Output directory for reports
├── logs/                       # Log directory
└── docs/                       # Documentation
    ├── README.md               # This file
    ├── prescanning_documentation.md
    ├── prescanning_implementation_guide.md
    ├── prescanning_workflow.md
    ├── prescanning_troubleshooting.md
    └── advanced_features_documentation.md
```

## Configuration

The pre-scanning phase is configured through the `config.json` file. Here's an example configuration:

```json
{
    "scanner": {
        "target_directory": "E:\\WA",
        "recursive": true,
        "max_depth": 10,
        "max_file_size_mb": 100,
        "exclude_dirs": [
            "System Volume Information",
            "$RECYCLE.BIN",
            "Windows",
            "Program Files",
            "Program Files (x86)"
        ],
        "exclude_extensions": [".exe", ".dll", ".sys"]
    },
    "rules": {
        "rule_files": [
            "rules/custom_rules.yar",
            "rules/malware_rules.yar",
            "rules/enhanced_rules.yar",
            "rules/permission_rules.yar",
            "rules/ransomware_advanced_rules.yar"
        ],
        "enable_categories": [
            "ransomware",
            "trojan",
            "backdoor",
            "keylogger",
            "spyware",
            "general",
            "document",
            "executable",
            "test"
        ]
    },
    "features": {
        "monitor_downloads": true,
        "hash_optimization": true
    },
    "performance": {
        "threads": 8,
        "batch_size": 20,
        "timeout_seconds": 60,
        "max_memory_mb": 1024,
        "adaptive_threading": true,
        "priority_extensions": [".doc", ".docx", ".xls", ".xlsx", ".pdf", ".zip", ".rar", ".7z", ".exe", ".dll"]
    },
    "output": {
        "log_directory": "logs",
        "output_directory": "output",
        "json_output": true,
        "csv_output": true,
        "html_report": true,
        "log_level": "info"
    }
}
```

## Key Features

The pre-scanning phase includes the following key features:

1. **Recursive Directory Scanning**: Scan directories and subdirectories for files.
2. **YARA Rule Integration**: Apply YARA rules to detect potential threats.
3. **Parallel Processing**: Scan files in parallel for improved performance.
4. **Memory Management**: Adaptive memory management for handling large directories.
5. **Unicode Support**: Proper handling of Unicode filenames across platforms.
6. **Comprehensive Reporting**: Generate JSON, CSV, and HTML reports with visualizations.
7. **Cross-Platform Compatibility**: Run on both Windows and Linux platforms.
8. **Download Monitoring**: Automatically scan files downloaded from browsers and WhatsApp.
9. **Hash-Based Optimization**: Skip rescanning of previously scanned files using hash comparison.
10. **Advanced Ransomware Detection**: Specialized rules for detecting ransomware, including permission-based detection.

## Best Practices

For best results when using the SBARDS project:

1. **Optimize YARA Rules**: Keep rules efficient and focused on specific threats.
2. **Configure Performance Settings**: Adjust thread count and batch size based on your system.
3. **Prioritize File Types**: Configure priority extensions for high-risk file types.
4. **Regular Updates**: Keep YARA rules updated with the latest threat signatures.
5. **Monitor Resource Usage**: Use adaptive threading to prevent system overload.
6. **Review Reports**: Regularly review scan reports for potential threats.

## Troubleshooting

If you encounter issues, refer to the [Troubleshooting Guide](prescanning_troubleshooting.md) for solutions to common problems.

## Contributing

Contributions to the SBARDS project are welcome! Please follow these steps:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
