# SBARDS Unified Response Engine - CMake Build Configuration
# Cross-platform build system for C++ Unified Response Core with Integrated Data Management

cmake_minimum_required(VERSION 3.16)
project(SBARDSUnifiedResponseEngine VERSION 2.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific options
if(MSVC)
    # Visual Studio
    add_compile_options(/W4 /WX /permissive-)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
else()
    # GCC/Clang
    add_compile_options(-Wall -Wextra -Werror -pedantic)
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options(-O3 -DNDEBUG)
    else()
        add_compile_options(-g -O0)
    endif()
endif()

# Platform-specific settings
if(WIN32)
    add_compile_definitions(WIN32_LEAN_AND_MEAN NOMINMAX)
    set(PLATFORM_LIBS ws2_32 crypt32 advapi32)
elseif(UNIX AND NOT APPLE)
    set(PLATFORM_LIBS pthread dl)
elseif(APPLE)
    set(PLATFORM_LIBS pthread)
    find_library(SECURITY_FRAMEWORK Security)
    list(APPEND PLATFORM_LIBS ${SECURITY_FRAMEWORK})
endif()

# Find required packages
find_package(OpenSSL REQUIRED)
find_package(Threads REQUIRED)

# Optional packages
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(JSONCPP jsoncpp)
endif()

# Source files for SBARDS Response Engine
set(RESPONSE_ENGINE_SOURCES
    sbards_response_engine.cpp
)

set(RESPONSE_ENGINE_HEADERS
    sbards_response_engine.hpp
)

# Create the main SBARDS Response Engine library
add_library(sbards_response_engine SHARED ${RESPONSE_ENGINE_SOURCES})

# Set target properties
set_target_properties(sbards_response_engine PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    OUTPUT_NAME "sbards_response_engine"
)

# Include directories
target_include_directories(sbards_response_engine
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link libraries
target_link_libraries(sbards_response_engine
    PUBLIC
        OpenSSL::SSL
        OpenSSL::Crypto
        Threads::Threads
    PRIVATE
        ${PLATFORM_LIBS}
)

# Add JSON support if available
if(JSONCPP_FOUND)
    target_link_libraries(sbards_response_engine PRIVATE ${JSONCPP_LIBRARIES})
    target_include_directories(sbards_response_engine PRIVATE ${JSONCPP_INCLUDE_DIRS})
    target_compile_definitions(sbards_response_engine PRIVATE HAVE_JSONCPP)
endif()

# Test executable
add_executable(test_sbards_response test_main.cpp)
target_link_libraries(test_sbards_response sbards_response_engine)

# Install targets
install(TARGETS sbards_response_engine
    EXPORT SBARDSResponseEngineTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

install(FILES ${RESPONSE_ENGINE_HEADERS}
    DESTINATION include/sbards
)

# Export targets
install(EXPORT SBARDSResponseEngineTargets
    FILE SBARDSResponseEngineTargets.cmake
    NAMESPACE SBARDS::
    DESTINATION lib/cmake/SBARDSResponseEngine
)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${OPENSSL_INCLUDE_DIR})

# Source files
set(UNIFIED_RESPONSE_ENGINE_SOURCES
    unified_response_engine.hpp
    unified_response_engine.cpp
    response_core.cpp
    data_core.cpp
    security_core.cpp
    performance_core.cpp
)

# Create shared library
add_library(sbards_unified_response SHARED ${UNIFIED_RESPONSE_ENGINE_SOURCES})

# Create static library
add_library(sbards_unified_response_static STATIC ${UNIFIED_RESPONSE_ENGINE_SOURCES})

# Set library properties
set_target_properties(sbards_unified_response PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 2
    OUTPUT_NAME "sbards_unified_response"
    POSITION_INDEPENDENT_CODE ON
)

set_target_properties(sbards_unified_response_static PROPERTIES
    OUTPUT_NAME "sbards_unified_response_static"
    POSITION_INDEPENDENT_CODE ON
)

# Link libraries
target_link_libraries(sbards_unified_response
    ${OPENSSL_LIBRARIES}
    ${ZLIB_LIBRARIES}
    ${PLATFORM_LIBS}
    Threads::Threads
)

target_link_libraries(sbards_unified_response_static
    ${OPENSSL_LIBRARIES}
    ${ZLIB_LIBRARIES}
    ${PLATFORM_LIBS}
    Threads::Threads
)

# Compiler definitions
target_compile_definitions(sbards_response_engine PRIVATE
    SBARDS_RESPONSE_ENGINE_EXPORTS
    OPENSSL_API_COMPAT=0x10100000L
)

target_compile_definitions(sbards_response_engine_static PRIVATE
    SBARDS_RESPONSE_ENGINE_STATIC
    OPENSSL_API_COMPAT=0x10100000L
)

# Installation
install(TARGETS sbards_response_engine sbards_response_engine_static
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES response_engine.hpp
    DESTINATION include/sbards
)

# Create test executable (optional)
option(BUILD_TESTS "Build test executable" ON)
if(BUILD_TESTS)
    add_executable(response_engine_test test_main.cpp)
    target_link_libraries(response_engine_test sbards_response_engine_static)

    # Add test
    enable_testing()
    add_test(NAME ResponseEngineTest COMMAND response_engine_test)
endif()

# Create Python extension module
option(BUILD_PYTHON_MODULE "Build Python extension module" ON)
if(BUILD_PYTHON_MODULE)
    find_package(Python3 COMPONENTS Interpreter Development)
    if(Python3_FOUND)
        add_library(sbards_response_python MODULE ${RESPONSE_ENGINE_SOURCES})
        target_link_libraries(sbards_response_python
            ${OPENSSL_LIBRARIES}
            ${PLATFORM_LIBS}
            Threads::Threads
            Python3::Python
        )

        set_target_properties(sbards_response_python PROPERTIES
            PREFIX ""
            OUTPUT_NAME "sbards_response_engine"
            SUFFIX "${Python3_SOABI}${CMAKE_SHARED_LIBRARY_SUFFIX}"
        )

        target_compile_definitions(sbards_response_python PRIVATE
            SBARDS_PYTHON_MODULE
            OPENSSL_API_COMPAT=0x10100000L
        )
    endif()
endif()

# Documentation
option(BUILD_DOCS "Build documentation" OFF)
if(BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)

        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)

        add_custom_target(docs ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    endif()
endif()

# Package configuration
include(CMakePackageConfigHelpers)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/SBARDSResponseEngineConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/SBARDSResponseEngineConfig.cmake"
    INSTALL_DESTINATION lib/cmake/SBARDSResponseEngine
)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/SBARDSResponseEngineConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/SBARDSResponseEngineConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/SBARDSResponseEngineConfigVersion.cmake"
    DESTINATION lib/cmake/SBARDSResponseEngine
)

# Export targets
install(EXPORT SBARDSResponseEngineTargets
    FILE SBARDSResponseEngineTargets.cmake
    NAMESPACE SBARDS::
    DESTINATION lib/cmake/SBARDSResponseEngine
)

export(EXPORT SBARDSResponseEngineTargets
    FILE "${CMAKE_CURRENT_BINARY_DIR}/SBARDSResponseEngineTargets.cmake"
    NAMESPACE SBARDS::
)

# Print configuration summary
message(STATUS "SBARDS Response Engine Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  OpenSSL version: ${OPENSSL_VERSION}")
message(STATUS "  Build tests: ${BUILD_TESTS}")
message(STATUS "  Build Python module: ${BUILD_PYTHON_MODULE}")
message(STATUS "  Build documentation: ${BUILD_DOCS}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
