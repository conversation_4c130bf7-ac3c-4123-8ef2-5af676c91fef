"""
SBARDS Behavioral Analyzer
Advanced behavioral analysis with machine learning capabilities

Features:
- Ransomware behavior detection
- Resource usage analysis
- Pattern recognition with ML
- Anomaly detection
- Threat scoring and classification
"""

import os
import asyncio
import logging
import json
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import pickle
import hashlib

# Machine learning imports
try:
    from sklearn.ensemble import IsolationForest, RandomForestClassifier
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report, confusion_matrix
    import joblib
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

class BehavioralAnalyzer:
    """
    Advanced Behavioral Analyzer
    
    Analyzes dynamic analysis results to detect malicious behavior patterns
    using machine learning and rule-based approaches
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Behavioral Analyzer
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.BehavioralAnalyzer")
        
        # Behavioral analysis configuration
        self.behavioral_config = config.get("dynamic_analysis", {}).get("behavioral_analysis", {})
        self.enabled = self.behavioral_config.get("enabled", True)
        self.ml_enabled = self.behavioral_config.get("ml_enabled", True) and ML_AVAILABLE
        
        if not ML_AVAILABLE:
            self.logger.warning("Machine learning libraries not available, using rule-based analysis only")
            self.ml_enabled = False
        
        # Model configuration
        self.model_path = self.behavioral_config.get("ml_model_path", "models/behavioral_analysis.pkl")
        
        # Detection configurations
        self.ransomware_config = self.behavioral_config.get("ransomware_detection", {})
        self.resource_config = self.behavioral_config.get("resource_analysis", {})
        self.pattern_config = self.behavioral_config.get("pattern_detection", {})
        
        # Initialize ML models
        self.anomaly_detector = None
        self.threat_classifier = None
        self.feature_scaler = None
        
        if self.ml_enabled:
            self._init_ml_models()
        
        # Behavioral patterns and rules
        self.ransomware_indicators = self._load_ransomware_indicators()
        self.malware_patterns = self._load_malware_patterns()
        self.benign_patterns = self._load_benign_patterns()
        
        # Feature extractors
        self.feature_extractors = {
            'file_operations': self._extract_file_features,
            'network_activity': self._extract_network_features,
            'process_behavior': self._extract_process_features,
            'registry_operations': self._extract_registry_features,
            'api_calls': self._extract_api_features,
            'resource_usage': self._extract_resource_features
        }
    
    def _init_ml_models(self):
        """Initialize machine learning models"""
        try:
            # Load pre-trained models if available
            if os.path.exists(self.model_path):
                self._load_pretrained_models()
            else:
                # Initialize new models
                self.anomaly_detector = IsolationForest(
                    contamination=0.1,
                    random_state=42,
                    n_estimators=100
                )
                
                self.threat_classifier = RandomForestClassifier(
                    n_estimators=100,
                    random_state=42,
                    max_depth=10
                )
                
                self.feature_scaler = StandardScaler()
                
                self.logger.info("Initialized new ML models")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize ML models: {e}")
            self.ml_enabled = False
    
    def _load_pretrained_models(self):
        """Load pre-trained models"""
        try:
            with open(self.model_path, 'rb') as f:
                models = pickle.load(f)
            
            self.anomaly_detector = models.get('anomaly_detector')
            self.threat_classifier = models.get('threat_classifier')
            self.feature_scaler = models.get('feature_scaler')
            
            self.logger.info(f"Loaded pre-trained models from {self.model_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to load pre-trained models: {e}")
            self._init_ml_models()
    
    def _save_models(self):
        """Save trained models"""
        try:
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            
            models = {
                'anomaly_detector': self.anomaly_detector,
                'threat_classifier': self.threat_classifier,
                'feature_scaler': self.feature_scaler
            }
            
            with open(self.model_path, 'wb') as f:
                pickle.dump(models, f)
            
            self.logger.info(f"Saved models to {self.model_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save models: {e}")
    
    async def analyze_behavior_async(self, analysis_result) -> Dict[str, Any]:
        """
        Perform comprehensive behavioral analysis
        
        Args:
            analysis_result: Dynamic analysis result object
            
        Returns:
            Behavioral analysis results
        """
        try:
            self.logger.info(f"Starting behavioral analysis for: {analysis_result.analysis_id}")
            
            # Extract features from analysis results
            features = await self._extract_all_features(analysis_result)
            
            # Rule-based analysis
            rule_based_results = await self._rule_based_analysis(analysis_result, features)
            
            # ML-based analysis (if enabled)
            ml_results = {}
            if self.ml_enabled:
                ml_results = await self._ml_based_analysis(features)
            
            # Ransomware-specific analysis
            ransomware_results = await self._analyze_ransomware_behavior(analysis_result, features)
            
            # Resource usage analysis
            resource_results = await self._analyze_resource_usage(analysis_result, features)
            
            # Pattern detection
            pattern_results = await self._detect_behavior_patterns(analysis_result, features)
            
            # Combine results and calculate scores
            combined_results = {
                "success": True,
                "analysis_id": analysis_result.analysis_id,
                "timestamp": datetime.now().isoformat(),
                "features": features,
                "rule_based_analysis": rule_based_results,
                "ml_analysis": ml_results,
                "ransomware_analysis": ransomware_results,
                "resource_analysis": resource_results,
                "pattern_analysis": pattern_results,
                "threat_score": 0.0,
                "confidence": 0.0,
                "indicators": [],
                "recommendations": []
            }
            
            # Calculate overall threat score
            combined_results["threat_score"] = self._calculate_behavioral_threat_score(combined_results)
            combined_results["confidence"] = self._calculate_confidence_score(combined_results)
            
            # Extract indicators
            combined_results["indicators"] = self._extract_behavioral_indicators(combined_results)
            
            # Generate recommendations
            combined_results["recommendations"] = self._generate_behavioral_recommendations(combined_results)
            
            self.logger.info(f"Behavioral analysis completed: {analysis_result.analysis_id} "
                           f"(Score: {combined_results['threat_score']:.2f})")
            
            return combined_results
            
        except Exception as e:
            self.logger.error(f"Behavioral analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _extract_all_features(self, analysis_result) -> Dict[str, Any]:
        """Extract all behavioral features"""
        features = {}
        
        try:
            for feature_type, extractor in self.feature_extractors.items():
                features[feature_type] = await extractor(analysis_result)
            
            # Add metadata features
            features['metadata'] = {
                'file_size': self._get_file_size(analysis_result.file_path),
                'file_type': self._get_file_type(analysis_result.file_path),
                'analysis_duration': analysis_result.duration_seconds,
                'sandbox_type': getattr(analysis_result, 'sandbox_type', 'unknown')
            }
            
            return features
            
        except Exception as e:
            self.logger.error(f"Feature extraction failed: {e}")
            return {}
    
    async def _extract_file_features(self, analysis_result) -> Dict[str, Any]:
        """Extract file operation features"""
        features = {
            'files_created': 0,
            'files_modified': 0,
            'files_deleted': 0,
            'files_encrypted': 0,
            'suspicious_extensions': 0,
            'system_files_accessed': 0,
            'temp_files_created': 0
        }
        
        try:
            # Extract from sandbox results
            if hasattr(analysis_result, 'sandbox_results'):
                fs_changes = analysis_result.sandbox_results.get('filesystem_changes', [])
                
                for change in fs_changes:
                    if 'created' in change.lower():
                        features['files_created'] += 1
                    if 'modified' in change.lower():
                        features['files_modified'] += 1
                    if 'deleted' in change.lower():
                        features['files_deleted'] += 1
                    
                    # Check for suspicious extensions
                    if any(ext in change.lower() for ext in ['.encrypted', '.locked', '.crypto']):
                        features['files_encrypted'] += 1
                    
                    # Check for system file access
                    if any(path in change.lower() for path in ['system32', 'windows', '/etc', '/usr']):
                        features['system_files_accessed'] += 1
                    
                    # Check for temp file creation
                    if any(path in change.lower() for path in ['temp', 'tmp', 'appdata']):
                        features['temp_files_created'] += 1
            
            return features
            
        except Exception as e:
            self.logger.error(f"File feature extraction failed: {e}")
            return features
    
    async def _extract_network_features(self, analysis_result) -> Dict[str, Any]:
        """Extract network activity features"""
        features = {
            'tcp_connections': 0,
            'udp_connections': 0,
            'dns_queries': 0,
            'http_requests': 0,
            'https_requests': 0,
            'suspicious_domains': 0,
            'c2_indicators': 0,
            'data_uploaded': 0,
            'data_downloaded': 0
        }
        
        try:
            # Extract from network analysis
            if hasattr(analysis_result, 'network_analysis'):
                network_data = analysis_result.network_analysis
                
                features['tcp_connections'] = len(network_data.get('tcp_connections', []))
                features['udp_connections'] = len(network_data.get('udp_connections', []))
                features['dns_queries'] = len(network_data.get('dns_queries', []))
                features['http_requests'] = len(network_data.get('http_requests', []))
                
                # Analyze domains for suspicious patterns
                domains = network_data.get('domains_contacted', [])
                for domain in domains:
                    if self._is_suspicious_domain(domain):
                        features['suspicious_domains'] += 1
                    if self._is_c2_indicator(domain):
                        features['c2_indicators'] += 1
            
            return features
            
        except Exception as e:
            self.logger.error(f"Network feature extraction failed: {e}")
            return features
    
    async def _extract_process_features(self, analysis_result) -> Dict[str, Any]:
        """Extract process behavior features"""
        features = {
            'processes_created': 0,
            'child_processes': 0,
            'process_injections': 0,
            'privilege_escalations': 0,
            'persistence_mechanisms': 0,
            'anti_analysis_techniques': 0
        }
        
        try:
            # Extract from sandbox results
            if hasattr(analysis_result, 'sandbox_results'):
                processes = analysis_result.sandbox_results.get('processes', {})
                
                before = processes.get('before', [])
                after = processes.get('after', [])
                
                features['processes_created'] = max(0, len(after) - len(before))
            
            # Extract from API monitoring
            if hasattr(analysis_result, 'api_monitoring'):
                api_calls = analysis_result.api_monitoring.get('api_calls', [])
                
                for call in api_calls:
                    api_name = call.get('function_name', '').lower()
                    
                    if 'createprocess' in api_name:
                        features['processes_created'] += 1
                    if 'writeprocessmemory' in api_name or 'setthreadcontext' in api_name:
                        features['process_injections'] += 1
                    if 'adjusttokenprivileges' in api_name:
                        features['privilege_escalations'] += 1
            
            return features
            
        except Exception as e:
            self.logger.error(f"Process feature extraction failed: {e}")
            return features
    
    async def _extract_registry_features(self, analysis_result) -> Dict[str, Any]:
        """Extract registry operation features"""
        features = {
            'registry_keys_created': 0,
            'registry_keys_modified': 0,
            'registry_keys_deleted': 0,
            'autorun_entries': 0,
            'security_settings_modified': 0
        }
        
        try:
            # Extract from API monitoring
            if hasattr(analysis_result, 'api_monitoring'):
                api_calls = analysis_result.api_monitoring.get('api_calls', [])
                
                for call in api_calls:
                    api_name = call.get('function_name', '').lower()
                    
                    if 'regcreatekey' in api_name:
                        features['registry_keys_created'] += 1
                    if 'regsetvalue' in api_name:
                        features['registry_keys_modified'] += 1
                        
                        # Check for autorun entries
                        params = call.get('parameters', [])
                        if any('run' in str(param).lower() for param in params):
                            features['autorun_entries'] += 1
                    
                    if 'regdeletekey' in api_name:
                        features['registry_keys_deleted'] += 1
            
            return features
            
        except Exception as e:
            self.logger.error(f"Registry feature extraction failed: {e}")
            return features
    
    async def _extract_api_features(self, analysis_result) -> Dict[str, Any]:
        """Extract API call features"""
        features = {
            'total_api_calls': 0,
            'file_api_calls': 0,
            'network_api_calls': 0,
            'process_api_calls': 0,
            'registry_api_calls': 0,
            'crypto_api_calls': 0,
            'suspicious_api_calls': 0
        }
        
        try:
            if hasattr(analysis_result, 'api_monitoring'):
                api_calls = analysis_result.api_monitoring.get('api_calls', [])
                features['total_api_calls'] = len(api_calls)
                
                for call in api_calls:
                    api_name = call.get('function_name', '').lower()
                    
                    # Categorize API calls
                    if any(api in api_name for api in ['createfile', 'readfile', 'writefile', 'deletefile']):
                        features['file_api_calls'] += 1
                    
                    if any(api in api_name for api in ['socket', 'connect', 'send', 'recv', 'internetopen']):
                        features['network_api_calls'] += 1
                    
                    if any(api in api_name for api in ['createprocess', 'openprocess', 'terminateprocess']):
                        features['process_api_calls'] += 1
                    
                    if any(api in api_name for api in ['regcreatekey', 'regsetvalue', 'regqueryvalue']):
                        features['registry_api_calls'] += 1
                    
                    if any(api in api_name for api in ['cryptacquirecontext', 'cryptencrypt', 'cryptdecrypt']):
                        features['crypto_api_calls'] += 1
                    
                    # Check for suspicious APIs
                    if self._is_suspicious_api(api_name):
                        features['suspicious_api_calls'] += 1
            
            return features
            
        except Exception as e:
            self.logger.error(f"API feature extraction failed: {e}")
            return features
    
    async def _extract_resource_features(self, analysis_result) -> Dict[str, Any]:
        """Extract resource usage features"""
        features = {
            'peak_memory_usage': 0,
            'average_cpu_usage': 0,
            'disk_io_operations': 0,
            'network_io_operations': 0,
            'execution_time': 0
        }
        
        try:
            if hasattr(analysis_result, 'memory_analysis'):
                memory_stats = analysis_result.memory_analysis.get('statistics', {})
                features['peak_memory_usage'] = memory_stats.get('peak_memory_mb', 0)
            
            features['execution_time'] = analysis_result.duration_seconds
            
            return features
            
        except Exception as e:
            self.logger.error(f"Resource feature extraction failed: {e}")
            return features
