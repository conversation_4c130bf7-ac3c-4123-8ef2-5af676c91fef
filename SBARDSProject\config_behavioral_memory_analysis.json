{"behavioral_analysis": {"enabled": true, "real_time_analysis": true, "ml_enabled": false, "threat_scoring": true, "ioc_extraction": true, "resource_monitoring": {"enabled": true, "monitoring_interval_seconds": 1, "history_retention_minutes": 60, "thresholds": {"cpu_usage_percent": 80.0, "memory_usage_mb": 2048.0, "io_rate_mbps": 100.0, "network_rate_mbps": 50.0, "memory_leak_mb": 100.0, "thread_count": 50, "handle_count": 10000}, "anomaly_detection": {"enabled": true, "statistical_analysis": true, "baseline_learning_hours": 24, "deviation_threshold": 2.0}}, "pattern_detection": {"enabled": true, "confidence_threshold": 0.5, "correlation_window_minutes": 5, "max_patterns_per_process": 100, "ransomware_detection": {"enabled": true, "file_encryption_threshold": 10, "shadow_copy_monitoring": true, "backup_deletion_detection": true, "crypto_api_monitoring": true, "bulk_file_operations": true, "extension_change_detection": true, "ransom_note_detection": true, "encryption_rate_threshold": 5.0, "suspicious_extensions": [".encrypted", ".locked", ".crypto", ".crypt", ".enc", ".vault", ".exx", ".ezz", ".ecc", ".abc", ".xyz", ".aaa", ".micro", ".ttt", ".xxx", ".zzz"]}, "apt_detection": {"enabled": true, "lateral_movement": true, "persistence_mechanisms": true, "data_exfiltration": true, "c2_communication": true, "process_injection": true, "credential_harvesting": true, "living_off_land": true, "steganography_detection": false, "network_connection_threshold": 5, "data_exfiltration_threshold_mb": 100}, "banking_trojan_detection": {"enabled": true, "browser_injection": true, "form_grabbing": true, "certificate_theft": true, "keylogging": true, "screen_capture": true, "financial_data_theft": true, "web_inject_detection": true, "banking_domains": ["bank", "paypal", "visa", "mastercard", "amex", "chase", "wellsfargo", "bankofamerica", "citibank"]}, "process_injection_detection": {"enabled": true, "dll_injection": true, "process_hollowing": true, "atom_bombing": true, "manual_dll_loading": true, "reflective_dll_loading": true, "thread_hijacking": true, "process_doppelganging": true, "ghostwriting": true, "injection_confidence_threshold": 0.7}, "stealth_techniques": {"enabled": true, "rootkit_behavior": true, "anti_debugging": true, "vm_evasion": true, "sandbox_evasion": true, "packing_obfuscation": true, "code_injection": true, "api_hooking": true, "direct_syscalls": true, "entropy_threshold": 7.0}}, "process_analysis": {"enabled": true, "monitor_child_processes": true, "monitor_process_chains": true, "max_process_chain_depth": 10, "suspicious_process_names": ["cmd.exe", "powershell.exe", "rundll32.exe", "regsvr32.exe", "mshta.exe", "wscript.exe", "cscript.exe", "bitsadmin.exe", "certutil.exe", "schtasks.exe", "at.exe", "sc.exe"], "process_creation_monitoring": true, "command_line_analysis": true, "parent_child_correlation": true}, "service_monitoring": {"enabled": true, "monitor_installations": true, "monitor_modifications": true, "monitor_startups": true, "critical_services": ["Windows Defender", "Windows Security", "Antimalware Service", "Windows Firewall", "Security Center", "WinDefend"], "service_creation_alerts": true, "service_modification_alerts": true}, "network_behavior": {"enabled": true, "connection_monitoring": true, "data_transfer_analysis": true, "c2_detection": true, "dns_analysis": true, "http_analysis": true, "encrypted_traffic_analysis": true, "beacon_detection": {"enabled": true, "interval_analysis": true, "jitter_analysis": true, "size_analysis": true, "frequency_threshold": 10}, "suspicious_domains": ["*.bit", "*.onion", "tempuri.org", "example.com", "*.tk", "*.ml", "*.ga", "*.cf", "*.pw", "duckdns.org", "no-ip.com", "ddns.net"], "suspicious_ports": [4444, 5555, 6666, 7777, 8080, 9999, 1337, 31337, 6667, 6697, 1234, 12345, 54321, 8888, 9090]}}, "memory_analysis": {"enabled": true, "automatic_dumps": false, "dump_interval_minutes": 30, "dump_on_suspicious_activity": true, "max_dump_size_mb": 2048, "compress_dumps": true, "encrypt_dumps": false, "injection_detection": {"enabled": true, "dll_injection": true, "process_hollowing": true, "code_injection": true, "thread_injection": true, "atom_bombing": true, "manual_dll_loading": true, "reflective_dll_loading": true, "confidence_threshold": 0.7, "deep_scan": true}, "stealth_detection": {"enabled": true, "heap_spraying": true, "rop_jop_chains": true, "anti_debugging": true, "packing_obfuscation": true, "rootkit_techniques": true, "api_hooking": true, "direct_syscalls": true, "entropy_analysis": true, "entropy_threshold": 7.0}, "forensic_analysis": {"enabled": true, "extract_strings": true, "extract_encryption_keys": true, "extract_certificates": true, "extract_network_artifacts": true, "extract_file_artifacts": true, "heap_analysis": true, "stack_analysis": true, "min_string_length": 4, "max_string_length": 1024, "unicode_strings": true, "ascii_strings": true}, "volatility_integration": {"enabled": false, "volatility_path": "/usr/local/bin/vol.py", "volatility_profile": "Win10x64_19041", "timeout_seconds": 300, "plugins": ["pslist", "psscan", "dlllist", "handles", "cmdline", "netscan", "malfind", "hollowfind", "apihooks", "ldrmodules", "modscan", "driverscan", "filescan", "mutantscan", "symlinkscan", "thrdscan", "unloadedmodules"], "custom_plugins": [], "output_format": "text", "parallel_execution": false}, "dump_management": {"dump_directory": "memory_dumps", "max_dumps_per_process": 5, "retention_days": 7, "auto_cleanup": true, "naming_convention": "process_{pid}_{timestamp}.dmp", "metadata_files": true, "hash_verification": true}}, "correlation_engine": {"enabled": true, "real_time_correlation": true, "correlation_window_minutes": 10, "cross_process_correlation": true, "temporal_analysis": true, "statistical_correlation": true, "threat_models": {"ransomware": {"required_indicators": 2, "indicators": ["potential_file_encryption", "shadow_copy_deletion", "security_tool_interference", "backup_deletion", "crypto_api_usage", "bulk_file_operations"], "confidence_boost": 0.2, "alert_threshold": 0.8}, "apt": {"required_indicators": 2, "indicators": ["lateral_movement", "data_exfiltration", "persistence_mechanisms", "c2_communication", "process_injection", "credential_harvesting"], "confidence_boost": 0.15, "alert_threshold": 0.75}, "banking_trojan": {"required_indicators": 2, "indicators": ["browser_injection", "form_grabbing", "certificate_theft", "keylogging", "financial_data_access", "web_inject"], "confidence_boost": 0.1, "alert_threshold": 0.7}}, "machine_learning": {"enabled": false, "model_path": "models/behavioral_correlation.pkl", "feature_extraction": true, "anomaly_detection": true, "classification": true, "clustering": true, "online_learning": false, "model_update_interval_hours": 24}}, "performance": {"max_concurrent_analyses": 4, "analysis_queue_size": 100, "event_buffer_size": 10000, "monitoring_interval_ms": 1000, "cleanup_interval_seconds": 300, "memory_limit_mb": 4096, "cpu_limit_percent": 70, "optimization": {"enable_caching": true, "cache_size_mb": 512, "compress_events": true, "batch_processing": true, "async_io": true, "parallel_analysis": true, "lazy_loading": true}, "resource_limits": {"max_memory_mb": 4096, "max_cpu_percent": 70, "max_disk_io_mbps": 100, "max_network_io_mbps": 50, "max_open_files": 1000, "max_threads": 50}}, "security": {"encryption": {"enabled": true, "algorithm": "AES-256-GCM", "key_rotation_hours": 24, "encrypt_dumps": false, "encrypt_logs": true, "encrypt_communications": true}, "access_control": {"enabled": true, "require_authentication": true, "role_based_access": true, "audit_logging": true, "session_timeout_minutes": 60, "max_failed_attempts": 3}, "isolation": {"process_isolation": true, "memory_isolation": true, "network_isolation": false, "file_system_isolation": true, "sandbox_analysis": false}, "privacy": {"anonymize_data": false, "data_retention_days": 30, "gdpr_compliance": true, "data_minimization": true, "consent_required": false}}, "alerting": {"enabled": true, "real_time_alerts": true, "alert_aggregation": true, "alert_correlation": true, "severity_levels": ["low", "medium", "high", "critical"], "notification_channels": {"email": {"enabled": false, "smtp_server": "", "smtp_port": 587, "username": "", "password": "", "recipients": []}, "webhook": {"enabled": false, "url": "", "headers": {}, "timeout_seconds": 30}, "syslog": {"enabled": true, "server": "localhost", "port": 514, "facility": "local0"}}, "alert_rules": {"ransomware_activity": {"severity": "critical", "immediate_notification": true, "auto_response": false}, "apt_activity": {"severity": "high", "immediate_notification": true, "auto_response": false}, "memory_injection": {"severity": "high", "immediate_notification": false, "auto_response": false}, "high_resource_usage": {"severity": "medium", "immediate_notification": false, "auto_response": false}}}, "logging": {"level": "INFO", "file_path": "logs/behavioral_memory_analysis.log", "max_file_size_mb": 100, "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "enable_console": true, "enable_file": true, "enable_syslog": false, "structured_logging": true, "log_rotation": true}, "output": {"format": "json", "compression": true, "encryption": false, "retention_days": 30, "export_formats": ["json", "csv", "xml", "yaml"], "real_time_streaming": false, "batch_export": true, "export_schedule": "daily"}, "integration": {"siem_integration": {"enabled": false, "siem_type": "splunk", "endpoint": "", "api_key": "", "index": "sbards_behavioral"}, "threat_intelligence": {"enabled": false, "feeds": [], "update_interval_hours": 6, "ioc_matching": true, "reputation_checking": true}, "sandbox_integration": {"enabled": false, "sandbox_type": "cuckoo", "endpoint": "http://localhost:8090", "api_key": "", "auto_submit": false}}}