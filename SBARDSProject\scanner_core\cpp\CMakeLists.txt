# SBARDS Advanced Dynamic Analysis Layer - C++ Components
# CMake configuration for high-performance analysis components

cmake_minimum_required(VERSION 3.16)
project(SBARDS_DynamicAnalysis VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Compiler-specific options
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4 /O2 /std:c++17")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi /DEBUG")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2 -std=c++17")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0 -DDEBUG")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")
endif()

# Platform detection
if(WIN32)
    add_definitions(-DWIN32_LEAN_AND_MEAN -DNOMINMAX)
    set(PLATFORM_LIBS ws2_32 iphlpapi shlwapi dbghelp psapi)
elseif(UNIX AND NOT APPLE)
    set(PLATFORM_LIBS pthread dl)
elseif(APPLE)
    set(PLATFORM_LIBS pthread dl)
endif()

# Find required packages
find_package(Threads REQUIRED)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files for Sandbox Engine
set(SANDBOX_ENGINE_SOURCES
    sandbox_engine.cpp
    sandbox_engine.hpp
    network_isolator.cpp
    filesystem_virtualizer.cpp
    memory_isolator.cpp
)

# Source files for API Hooking
set(API_HOOKING_SOURCES
    api_hooking.cpp
    api_hooking.hpp
    system_call_monitor.cpp
    windows_api_monitor.cpp
)

# Source files for Memory Analyzer
set(MEMORY_ANALYZER_SOURCES
    memory_analyzer.cpp
    memory_analyzer.hpp
    volatility_analyzer.cpp
    injection_detector.cpp
)

# Source files for Response Engine
set(RESPONSE_ENGINE_SOURCES
    response_engine.cpp
    response_engine.hpp
    isolation_manager.cpp
    isolation_manager.hpp
    notification_system.cpp
    notification_system.hpp
    quarantine_manager.cpp
    quarantine_manager.hpp
    permission_manager.cpp
    permission_manager.hpp
    honeypot_manager.cpp
    honeypot_manager.hpp
    recovery_system.cpp
    recovery_system.hpp
    response_bridge.cpp
    response_bridge.hpp
)

# Source files for Performance Monitor
set(PERFORMANCE_MONITOR_SOURCES
    performance_monitor.cpp
    performance_monitor.hpp
    resource_monitor.cpp
    network_monitor.cpp
)

# Source files for User Simulation
set(USER_SIMULATION_SOURCES
    user_simulation.cpp
    user_simulation.hpp
    application_installer.cpp
    interaction_simulator.cpp
)

# Common utility sources
set(COMMON_SOURCES
    utils/logger.cpp
    utils/config_parser.cpp
    utils/crypto_utils.cpp
    utils/string_utils.cpp
    utils/file_utils.cpp
)

# Create static libraries for each component
add_library(sbards_sandbox_engine STATIC ${SANDBOX_ENGINE_SOURCES})
add_library(sbards_api_hooking STATIC ${API_HOOKING_SOURCES})
add_library(sbards_memory_analyzer STATIC ${MEMORY_ANALYZER_SOURCES})
add_library(sbards_response_engine STATIC ${RESPONSE_ENGINE_SOURCES})
add_library(sbards_performance_monitor STATIC ${PERFORMANCE_MONITOR_SOURCES})
add_library(sbards_user_simulation STATIC ${USER_SIMULATION_SOURCES})
add_library(sbards_common STATIC ${COMMON_SOURCES})

# Link libraries
target_link_libraries(sbards_sandbox_engine
    ${PLATFORM_LIBS}
    Threads::Threads
    sbards_common
)

target_link_libraries(sbards_api_hooking
    ${PLATFORM_LIBS}
    Threads::Threads
    sbards_common
)

target_link_libraries(sbards_memory_analyzer
    ${PLATFORM_LIBS}
    Threads::Threads
    sbards_common
)

target_link_libraries(sbards_response_engine
    ${PLATFORM_LIBS}
    Threads::Threads
    sbards_common
)

target_link_libraries(sbards_performance_monitor
    ${PLATFORM_LIBS}
    Threads::Threads
    sbards_common
)

target_link_libraries(sbards_user_simulation
    ${PLATFORM_LIBS}
    Threads::Threads
    sbards_common
)

# Windows-specific libraries
if(WIN32)
    # Microsoft Detours for API hooking
    find_library(DETOURS_LIB detours PATHS "C:/Program Files/Microsoft Research/Detours/lib.X64")
    if(DETOURS_LIB)
        target_link_libraries(sbards_api_hooking ${DETOURS_LIB})
        target_include_directories(sbards_api_hooking PRIVATE "C:/Program Files/Microsoft Research/Detours/include")
    endif()

    # Windows-specific libraries
    target_link_libraries(sbards_sandbox_engine advapi32 kernel32 user32)
    target_link_libraries(sbards_api_hooking advapi32 kernel32 user32 ntdll)
    target_link_libraries(sbards_memory_analyzer advapi32 kernel32 user32 ntdll)
    target_link_libraries(sbards_response_engine advapi32 kernel32 user32 ntdll wtsapi32 netapi32)
    target_link_libraries(sbards_performance_monitor advapi32 kernel32 user32 pdh)
    target_link_libraries(sbards_user_simulation advapi32 kernel32 user32 gdi32)
endif()

# Linux-specific libraries
if(UNIX AND NOT APPLE)
    target_link_libraries(sbards_api_hooking cap)
    target_link_libraries(sbards_memory_analyzer proc)
    target_link_libraries(sbards_response_engine cap proc)
    target_link_libraries(sbards_performance_monitor proc)
endif()

# Create main dynamic analysis executable
add_executable(sbards_dynamic_analyzer
    main.cpp
    dynamic_analysis_orchestrator.cpp
)

target_link_libraries(sbards_dynamic_analyzer
    sbards_sandbox_engine
    sbards_api_hooking
    sbards_memory_analyzer
    sbards_response_engine
    sbards_performance_monitor
    sbards_user_simulation
    sbards_common
    ${PLATFORM_LIBS}
    Threads::Threads
)

# Python integration library
add_library(sbards_python_bridge SHARED
    python_bridge.cpp
    python_bridge.hpp
)

# Find Python
find_package(Python3 COMPONENTS Interpreter Development REQUIRED)
target_include_directories(sbards_python_bridge PRIVATE ${Python3_INCLUDE_DIRS})
target_link_libraries(sbards_python_bridge
    ${Python3_LIBRARIES}
    sbards_sandbox_engine
    sbards_api_hooking
    sbards_memory_analyzer
    sbards_response_engine
    sbards_performance_monitor
    sbards_user_simulation
    sbards_common
)

# Set output directories
set_target_properties(sbards_dynamic_analyzer PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

set_target_properties(sbards_python_bridge PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

# Install targets
install(TARGETS sbards_dynamic_analyzer
    RUNTIME DESTINATION bin
)

install(TARGETS sbards_python_bridge
    LIBRARY DESTINATION lib
)

install(FILES
    sandbox_engine.hpp
    api_hooking.hpp
    memory_analyzer.hpp
    performance_monitor.hpp
    user_simulation.hpp
    DESTINATION include/sbards
)

# Testing
enable_testing()

# Unit tests
add_executable(test_sandbox_engine tests/test_sandbox_engine.cpp)
target_link_libraries(test_sandbox_engine sbards_sandbox_engine sbards_common)
add_test(NAME SandboxEngineTest COMMAND test_sandbox_engine)

add_executable(test_api_hooking tests/test_api_hooking.cpp)
target_link_libraries(test_api_hooking sbards_api_hooking sbards_common)
add_test(NAME APIHookingTest COMMAND test_api_hooking)

add_executable(test_memory_analyzer tests/test_memory_analyzer.cpp)
target_link_libraries(test_memory_analyzer sbards_memory_analyzer sbards_common)
add_test(NAME MemoryAnalyzerTest COMMAND test_memory_analyzer)

# Performance tests
add_executable(perf_test_sandbox tests/perf_test_sandbox.cpp)
target_link_libraries(perf_test_sandbox sbards_sandbox_engine sbards_common)

add_executable(perf_test_memory tests/perf_test_memory.cpp)
target_link_libraries(perf_test_memory sbards_memory_analyzer sbards_common)

# Documentation
find_package(Doxygen)
if(DOXYGEN_FOUND)
    set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in)
    set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)

    configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)

    add_custom_target(doc_doxygen ALL
        COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Generating API documentation with Doxygen"
        VERBATIM
    )
endif()

# Package configuration
set(CPACK_PROJECT_NAME ${PROJECT_NAME})
set(CPACK_PROJECT_VERSION ${PROJECT_VERSION})
include(CPack)

# Custom targets for development
add_custom_target(format
    COMMAND clang-format -i -style=Google *.cpp *.hpp
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT "Formatting source code"
)

add_custom_target(static-analysis
    COMMAND cppcheck --enable=all --std=c++17 *.cpp *.hpp
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT "Running static analysis"
)

# Build configuration summary
message(STATUS "")
message(STATUS "SBARDS Dynamic Analysis Layer Configuration:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "  Python version: ${Python3_VERSION}")
message(STATUS "")
