#include "isolation_manager.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <random>
#include <iomanip>
#include <filesystem>
#include <algorithm>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #include <netfw.h>
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <ifaddrs.h>
    #include <net/if.h>
#endif

namespace fs = std::filesystem;

IsolationManager::IsolationManager(const ResponseConfig& config)
    : config_(config), running_(false) {

    // Initialize statistics
    isolation_statistics_["total_isolations"] = 0;
    isolation_statistics_["active_sessions"] = 0;
    isolation_statistics_["processes_isolated"] = 0;
    isolation_statistics_["network_rules_created"] = 0;
    isolation_statistics_["system_lockdowns"] = 0;
    isolation_statistics_["monitoring_sessions"] = 0;
}

IsolationManager::~IsolationManager() {
    shutdown();
}

bool IsolationManager::initialize() {
    try {
        std::cout << "[IsolationManager] Initializing Isolation Manager..." << std::endl;

        // Create isolation directories
        fs::path isolation_base = fs::path(config_.base_directory) / "isolation";
        if (!fs::exists(isolation_base)) {
            fs::create_directories(isolation_base);
        }

        // Initialize platform-specific isolation
#ifdef _WIN32
        if (!initialize_windows_isolation()) {
            std::cerr << "[IsolationManager] Failed to initialize Windows isolation" << std::endl;
            return false;
        }
#else
        if (!initialize_linux_isolation()) {
            std::cerr << "[IsolationManager] Failed to initialize Linux isolation" << std::endl;
            return false;
        }
#endif

        // Start monitoring threads
        running_ = true;
        process_monitor_thread_ = std::thread(&IsolationManager::process_monitoring_loop, this);
        network_monitor_thread_ = std::thread(&IsolationManager::network_monitoring_loop, this);

        std::cout << "[IsolationManager] Isolation Manager initialized successfully" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[IsolationManager] Initialization error: " << e.what() << std::endl;
        return false;
    }
}

void IsolationManager::shutdown() {
    if (running_) {
        std::cout << "[IsolationManager] Shutting down Isolation Manager..." << std::endl;

        // Stop monitoring threads
        running_ = false;

        if (process_monitor_thread_.joinable()) {
            process_monitor_thread_.join();
        }

        if (network_monitor_thread_.joinable()) {
            network_monitor_thread_.join();
        }

        // Terminate all active sessions
        std::lock_guard<std::mutex> lock(sessions_mutex_);
        for (auto& session_pair : active_sessions_) {
            terminate_isolation_session(session_pair.first);
        }
        active_sessions_.clear();

        // Cleanup platform-specific resources
#ifdef _WIN32
        cleanup_windows_isolation();
#else
        cleanup_linux_isolation();
#endif

        std::cout << "[IsolationManager] Isolation Manager shutdown complete" << std::endl;
    }
}

bool IsolationManager::is_running() const {
    return running_;
}

ResponseResult IsolationManager::setup_light_monitoring(const std::string& file_path,
                                                       const std::map<std::string, std::string>& metadata) {
    ResponseResult result;
    result.success = true;
    result.execution_timestamp = std::chrono::system_clock::now();

    try {
        std::cout << "[IsolationManager] Setting up light monitoring for: " << file_path << std::endl;

        // Generate session ID
        std::string session_id = generate_session_id();

        // Create monitoring session
        IsolationSession session;
        session.session_id = session_id;
        session.file_path = file_path;
        session.isolation_type = IsolationType::LIGHT_MONITORING;
        session.start_time = std::chrono::system_clock::now();
        session.active = true;
        session.metadata = metadata;

        // Find processes associated with the file
        auto processes = find_processes_by_file(file_path);
        session.isolated_processes = processes;

        // Add processes to monitoring
        {
            std::lock_guard<std::mutex> lock(processes_mutex_);
            for (uint32_t pid : processes) {
                ProcessMonitorInfo monitor_info;
                monitor_info.process_id = pid;
                monitor_info.process_name = get_process_executable_path(pid);
                monitor_info.executable_path = file_path;
                monitor_info.start_time = std::chrono::system_clock::now();
                monitor_info.is_isolated = false;

                monitored_processes_[pid] = monitor_info;
            }
        }

        // Store session
        {
            std::lock_guard<std::mutex> lock(sessions_mutex_);
            active_sessions_[session_id] = session;
        }

        result.isolation_id = session_id;
        result.actions_taken.push_back(ResponseAction::DEEP_ANALYSIS);

        update_statistics("monitoring_sessions");
        update_statistics("active_sessions");

        log_isolation_event("LIGHT_MONITORING_STARTED", session_id, file_path);

        std::cout << "[IsolationManager] Light monitoring setup completed: " << session_id << std::endl;

    } catch (const std::exception& e) {
        result.success = false;
        result.error_message = std::string("Light monitoring setup error: ") + e.what();
    }

    return result;
}

ResponseResult IsolationManager::setup_deep_monitoring(const std::string& file_path,
                                                      const std::map<std::string, std::string>& metadata) {
    ResponseResult result;
    result.success = true;
    result.execution_timestamp = std::chrono::system_clock::now();

    try {
        std::cout << "[IsolationManager] Setting up deep monitoring for: " << file_path << std::endl;

        // Generate session ID
        std::string session_id = generate_session_id();

        // Create monitoring session
        IsolationSession session;
        session.session_id = session_id;
        session.file_path = file_path;
        session.isolation_type = IsolationType::DEEP_MONITORING;
        session.start_time = std::chrono::system_clock::now();
        session.active = true;
        session.metadata = metadata;

        // Find processes associated with the file
        auto processes = find_processes_by_file(file_path);
        session.isolated_processes = processes;

        // Create isolated directory for monitoring
        std::string isolated_path;
        if (create_isolated_directory(session_id, isolated_path)) {
            session.isolation_directory = isolated_path;
        }

        // Add processes to deep monitoring
        {
            std::lock_guard<std::mutex> lock(processes_mutex_);
            for (uint32_t pid : processes) {
                ProcessMonitorInfo monitor_info;
                monitor_info.process_id = pid;
                monitor_info.process_name = get_process_executable_path(pid);
                monitor_info.executable_path = file_path;
                monitor_info.start_time = std::chrono::system_clock::now();
                monitor_info.is_isolated = false;
                monitor_info.network_connections = get_process_network_connections(pid);
                monitor_info.file_accesses = get_process_file_accesses(pid);

                monitored_processes_[pid] = monitor_info;
            }
        }

        // Store session
        {
            std::lock_guard<std::mutex> lock(sessions_mutex_);
            active_sessions_[session_id] = session;
        }

        result.isolation_id = session_id;
        result.actions_taken.push_back(ResponseAction::DEEP_ANALYSIS);

        update_statistics("monitoring_sessions");
        update_statistics("active_sessions");

        log_isolation_event("DEEP_MONITORING_STARTED", session_id, file_path);

        std::cout << "[IsolationManager] Deep monitoring setup completed: " << session_id << std::endl;

    } catch (const std::exception& e) {
        result.success = false;
        result.error_message = std::string("Deep monitoring setup error: ") + e.what();
    }

    return result;
}

ResponseResult IsolationManager::isolate_process(const std::string& file_path,
                                                const std::map<std::string, std::string>& metadata) {
    ResponseResult result;
    result.success = true;
    result.execution_timestamp = std::chrono::system_clock::now();

    try {
        std::cout << "[IsolationManager] Isolating processes for: " << file_path << std::endl;

        // Generate session ID
        std::string session_id = generate_session_id();

        // Create isolation session
        IsolationSession session;
        session.session_id = session_id;
        session.file_path = file_path;
        session.isolation_type = IsolationType::PROCESS_ISOLATION;
        session.start_time = std::chrono::system_clock::now();
        session.active = true;
        session.metadata = metadata;

        // Find processes associated with the file
        auto processes = find_processes_by_file(file_path);
        session.isolated_processes = processes;

        // Create isolated directory
        std::string isolated_path;
        if (create_isolated_directory(session_id, isolated_path)) {
            session.isolation_directory = isolated_path;
        }

        // Isolate each process
        bool isolation_success = true;
        for (uint32_t pid : processes) {
            if (!isolate_process_tree(pid, session_id)) {
                isolation_success = false;
                std::cerr << "[IsolationManager] Failed to isolate process: " << pid << std::endl;
            } else {
                std::cout << "[IsolationManager] Successfully isolated process: " << pid << std::endl;
            }
        }

        if (!isolation_success) {
            result.success = false;
            result.error_message = "Failed to isolate some processes";
        }

        // Store session
        {
            std::lock_guard<std::mutex> lock(sessions_mutex_);
            active_sessions_[session_id] = session;
        }

        result.isolation_id = session_id;
        result.actions_taken.push_back(ResponseAction::ISOLATE);

        update_statistics("total_isolations");
        update_statistics("processes_isolated", processes.size());
        update_statistics("active_sessions");

        log_isolation_event("PROCESS_ISOLATION_STARTED", session_id, file_path);

        std::cout << "[IsolationManager] Process isolation completed: " << session_id << std::endl;

    } catch (const std::exception& e) {
        result.success = false;
        result.error_message = std::string("Process isolation error: ") + e.what();
    }

    return result;
}

ResponseResult IsolationManager::terminate_process(const std::string& file_path,
                                                  const std::map<std::string, std::string>& metadata) {
    ResponseResult result;
    result.success = true;
    result.execution_timestamp = std::chrono::system_clock::now();

    try {
        std::cout << "[IsolationManager] Terminating processes for: " << file_path << std::endl;

        // Find processes associated with the file
        auto processes = find_processes_by_file(file_path);

        // Terminate each process
        bool termination_success = true;
        for (uint32_t pid : processes) {
            if (!terminate_process_tree(pid)) {
                termination_success = false;
                std::cerr << "[IsolationManager] Failed to terminate process: " << pid << std::endl;
            } else {
                std::cout << "[IsolationManager] Successfully terminated process: " << pid << std::endl;
            }
        }

        if (!termination_success) {
            result.success = false;
            result.error_message = "Failed to terminate some processes";
        }

        result.actions_taken.push_back(ResponseAction::TERMINATE);

        log_isolation_event("PROCESS_TERMINATION", "N/A", file_path);

        std::cout << "[IsolationManager] Process termination completed" << std::endl;

    } catch (const std::exception& e) {
        result.success = false;
        result.error_message = std::string("Process termination error: ") + e.what();
    }

    return result;
}

ResponseResult IsolationManager::terminate_all_related_processes(const std::string& file_path,
                                                               const std::map<std::string, std::string>& metadata) {
    ResponseResult result;
    result.success = true;
    result.execution_timestamp = std::chrono::system_clock::now();

    try {
        std::cout << "[IsolationManager] Terminating all related processes for: " << file_path << std::endl;

        // Find all processes associated with the file
        auto processes = find_processes_by_file(file_path);

        // Find all child processes
        std::vector<uint32_t> all_processes = processes;
        for (uint32_t pid : processes) {
            auto children = find_child_processes(pid);
            all_processes.insert(all_processes.end(), children.begin(), children.end());
        }

        // Remove duplicates
        std::sort(all_processes.begin(), all_processes.end());
        all_processes.erase(std::unique(all_processes.begin(), all_processes.end()), all_processes.end());

        // Terminate all processes
        bool termination_success = true;
        for (uint32_t pid : all_processes) {
            if (!terminate_process_tree(pid)) {
                termination_success = false;
                std::cerr << "[IsolationManager] Failed to terminate process tree: " << pid << std::endl;
            } else {
                std::cout << "[IsolationManager] Successfully terminated process tree: " << pid << std::endl;
            }
        }

        if (!termination_success) {
            result.success = false;
            result.error_message = "Failed to terminate some process trees";
        }

        result.actions_taken.push_back(ResponseAction::PROCESS_TERMINATION);

        log_isolation_event("ALL_PROCESSES_TERMINATION", "N/A", file_path);

        std::cout << "[IsolationManager] All related processes termination completed" << std::endl;

    } catch (const std::exception& e) {
        result.success = false;
        result.error_message = std::string("All processes termination error: ") + e.what();
    }

    return result;
}

ResponseResult IsolationManager::isolate_network(const std::string& file_path,
                                                const std::map<std::string, std::string>& metadata) {
    ResponseResult result;
    result.success = true;
    result.execution_timestamp = std::chrono::system_clock::now();

    try {
        std::cout << "[IsolationManager] Setting up network isolation for: " << file_path << std::endl;

        // Generate session ID
        std::string session_id = generate_session_id();

        // Find processes associated with the file
        auto processes = find_processes_by_file(file_path);

        // Create network isolation rules for each process
        std::vector<std::string> blocked_connections;
        for (uint32_t pid : processes) {
            auto connections = get_process_network_connections(pid);
            for (const auto& connection : connections) {
                // Parse connection string and create blocking rule
                std::string rule_id = session_id + "_" + std::to_string(pid) + "_" + connection;

                NetworkIsolationRule rule;
                rule.rule_id = rule_id;
                rule.source_ip = "0.0.0.0"; // Block all sources
                rule.destination_ip = "0.0.0.0"; // Block all destinations
                rule.port = 0; // Block all ports
                rule.protocol = "ALL";
                rule.blocked = true;
                rule.created_time = std::chrono::system_clock::now();

                if (create_network_isolation_rule(rule_id, rule.source_ip, rule.destination_ip, rule.port, rule.protocol)) {
                    std::lock_guard<std::mutex> lock(network_mutex_);
                    network_rules_.push_back(rule);
                    blocked_connections.push_back(connection);
                }
            }
        }

        result.isolation_id = session_id;
        result.actions_taken.push_back(ResponseAction::NETWORK_ISOLATION);

        update_statistics("network_rules_created", blocked_connections.size());

        log_isolation_event("NETWORK_ISOLATION", session_id, file_path);

        std::cout << "[IsolationManager] Network isolation completed: " << session_id << std::endl;

    } catch (const std::exception& e) {
        result.success = false;
        result.error_message = std::string("Network isolation error: ") + e.what();
    }

    return result;
}