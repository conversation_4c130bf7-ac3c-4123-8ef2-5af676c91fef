"""
SBARDS Sandbox Orchestrator
Advanced multi-environment sandbox management and orchestration

Features:
- Docker container sandboxing
- Virtual machine integration
- Hybrid environment management
- Network isolation and simulation
- File system virtualization
- Resource monitoring and control
"""

import os
import asyncio
import logging
import tempfile
import shutil
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set
from pathlib import Path
import docker
import subprocess
import threading
from concurrent.futures import ThreadPoolExecutor
import psutil
import yaml

class SandboxOrchestrator:
    """
    Advanced Sandbox Orchestrator
    
    Manages multiple sandbox environments for dynamic analysis:
    - Docker containers for lightweight analysis
    - Virtual machines for full OS simulation
    - Hybrid environments for comprehensive analysis
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Sandbox Orchestrator
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.SandboxOrchestrator")
        
        # Sandbox configuration
        self.sandbox_config = config.get("dynamic_analysis", {}).get("sandbox", {})
        
        # Environment settings
        self.docker_enabled = self.sandbox_config.get("docker_enabled", True)
        self.vm_enabled = self.sandbox_config.get("vm_enabled", False)
        self.container_image = self.sandbox_config.get("container_image", "sbards/analysis:latest")
        self.vm_snapshots = self.sandbox_config.get("vm_snapshots", {})
        
        # Isolation settings
        self.network_isolation = self.sandbox_config.get("network_isolation", True)
        self.file_isolation = self.sandbox_config.get("file_isolation", True)
        self.memory_isolation = self.sandbox_config.get("memory_isolation", True)
        self.escape_prevention = self.sandbox_config.get("escape_prevention", True)
        
        # Resource limits
        self.max_memory_mb = config.get("dynamic_analysis", {}).get("performance", {}).get("resource_limits", {}).get("max_memory_mb", 2048)
        self.max_cpu_percent = config.get("dynamic_analysis", {}).get("performance", {}).get("resource_limits", {}).get("max_cpu_percent", 50)
        
        # Initialize Docker client
        self.docker_client = None
        if self.docker_enabled:
            self._init_docker_client()
        
        # Active sandboxes
        self.active_sandboxes: Dict[str, Dict[str, Any]] = {}
        self.sandbox_lock = threading.Lock()
        
        # Thread pool for concurrent operations
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # Monitoring
        self.monitoring_active = False
        self.monitoring_thread = None
        
    def _init_docker_client(self):
        """Initialize Docker client"""
        try:
            self.docker_client = docker.from_env()
            # Test connection
            self.docker_client.ping()
            self.logger.info("Docker client initialized successfully")
            
            # Ensure analysis image exists
            self._ensure_analysis_image()
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Docker client: {e}")
            self.docker_client = None
            self.docker_enabled = False
    
    def _ensure_analysis_image(self):
        """Ensure the analysis Docker image exists"""
        try:
            # Check if image exists
            try:
                self.docker_client.images.get(self.container_image)
                self.logger.info(f"Analysis image {self.container_image} found")
                return
            except docker.errors.ImageNotFound:
                pass
            
            # Build or pull the image
            if self._should_build_image():
                self._build_analysis_image()
            else:
                self._pull_analysis_image()
                
        except Exception as e:
            self.logger.error(f"Failed to ensure analysis image: {e}")
    
    def _should_build_image(self) -> bool:
        """Check if we should build the image locally"""
        dockerfile_path = Path(__file__).parent / "docker" / "Dockerfile"
        return dockerfile_path.exists()
    
    def _build_analysis_image(self):
        """Build the analysis Docker image"""
        try:
            dockerfile_dir = Path(__file__).parent / "docker"
            self.logger.info(f"Building analysis image from {dockerfile_dir}")
            
            image, logs = self.docker_client.images.build(
                path=str(dockerfile_dir),
                tag=self.container_image,
                rm=True,
                forcerm=True
            )
            
            self.logger.info(f"Successfully built analysis image: {image.id}")
            
        except Exception as e:
            self.logger.error(f"Failed to build analysis image: {e}")
            raise
    
    def _pull_analysis_image(self):
        """Pull the analysis Docker image"""
        try:
            self.logger.info(f"Pulling analysis image: {self.container_image}")
            image = self.docker_client.images.pull(self.container_image)
            self.logger.info(f"Successfully pulled analysis image: {image.id}")
            
        except Exception as e:
            self.logger.error(f"Failed to pull analysis image: {e}")
            # Fall back to a basic Ubuntu image
            self.container_image = "ubuntu:20.04"
            self.logger.warning(f"Falling back to {self.container_image}")
    
    async def analyze_file_async(self, file_path: str, timeout: int = 300, 
                                enable_network_monitoring: bool = True) -> Dict[str, Any]:
        """
        Perform asynchronous file analysis in sandbox
        
        Args:
            file_path: Path to file to analyze
            timeout: Analysis timeout in seconds
            enable_network_monitoring: Whether to monitor network activity
            
        Returns:
            Analysis results
        """
        analysis_id = self._generate_analysis_id()
        
        try:
            self.logger.info(f"Starting sandbox analysis: {analysis_id} for {file_path}")
            
            # Choose sandbox environment
            sandbox_type = self._choose_sandbox_environment(file_path)
            
            if sandbox_type == "docker":
                result = await self._analyze_in_docker(analysis_id, file_path, timeout, enable_network_monitoring)
            elif sandbox_type == "vm":
                result = await self._analyze_in_vm(analysis_id, file_path, timeout, enable_network_monitoring)
            else:
                result = await self._analyze_in_hybrid(analysis_id, file_path, timeout, enable_network_monitoring)
            
            self.logger.info(f"Sandbox analysis completed: {analysis_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Sandbox analysis failed: {analysis_id} - {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis_id": analysis_id,
                "sandbox_type": sandbox_type,
                "timestamp": datetime.now().isoformat()
            }
    
    def _choose_sandbox_environment(self, file_path: str) -> str:
        """Choose appropriate sandbox environment based on file type"""
        try:
            file_ext = Path(file_path).suffix.lower()
            
            # Windows executables - prefer VM if available
            if file_ext in ['.exe', '.dll', '.scr', '.com', '.bat', '.cmd', '.ps1']:
                if self.vm_enabled and 'windows10' in self.vm_snapshots:
                    return "vm"
                elif self.docker_enabled:
                    return "docker"
            
            # Linux executables - prefer Docker
            elif file_ext in ['.elf', '.so', '.sh']:
                if self.docker_enabled:
                    return "docker"
                elif self.vm_enabled and 'ubuntu20' in self.vm_snapshots:
                    return "vm"
            
            # Documents and scripts - Docker is sufficient
            elif file_ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.js', '.vbs']:
                if self.docker_enabled:
                    return "docker"
            
            # Default to hybrid if both are available
            if self.docker_enabled and self.vm_enabled:
                return "hybrid"
            elif self.docker_enabled:
                return "docker"
            elif self.vm_enabled:
                return "vm"
            else:
                raise Exception("No sandbox environments available")
                
        except Exception as e:
            self.logger.error(f"Failed to choose sandbox environment: {e}")
            return "docker" if self.docker_enabled else "vm"
    
    async def _analyze_in_docker(self, analysis_id: str, file_path: str, 
                                timeout: int, enable_network_monitoring: bool) -> Dict[str, Any]:
        """Analyze file in Docker container"""
        container = None
        temp_dir = None
        
        try:
            # Create temporary directory for analysis
            temp_dir = tempfile.mkdtemp(prefix=f"sbards_analysis_{analysis_id}_")
            
            # Copy file to temp directory
            file_name = Path(file_path).name
            temp_file_path = Path(temp_dir) / file_name
            shutil.copy2(file_path, temp_file_path)
            
            # Create analysis script
            analysis_script = self._create_analysis_script(file_name, enable_network_monitoring)
            script_path = Path(temp_dir) / "analyze.sh"
            with open(script_path, 'w') as f:
                f.write(analysis_script)
            os.chmod(script_path, 0o755)
            
            # Configure container
            container_config = {
                'image': self.container_image,
                'command': f'/analysis/analyze.sh',
                'volumes': {str(temp_dir): {'bind': '/analysis', 'mode': 'rw'}},
                'working_dir': '/analysis',
                'detach': True,
                'remove': False,  # We'll remove manually after getting logs
                'mem_limit': f'{self.max_memory_mb}m',
                'cpu_period': 100000,
                'cpu_quota': int(100000 * self.max_cpu_percent / 100),
                'network_mode': 'none' if self.network_isolation else 'bridge',
                'security_opt': ['no-new-privileges:true'],
                'cap_drop': ['ALL'],
                'cap_add': ['CHOWN', 'DAC_OVERRIDE', 'FOWNER', 'SETGID', 'SETUID'],
                'read_only': False,
                'tmpfs': {'/tmp': 'noexec,nosuid,size=100m'}
            }
            
            # Start container
            self.logger.info(f"Starting Docker container for analysis: {analysis_id}")
            container = self.docker_client.containers.run(**container_config)
            
            # Register active sandbox
            with self.sandbox_lock:
                self.active_sandboxes[analysis_id] = {
                    'type': 'docker',
                    'container': container,
                    'start_time': datetime.now(),
                    'temp_dir': temp_dir,
                    'file_path': file_path
                }
            
            # Wait for completion with timeout
            start_time = time.time()
            while time.time() - start_time < timeout:
                container.reload()
                if container.status in ['exited', 'dead']:
                    break
                await asyncio.sleep(1)
            
            # Stop container if still running
            if container.status == 'running':
                container.stop(timeout=10)
                container.wait(timeout=10)
            
            # Get container logs
            logs = container.logs(stdout=True, stderr=True).decode('utf-8', errors='ignore')
            
            # Get exit code
            exit_code = container.attrs['State']['ExitCode']
            
            # Parse analysis results
            results = self._parse_docker_results(temp_dir, logs, exit_code)
            results.update({
                'success': True,
                'analysis_id': analysis_id,
                'sandbox_type': 'docker',
                'container_id': container.id,
                'execution_time': time.time() - start_time,
                'timestamp': datetime.now().isoformat()
            })
            
            return results
            
        except Exception as e:
            self.logger.error(f"Docker analysis failed: {analysis_id} - {e}")
            return {
                'success': False,
                'error': str(e),
                'analysis_id': analysis_id,
                'sandbox_type': 'docker',
                'timestamp': datetime.now().isoformat()
            }
        
        finally:
            # Cleanup
            try:
                if container:
                    container.remove(force=True)
            except:
                pass
            
            try:
                if temp_dir and os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
            except:
                pass
            
            # Remove from active sandboxes
            with self.sandbox_lock:
                if analysis_id in self.active_sandboxes:
                    del self.active_sandboxes[analysis_id]
    
    async def _analyze_in_vm(self, analysis_id: str, file_path: str, 
                            timeout: int, enable_network_monitoring: bool) -> Dict[str, Any]:
        """Analyze file in virtual machine"""
        # VM analysis implementation would go here
        # This is a placeholder for VM integration
        self.logger.warning("VM analysis not yet implemented")
        return {
            'success': False,
            'error': 'VM analysis not implemented',
            'analysis_id': analysis_id,
            'sandbox_type': 'vm',
            'timestamp': datetime.now().isoformat()
        }
    
    async def _analyze_in_hybrid(self, analysis_id: str, file_path: str, 
                                timeout: int, enable_network_monitoring: bool) -> Dict[str, Any]:
        """Analyze file in hybrid environment (Docker + VM)"""
        try:
            # Run both Docker and VM analysis concurrently
            docker_task = self._analyze_in_docker(f"{analysis_id}_docker", file_path, timeout, enable_network_monitoring)
            vm_task = self._analyze_in_vm(f"{analysis_id}_vm", file_path, timeout, enable_network_monitoring)
            
            docker_result, vm_result = await asyncio.gather(docker_task, vm_task, return_exceptions=True)
            
            # Combine results
            combined_result = {
                'success': True,
                'analysis_id': analysis_id,
                'sandbox_type': 'hybrid',
                'docker_results': docker_result if not isinstance(docker_result, Exception) else {'error': str(docker_result)},
                'vm_results': vm_result if not isinstance(vm_result, Exception) else {'error': str(vm_result)},
                'timestamp': datetime.now().isoformat()
            }
            
            return combined_result
            
        except Exception as e:
            self.logger.error(f"Hybrid analysis failed: {analysis_id} - {e}")
            return {
                'success': False,
                'error': str(e),
                'analysis_id': analysis_id,
                'sandbox_type': 'hybrid',
                'timestamp': datetime.now().isoformat()
            }
    
    def _create_analysis_script(self, file_name: str, enable_network_monitoring: bool) -> str:
        """Create analysis script for container"""
        script = f"""#!/bin/bash
set -e

echo "SBARDS Dynamic Analysis Starting"
echo "File: {file_name}"
echo "Timestamp: $(date)"
echo "================================"

# Create output directory
mkdir -p /analysis/output

# File information
echo "=== FILE INFORMATION ===" > /analysis/output/file_info.txt
file /analysis/{file_name} >> /analysis/output/file_info.txt
ls -la /analysis/{file_name} >> /analysis/output/file_info.txt
md5sum /analysis/{file_name} >> /analysis/output/file_info.txt
sha256sum /analysis/{file_name} >> /analysis/output/file_info.txt

# System information
echo "=== SYSTEM INFORMATION ===" > /analysis/output/system_info.txt
uname -a >> /analysis/output/system_info.txt
cat /proc/version >> /analysis/output/system_info.txt
cat /proc/meminfo >> /analysis/output/system_info.txt
cat /proc/cpuinfo >> /analysis/output/system_info.txt

# Process monitoring
echo "=== INITIAL PROCESSES ===" > /analysis/output/processes_before.txt
ps aux >> /analysis/output/processes_before.txt

# Network monitoring
if [ "{enable_network_monitoring}" = "True" ]; then
    echo "=== NETWORK INFORMATION ===" > /analysis/output/network_info.txt
    netstat -tuln >> /analysis/output/network_info.txt 2>/dev/null || true
    ip addr show >> /analysis/output/network_info.txt 2>/dev/null || true
fi

# Execute the file (with timeout and error handling)
echo "=== EXECUTION RESULTS ===" > /analysis/output/execution.txt
echo "Starting execution at $(date)" >> /analysis/output/execution.txt

# Make file executable if it's a binary
chmod +x /analysis/{file_name} 2>/dev/null || true

# Try to execute the file
timeout 60 /analysis/{file_name} >> /analysis/output/execution.txt 2>&1 || {{
    echo "Execution completed with exit code: $?" >> /analysis/output/execution.txt
}}

echo "Execution finished at $(date)" >> /analysis/output/execution.txt

# Post-execution monitoring
echo "=== FINAL PROCESSES ===" > /analysis/output/processes_after.txt
ps aux >> /analysis/output/processes_after.txt

# File system changes
echo "=== FILE SYSTEM CHANGES ===" > /analysis/output/filesystem_changes.txt
find /tmp -type f -newer /analysis/{file_name} 2>/dev/null >> /analysis/output/filesystem_changes.txt || true
find /var/tmp -type f -newer /analysis/{file_name} 2>/dev/null >> /analysis/output/filesystem_changes.txt || true

echo "Analysis completed at $(date)"
echo "Results saved to /analysis/output/"
"""
        return script
    
    def _parse_docker_results(self, temp_dir: str, logs: str, exit_code: int) -> Dict[str, Any]:
        """Parse Docker analysis results"""
        results = {
            'exit_code': exit_code,
            'logs': logs,
            'file_info': {},
            'system_info': {},
            'processes': {'before': [], 'after': []},
            'network_info': {},
            'execution_output': '',
            'filesystem_changes': []
        }
        
        try:
            output_dir = Path(temp_dir) / 'output'
            
            # Parse file info
            file_info_path = output_dir / 'file_info.txt'
            if file_info_path.exists():
                results['file_info'] = file_info_path.read_text()
            
            # Parse system info
            system_info_path = output_dir / 'system_info.txt'
            if system_info_path.exists():
                results['system_info'] = system_info_path.read_text()
            
            # Parse execution output
            execution_path = output_dir / 'execution.txt'
            if execution_path.exists():
                results['execution_output'] = execution_path.read_text()
            
            # Parse process lists
            processes_before_path = output_dir / 'processes_before.txt'
            if processes_before_path.exists():
                results['processes']['before'] = processes_before_path.read_text().split('\n')
            
            processes_after_path = output_dir / 'processes_after.txt'
            if processes_after_path.exists():
                results['processes']['after'] = processes_after_path.read_text().split('\n')
            
            # Parse network info
            network_info_path = output_dir / 'network_info.txt'
            if network_info_path.exists():
                results['network_info'] = network_info_path.read_text()
            
            # Parse filesystem changes
            fs_changes_path = output_dir / 'filesystem_changes.txt'
            if fs_changes_path.exists():
                results['filesystem_changes'] = fs_changes_path.read_text().split('\n')
            
        except Exception as e:
            self.logger.error(f"Failed to parse Docker results: {e}")
        
        return results
    
    def _generate_analysis_id(self) -> str:
        """Generate unique analysis ID"""
        import uuid
        return f"analysis_{int(time.time())}_{uuid.uuid4().hex[:8]}"
    
    def get_active_analyses(self) -> Dict[str, Dict[str, Any]]:
        """Get information about active analyses"""
        with self.sandbox_lock:
            return dict(self.active_sandboxes)
    
    def stop_analysis(self, analysis_id: str) -> bool:
        """Stop a running analysis"""
        try:
            with self.sandbox_lock:
                if analysis_id not in self.active_sandboxes:
                    return False
                
                sandbox_info = self.active_sandboxes[analysis_id]
                
                if sandbox_info['type'] == 'docker':
                    container = sandbox_info['container']
                    container.stop(timeout=10)
                    container.remove(force=True)
                
                del self.active_sandboxes[analysis_id]
                
            self.logger.info(f"Stopped analysis: {analysis_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop analysis {analysis_id}: {e}")
            return False
    
    def cleanup_all_analyses(self):
        """Stop and cleanup all active analyses"""
        with self.sandbox_lock:
            for analysis_id in list(self.active_sandboxes.keys()):
                self.stop_analysis(analysis_id)
