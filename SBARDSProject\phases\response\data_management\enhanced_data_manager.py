#!/usr/bin/env python3
"""
SBARDS Enhanced Data Manager - Python Interface Layer
C++ Core Data Engine with Python Integration Layer for Advanced Data Management

This module provides Python interface to the C++ Data Core Engine:
1. C++ Core Data Engine (High-Performance Data Processing)
2. Python Integration Layer (API, Configuration, Monitoring)
3. Cross-Platform Compatibility (Windows, Linux, macOS)
4. Advanced Security Integration
5. Real-time Data Monitoring
6. Enterprise-Grade Data Management
"""

import os
import sys
import json
import logging
import ctypes
import asyncio
import threading
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Callable
import platform
import subprocess
from dataclasses import dataclass, asdict

# Platform detection for C++ library loading
PLATFORM = platform.system().lower()
ARCHITECTURE = platform.machine().lower()

# C++ Library availability check
CPP_DATA_CORE_AVAILABLE = False
CPP_DATA_LIBRARY_PATH = None

# Optional imports with fallbacks
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    requests = None
    REQUESTS_AVAILABLE = False

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    psutil = None
    PSUTIL_AVAILABLE = False

@dataclass
class CPPDataLibraryInfo:
    """Information about loaded C++ data library."""
    path: str
    loaded: bool
    version: str
    platform: str
    architecture: str

@dataclass
class DataMetrics:
    """Data performance metrics."""
    total_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    average_operation_time_ms: float = 0.0
    total_data_stored_bytes: int = 0
    total_data_retrieved_bytes: int = 0
    encryption_operations: int = 0
    compression_operations: int = 0

class CPPDataEngineInterface:
    """Interface to C++ Data Engine."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize C++ Data Engine Interface."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.CPPDataInterface")
        
        # C++ library handle
        self.cpp_lib = None
        self.engine_handle = None
        self.library_info = None
        
        # Threading
        self.lock = threading.RLock()
        
        # Initialize C++ engine
        self._initialize_cpp_data_engine()
    
    def _initialize_cpp_data_engine(self):
        """Initialize C++ Data Engine."""
        try:
            # Load C++ library
            if self._load_cpp_data_library():
                # Create engine instance
                config_json = json.dumps(self._convert_config_to_cpp_format())
                self.engine_handle = self.cpp_lib.CreateDataEngine(config_json.encode('utf-8'))
                
                if self.engine_handle:
                    # Initialize engine
                    if self.cpp_lib.InitializeDataEngine(self.engine_handle):
                        global CPP_DATA_CORE_AVAILABLE
                        CPP_DATA_CORE_AVAILABLE = True
                        self.logger.info("C++ Data Engine initialized successfully")
                    else:
                        self.logger.error("Failed to initialize C++ Data Engine")
                        self._cleanup()
                else:
                    self.logger.error("Failed to create C++ Data Engine")
            else:
                self.logger.warning("C++ data library not available, using Python fallback")
                
        except Exception as e:
            self.logger.error(f"C++ data engine initialization failed: {e}")
            self._cleanup()
    
    def _load_cpp_data_library(self) -> bool:
        """Load C++ data library."""
        try:
            # Determine library paths based on platform
            lib_paths = self._get_data_library_paths()
            
            for lib_path in lib_paths:
                if lib_path.exists():
                    try:
                        self.cpp_lib = ctypes.CDLL(str(lib_path))
                        self._setup_data_function_signatures()
                        
                        self.library_info = CPPDataLibraryInfo(
                            path=str(lib_path),
                            loaded=True,
                            version="1.0.0",
                            platform=PLATFORM,
                            architecture=ARCHITECTURE
                        )
                        
                        global CPP_DATA_LIBRARY_PATH
                        CPP_DATA_LIBRARY_PATH = str(lib_path)
                        
                        self.logger.info(f"Loaded C++ data library: {lib_path}")
                        return True
                        
                    except Exception as e:
                        self.logger.warning(f"Failed to load {lib_path}: {e}")
                        continue
            
            self.logger.warning("No C++ data library found")
            return False
            
        except Exception as e:
            self.logger.error(f"Error loading C++ data library: {e}")
            return False
    
    def _get_data_library_paths(self) -> List[Path]:
        """Get possible C++ data library paths."""
        base_path = Path(__file__).parent / "cpp_core"
        
        if PLATFORM == "windows":
            extensions = [".dll"]
        elif PLATFORM == "darwin":
            extensions = [".dylib", ".so"]
        else:  # Linux and others
            extensions = [".so"]
        
        paths = []
        for ext in extensions:
            # Build directory
            paths.append(base_path / "build" / f"libsbards_data_engine{ext}")
            paths.append(base_path / "build" / "lib" / f"libsbards_data_engine{ext}")
            
            # Direct in cpp_core
            paths.append(base_path / f"libsbards_data_engine{ext}")
            
            # Release/Debug directories
            paths.append(base_path / "Release" / f"sbards_data_engine{ext}")
            paths.append(base_path / "Debug" / f"sbards_data_engine{ext}")
        
        return paths
    
    def _setup_data_function_signatures(self):
        """Setup C++ data function signatures for ctypes."""
        try:
            # CreateDataEngine
            self.cpp_lib.CreateDataEngine.restype = ctypes.c_void_p
            self.cpp_lib.CreateDataEngine.argtypes = [ctypes.c_char_p]
            
            # DestroyDataEngine
            self.cpp_lib.DestroyDataEngine.restype = None
            self.cpp_lib.DestroyDataEngine.argtypes = [ctypes.c_void_p]
            
            # InitializeDataEngine
            self.cpp_lib.InitializeDataEngine.restype = ctypes.c_bool
            self.cpp_lib.InitializeDataEngine.argtypes = [ctypes.c_void_p]
            
            # ShutdownDataEngine
            self.cpp_lib.ShutdownDataEngine.restype = None
            self.cpp_lib.ShutdownDataEngine.argtypes = [ctypes.c_void_p]
            
            # StoreDataOperation
            self.cpp_lib.StoreDataOperation.restype = ctypes.c_char_p
            self.cpp_lib.StoreDataOperation.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            # RetrieveDataOperation
            self.cpp_lib.RetrieveDataOperation.restype = ctypes.c_char_p
            self.cpp_lib.RetrieveDataOperation.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            # GetDataPerformanceMetrics
            self.cpp_lib.GetDataPerformanceMetrics.restype = ctypes.c_char_p
            self.cpp_lib.GetDataPerformanceMetrics.argtypes = [ctypes.c_void_p]
            
            # UpdateDataConfiguration
            self.cpp_lib.UpdateDataConfiguration.restype = ctypes.c_bool
            self.cpp_lib.UpdateDataConfiguration.argtypes = [ctypes.c_void_p, ctypes.c_char_p]
            
            # FreeDataMemory
            self.cpp_lib.FreeDataMemory.restype = None
            self.cpp_lib.FreeDataMemory.argtypes = [ctypes.c_char_p]
            
        except Exception as e:
            self.logger.error(f"Error setting up data function signatures: {e}")
            raise
    
    def _convert_config_to_cpp_format(self) -> Dict[str, Any]:
        """Convert Python config to C++ format."""
        data_config = self.config.get("response_data", {})
        
        return {
            "security_level": 1,  # ENHANCED
            "base_directory": data_config.get("base_directory", "response_data"),
            "quarantine_directory": data_config.get("quarantine_directory", "response_data/quarantine"),
            "honeypot_directory": data_config.get("honeypot_directory", "response_data/honeypot"),
            "forensics_directory": data_config.get("forensics_directory", "response_data/forensics"),
            "ml_models_directory": data_config.get("ml_models_directory", "response_data/ml_models"),
            "blockchain_directory": data_config.get("blockchain_directory", "response_data/blockchain"),
            "backup_directory": data_config.get("backup_directory", "response_data/backup"),
            "reports_directory": data_config.get("reports_directory", "response_data/reports"),
            "safe_files_directory": data_config.get("safe_files_directory", "response_data/safe_files"),
            "encryption_enabled": data_config.get("encryption_enabled", True),
            "compression_enabled": data_config.get("compression_enabled", False),
            "blockchain_logging": data_config.get("blockchain_logging", False),
            "forensic_integrity_check": data_config.get("forensic_integrity_check", True),
            "max_concurrent_operations": data_config.get("max_concurrent_operations", 10),
            "operation_timeout_seconds": data_config.get("operation_timeout_seconds", 300)
        }
    
    def _cleanup(self):
        """Cleanup C++ resources."""
        try:
            if self.engine_handle and self.cpp_lib:
                self.cpp_lib.ShutdownDataEngine(self.engine_handle)
                self.cpp_lib.DestroyDataEngine(self.engine_handle)
                self.engine_handle = None
        except Exception as e:
            self.logger.error(f"Error during data cleanup: {e}")
    
    def __del__(self):
        """Destructor."""
        self._cleanup()

class EnhancedDataManager:
    """Enhanced Data Manager with C++ Core Engine Integration."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize Enhanced Data Manager."""
        self.config = config
        self.logger = logging.getLogger("SBARDS.EnhancedDataManager")

        # Initialize C++ Data Engine Interface
        self.cpp_interface = CPPDataEngineInterface(config)

        # Enhanced configuration
        self.enhanced_config = config.get("enhanced_data", {})
        self.security_enabled = self.enhanced_config.get("security_enabled", True)
        self.encryption_enabled = self.enhanced_config.get("encryption_enabled", True)
        self.forensic_mode = self.enhanced_config.get("forensic_mode", True)

        # Performance monitoring
        self.data_metrics = {
            "total_operations": 0,
            "cpp_enhanced_operations": 0,
            "security_operations": 0,
            "encryption_operations": 0,
            "forensic_operations": 0
        }

        # Data tracking
        self.data_registry = {}
        self.registry_lock = threading.RLock()

        self.logger.info("Enhanced Data Manager with C++ Core initialized successfully")

    async def store_data_secure(self, data: bytes, data_type: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Store data securely using C++ core engine."""
        try:
            self.logger.info(f"Storing data securely: type={data_type}, size={len(data)} bytes")

            # Try C++ core engine first
            if CPP_DATA_CORE_AVAILABLE and self.cpp_interface.engine_handle:
                cpp_result = await self._store_with_cpp_engine(data, data_type, metadata)
                if cpp_result.get("success", False):
                    self.logger.info("C++ core data engine stored successfully")
                    return cpp_result
                else:
                    self.logger.warning("C++ core data engine failed, using Python fallback")

            # Python fallback
            return await self._store_with_python_fallback(data, data_type, metadata)

        except Exception as e:
            self.logger.error(f"Error in secure data storage: {e}")
            return {"success": False, "error": str(e)}

    async def _store_with_cpp_engine(self, data: bytes, data_type: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Store data using C++ core engine."""
        try:
            with self.cpp_interface.lock:
                if not self.cpp_interface.engine_handle:
                    return {"success": False, "error": "C++ data engine not available"}

                # Convert data to JSON format for C++ engine
                import base64
                data_json = json.dumps({
                    "data": base64.b64encode(data).decode('utf-8'),
                    "data_type": self._map_data_type_to_cpp(data_type),
                    "metadata": metadata
                })
                
                # Call C++ engine
                result_ptr = self.cpp_interface.cpp_lib.StoreDataOperation(
                    self.cpp_interface.engine_handle,
                    data_json.encode('utf-8')
                )

                if not result_ptr:
                    return {"success": False, "error": "C++ data engine returned null"}

                # Convert result back to Python
                result_json = ctypes.string_at(result_ptr).decode('utf-8')
                self.cpp_interface.cpp_lib.FreeDataMemory(result_ptr)

                cpp_result = json.loads(result_json)
                
                # Add Python integration metadata
                cpp_result["cpp_integration"] = True
                cpp_result["data_engine_version"] = "1.0.0"
                cpp_result["timestamp"] = datetime.now(timezone.utc).isoformat()

                # Update metrics
                self.data_metrics["cpp_enhanced_operations"] += 1

                return cpp_result

        except Exception as e:
            self.logger.error(f"Error storing with C++ data engine: {e}")
            return {"success": False, "error": str(e)}

    def _map_data_type_to_cpp(self, data_type: str) -> int:
        """Map Python data type to C++ enum value."""
        mapping = {
            "quarantine": 0,
            "honeypot": 1,
            "forensic": 2,
            "ml_model": 3,
            "blockchain": 4,
            "backup": 5,
            "report": 6,
            "safe_file": 7
        }
        return mapping.get(data_type.lower(), 0)

    async def _store_with_python_fallback(self, data: bytes, data_type: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Store data using Python fallback."""
        try:
            # Generate record ID
            record_id = self._generate_record_id()
            
            # Determine storage path
            storage_path = self._get_storage_path(data_type)
            
            # Create directories
            os.makedirs(storage_path, exist_ok=True)
            
            # Store data
            file_path = os.path.join(storage_path, f"{record_id}.dat")
            
            with open(file_path, 'wb') as f:
                f.write(data)
            
            # Store metadata
            metadata_path = os.path.join(storage_path, f"{record_id}.meta")
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Update registry
            with self.registry_lock:
                self.data_registry[record_id] = {
                    "data_type": data_type,
                    "file_path": file_path,
                    "metadata_path": metadata_path,
                    "size": len(data),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            
            # Update metrics
            self.data_metrics["total_operations"] += 1
            
            return {
                "success": True,
                "record_id": record_id,
                "stored_path": file_path,
                "cpp_integration": False,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error in Python fallback storage: {e}")
            return {"success": False, "error": str(e)}

    def _generate_record_id(self) -> str:
        """Generate unique record ID."""
        import uuid
        return f"REC_{int(time.time())}_{uuid.uuid4().hex[:8]}"

    def _get_storage_path(self, data_type: str) -> str:
        """Get storage path for data type."""
        base_dir = self.config.get("response_data", {}).get("base_directory", "response_data")
        
        type_mapping = {
            "quarantine": "quarantine",
            "honeypot": "honeypot", 
            "forensic": "forensics",
            "ml_model": "ml_models",
            "blockchain": "blockchain",
            "backup": "backup",
            "report": "reports",
            "safe_file": "safe_files"
        }
        
        subdir = type_mapping.get(data_type.lower(), "general")
        return os.path.join(base_dir, subdir)

    async def retrieve_data_secure(self, record_id: str) -> Dict[str, Any]:
        """Retrieve data securely."""
        try:
            self.logger.info(f"Retrieving data securely: record_id={record_id}")

            # Try C++ core engine first
            if CPP_DATA_CORE_AVAILABLE and self.cpp_interface.engine_handle:
                cpp_result = await self._retrieve_with_cpp_engine(record_id)
                if cpp_result.get("success", False):
                    return cpp_result

            # Python fallback
            return await self._retrieve_with_python_fallback(record_id)

        except Exception as e:
            self.logger.error(f"Error in secure data retrieval: {e}")
            return {"success": False, "error": str(e)}

    async def _retrieve_with_cpp_engine(self, record_id: str) -> Dict[str, Any]:
        """Retrieve data using C++ core engine."""
        try:
            with self.cpp_interface.lock:
                if not self.cpp_interface.engine_handle:
                    return {"success": False, "error": "C++ data engine not available"}

                # Call C++ engine
                result_ptr = self.cpp_interface.cpp_lib.RetrieveDataOperation(
                    self.cpp_interface.engine_handle,
                    record_id.encode('utf-8')
                )

                if not result_ptr:
                    return {"success": False, "error": "C++ data engine returned null"}

                # Convert result back to Python
                result_json = ctypes.string_at(result_ptr).decode('utf-8')
                self.cpp_interface.cpp_lib.FreeDataMemory(result_ptr)

                cpp_result = json.loads(result_json)
                cpp_result["cpp_integration"] = True

                return cpp_result

        except Exception as e:
            self.logger.error(f"Error retrieving with C++ data engine: {e}")
            return {"success": False, "error": str(e)}

    async def _retrieve_with_python_fallback(self, record_id: str) -> Dict[str, Any]:
        """Retrieve data using Python fallback."""
        try:
            with self.registry_lock:
                if record_id not in self.data_registry:
                    return {"success": False, "error": "Record not found"}
                
                record_info = self.data_registry[record_id]
            
            # Read data
            with open(record_info["file_path"], 'rb') as f:
                data = f.read()
            
            # Read metadata
            with open(record_info["metadata_path"], 'r') as f:
                metadata = json.load(f)
            
            import base64
            return {
                "success": True,
                "record_id": record_id,
                "data": base64.b64encode(data).decode('utf-8'),
                "metadata": metadata,
                "cpp_integration": False,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error in Python fallback retrieval: {e}")
            return {"success": False, "error": str(e)}

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        try:
            # Try to get C++ metrics first
            if CPP_DATA_CORE_AVAILABLE and self.cpp_interface.engine_handle:
                with self.cpp_interface.lock:
                    metrics_ptr = self.cpp_interface.cpp_lib.GetDataPerformanceMetrics(
                        self.cpp_interface.engine_handle
                    )
                    
                    if metrics_ptr:
                        metrics_json = ctypes.string_at(metrics_ptr).decode('utf-8')
                        self.cpp_interface.cpp_lib.FreeDataMemory(metrics_ptr)
                        
                        cpp_metrics = json.loads(metrics_json)
                        cpp_metrics.update(self.data_metrics)
                        return cpp_metrics
            
            # Return Python metrics
            return self.data_metrics

        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {e}")
            return self.data_metrics
