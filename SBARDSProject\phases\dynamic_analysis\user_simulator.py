"""
SBARDS User Environment Simulator
Advanced user interaction simulation for dynamic analysis

Features:
- Application installation simulation
- User interaction simulation (clicks, typing, scrolling)
- Time delay simulation to bypass malware delays
- Realistic data environment creation
- Multi-platform support (Windows, Linux, macOS)
"""

import os
import asyncio
import logging
import time
import random
import tempfile
import shutil
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import subprocess
import threading

# Platform-specific imports
import platform
if platform.system() == "Windows":
    try:
        import pyautogui
        import win32gui
        import win32con
        import win32api
        import win32process
    except ImportError:
        pyautogui = None
elif platform.system() == "Linux":
    try:
        import pyautogui
        from pynput import mouse, keyboard
        import Xlib.display
    except ImportError:
        pyautogui = None
elif platform.system() == "Darwin":
    try:
        import pyautogui
        from pynput import mouse, keyboard
        import osascript
    except ImportError:
        pyautogui = None

class UserSimulator:
    """
    Advanced User Environment Simulator
    
    Simulates realistic user environment and interactions to trigger
    malware behavior that depends on user activity
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize User Simulator
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.UserSimulator")
        
        # User simulation configuration
        self.user_config = config.get("dynamic_analysis", {}).get("user_simulation", {})
        self.enabled = self.user_config.get("enabled", True)
        
        if not self.enabled:
            self.logger.info("User simulation disabled")
            return
        
        # Simulation settings
        self.install_applications = self.user_config.get("install_applications", True)
        self.simulate_interactions = self.user_config.get("simulate_interactions", True)
        self.time_delays = self.user_config.get("time_delays", True)
        self.realistic_data = self.user_config.get("realistic_data", True)
        
        # Application list
        self.applications = self.user_config.get("applications", [
            "office_suite", "web_browser", "pdf_reader", "media_player"
        ])
        
        # Platform detection
        self.platform = platform.system()
        self.logger.info(f"User simulator initialized for platform: {self.platform}")
        
        # Initialize platform-specific components
        self._init_platform_components()
        
        # Simulation state
        self.simulation_active = False
        self.simulation_thread = None
        self.stop_event = threading.Event()
        
        # Data directories
        self.temp_user_dir = None
        self.documents_dir = None
        self.downloads_dir = None
        self.desktop_dir = None
        
    def _init_platform_components(self):
        """Initialize platform-specific components"""
        try:
            if self.platform == "Windows":
                self._init_windows_components()
            elif self.platform == "Linux":
                self._init_linux_components()
            elif self.platform == "Darwin":
                self._init_macos_components()
            else:
                self.logger.warning(f"Unsupported platform: {self.platform}")
                self.enabled = False
        except Exception as e:
            self.logger.error(f"Failed to initialize platform components: {e}")
            self.enabled = False
    
    def _init_windows_components(self):
        """Initialize Windows-specific components"""
        if pyautogui is None:
            self.logger.warning("pyautogui not available, user simulation limited")
        
        # Configure pyautogui
        if pyautogui:
            pyautogui.FAILSAFE = True
            pyautogui.PAUSE = 0.1
    
    def _init_linux_components(self):
        """Initialize Linux-specific components"""
        if pyautogui is None:
            self.logger.warning("pyautogui not available, user simulation limited")
        
        # Configure pyautogui for Linux
        if pyautogui:
            pyautogui.FAILSAFE = True
            pyautogui.PAUSE = 0.1
    
    def _init_macos_components(self):
        """Initialize macOS-specific components"""
        if pyautogui is None:
            self.logger.warning("pyautogui not available, user simulation limited")
        
        # Configure pyautogui for macOS
        if pyautogui:
            pyautogui.FAILSAFE = True
            pyautogui.PAUSE = 0.1
    
    async def simulate_interaction_async(self, file_path: str, timeout: int = 300) -> Dict[str, Any]:
        """
        Simulate user interactions asynchronously
        
        Args:
            file_path: Path to file being analyzed
            timeout: Simulation timeout in seconds
            
        Returns:
            Simulation results
        """
        if not self.enabled:
            return {
                "success": False,
                "error": "User simulation disabled",
                "timestamp": datetime.now().isoformat()
            }
        
        try:
            self.logger.info(f"Starting user simulation for: {file_path}")
            start_time = datetime.now()
            
            # Create temporary user environment
            await self._create_user_environment()
            
            # Install applications if enabled
            if self.install_applications:
                await self._install_applications()
            
            # Create realistic data if enabled
            if self.realistic_data:
                await self._create_realistic_data()
            
            # Start interaction simulation
            simulation_results = await self._run_interaction_simulation(file_path, timeout)
            
            # Add time delays to bypass malware delays
            if self.time_delays:
                await self._simulate_time_delays()
            
            end_time = datetime.now()
            
            result = {
                "success": True,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration_seconds": (end_time - start_time).total_seconds(),
                "simulation_results": simulation_results,
                "environment_created": True,
                "applications_installed": self.install_applications,
                "realistic_data_created": self.realistic_data,
                "time_delays_simulated": self.time_delays,
                "platform": self.platform
            }
            
            self.logger.info("User simulation completed successfully")
            return result
            
        except Exception as e:
            self.logger.error(f"User simulation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        
        finally:
            # Cleanup
            await self._cleanup_user_environment()
    
    async def _create_user_environment(self):
        """Create realistic user environment"""
        try:
            # Create temporary user directory
            self.temp_user_dir = tempfile.mkdtemp(prefix="sbards_user_")
            
            # Create standard user directories
            self.documents_dir = Path(self.temp_user_dir) / "Documents"
            self.downloads_dir = Path(self.temp_user_dir) / "Downloads"
            self.desktop_dir = Path(self.temp_user_dir) / "Desktop"
            
            for directory in [self.documents_dir, self.downloads_dir, self.desktop_dir]:
                directory.mkdir(parents=True, exist_ok=True)
            
            self.logger.info(f"Created user environment: {self.temp_user_dir}")
            
        except Exception as e:
            self.logger.error(f"Failed to create user environment: {e}")
            raise
    
    async def _install_applications(self):
        """Simulate application installation"""
        try:
            installed_apps = []
            
            for app in self.applications:
                if app == "office_suite":
                    await self._simulate_office_installation()
                    installed_apps.append("office_suite")
                elif app == "web_browser":
                    await self._simulate_browser_installation()
                    installed_apps.append("web_browser")
                elif app == "pdf_reader":
                    await self._simulate_pdf_reader_installation()
                    installed_apps.append("pdf_reader")
                elif app == "media_player":
                    await self._simulate_media_player_installation()
                    installed_apps.append("media_player")
            
            self.logger.info(f"Simulated installation of: {installed_apps}")
            
        except Exception as e:
            self.logger.error(f"Failed to simulate application installation: {e}")
    
    async def _simulate_office_installation(self):
        """Simulate office suite installation"""
        # Create office-like directory structure
        office_dir = Path(self.temp_user_dir) / "Program Files" / "Office"
        office_dir.mkdir(parents=True, exist_ok=True)
        
        # Create dummy executables
        for app in ["winword.exe", "excel.exe", "powerpnt.exe"]:
            app_path = office_dir / app
            app_path.write_text("# Dummy office application")
        
        # Create recent documents
        recent_dir = self.documents_dir / "Recent"
        recent_dir.mkdir(exist_ok=True)
        
        await asyncio.sleep(0.5)  # Simulate installation time
    
    async def _simulate_browser_installation(self):
        """Simulate web browser installation"""
        browser_dir = Path(self.temp_user_dir) / "Program Files" / "Browser"
        browser_dir.mkdir(parents=True, exist_ok=True)
        
        # Create browser executable
        browser_exe = browser_dir / "browser.exe"
        browser_exe.write_text("# Dummy browser application")
        
        # Create browser data directory
        browser_data = Path(self.temp_user_dir) / "AppData" / "Browser"
        browser_data.mkdir(parents=True, exist_ok=True)
        
        # Create dummy bookmarks and history
        (browser_data / "bookmarks.json").write_text('{"bookmarks": []}')
        (browser_data / "history.db").write_text("# Dummy history database")
        
        await asyncio.sleep(0.5)
    
    async def _simulate_pdf_reader_installation(self):
        """Simulate PDF reader installation"""
        pdf_dir = Path(self.temp_user_dir) / "Program Files" / "PDFReader"
        pdf_dir.mkdir(parents=True, exist_ok=True)
        
        pdf_exe = pdf_dir / "pdfreader.exe"
        pdf_exe.write_text("# Dummy PDF reader")
        
        await asyncio.sleep(0.3)
    
    async def _simulate_media_player_installation(self):
        """Simulate media player installation"""
        media_dir = Path(self.temp_user_dir) / "Program Files" / "MediaPlayer"
        media_dir.mkdir(parents=True, exist_ok=True)
        
        media_exe = media_dir / "mediaplayer.exe"
        media_exe.write_text("# Dummy media player")
        
        await asyncio.sleep(0.3)
    
    async def _create_realistic_data(self):
        """Create realistic user data"""
        try:
            # Create documents
            await self._create_sample_documents()
            
            # Create images
            await self._create_sample_images()
            
            # Create downloads
            await self._create_sample_downloads()
            
            # Create desktop shortcuts
            await self._create_desktop_shortcuts()
            
            self.logger.info("Created realistic user data")
            
        except Exception as e:
            self.logger.error(f"Failed to create realistic data: {e}")
    
    async def _create_sample_documents(self):
        """Create sample documents"""
        documents = [
            ("report.txt", "This is a sample report document.\nIt contains important information."),
            ("budget.csv", "Item,Amount\nOffice Supplies,150\nSoftware License,500"),
            ("presentation.txt", "# Sample Presentation\n\n## Slide 1\nIntroduction\n\n## Slide 2\nContent"),
            ("notes.txt", "Meeting notes:\n- Discuss project timeline\n- Review budget\n- Plan next steps")
        ]
        
        for filename, content in documents:
            doc_path = self.documents_dir / filename
            doc_path.write_text(content)
        
        await asyncio.sleep(0.2)
    
    async def _create_sample_images(self):
        """Create sample image files"""
        # Create dummy image files (just text files with image extensions)
        images = ["photo1.jpg", "screenshot.png", "diagram.gif"]
        
        for image in images:
            img_path = self.documents_dir / image
            img_path.write_text("# Dummy image file")
        
        await asyncio.sleep(0.1)
    
    async def _create_sample_downloads(self):
        """Create sample download files"""
        downloads = [
            ("installer.exe", "# Dummy installer"),
            ("document.pdf", "# Dummy PDF document"),
            ("archive.zip", "# Dummy archive file")
        ]
        
        for filename, content in downloads:
            download_path = self.downloads_dir / filename
            download_path.write_text(content)
        
        await asyncio.sleep(0.1)
    
    async def _create_desktop_shortcuts(self):
        """Create desktop shortcuts"""
        shortcuts = ["Browser.lnk", "Office.lnk", "PDFReader.lnk"]
        
        for shortcut in shortcuts:
            shortcut_path = self.desktop_dir / shortcut
            shortcut_path.write_text("# Dummy shortcut")
        
        await asyncio.sleep(0.1)
    
    async def _run_interaction_simulation(self, file_path: str, timeout: int) -> Dict[str, Any]:
        """Run user interaction simulation"""
        try:
            interactions = []
            start_time = time.time()
            
            # Simulate various user interactions
            if self.simulate_interactions and pyautogui:
                # Mouse movements
                await self._simulate_mouse_movements()
                interactions.append("mouse_movements")
                
                # Keyboard activity
                await self._simulate_keyboard_activity()
                interactions.append("keyboard_activity")
                
                # Window interactions
                await self._simulate_window_interactions()
                interactions.append("window_interactions")
                
                # File operations
                await self._simulate_file_operations()
                interactions.append("file_operations")
            
            # Wait for a period to allow malware to activate
            await self._simulate_user_idle_time()
            interactions.append("idle_time")
            
            return {
                "interactions_performed": interactions,
                "duration_seconds": time.time() - start_time,
                "mouse_movements": True,
                "keyboard_activity": True,
                "window_interactions": True,
                "file_operations": True
            }
            
        except Exception as e:
            self.logger.error(f"Interaction simulation failed: {e}")
            return {"error": str(e)}
    
    async def _simulate_mouse_movements(self):
        """Simulate realistic mouse movements"""
        if not pyautogui:
            return
        
        try:
            # Get screen size
            screen_width, screen_height = pyautogui.size()
            
            # Perform random mouse movements
            for _ in range(5):
                x = random.randint(100, screen_width - 100)
                y = random.randint(100, screen_height - 100)
                
                # Move mouse smoothly
                pyautogui.moveTo(x, y, duration=random.uniform(0.5, 1.5))
                await asyncio.sleep(random.uniform(0.2, 0.8))
                
                # Random clicks
                if random.random() < 0.3:
                    pyautogui.click()
                    await asyncio.sleep(random.uniform(0.1, 0.5))
            
        except Exception as e:
            self.logger.error(f"Mouse simulation failed: {e}")
    
    async def _simulate_keyboard_activity(self):
        """Simulate keyboard activity"""
        if not pyautogui:
            return
        
        try:
            # Simulate typing
            sample_texts = [
                "Hello world",
                "This is a test",
                "User activity simulation",
                "Dynamic analysis in progress"
            ]
            
            for text in sample_texts[:2]:  # Limit to avoid interference
                pyautogui.typewrite(text, interval=random.uniform(0.05, 0.15))
                await asyncio.sleep(random.uniform(0.5, 1.0))
                
                # Press some keys
                pyautogui.press('enter')
                await asyncio.sleep(random.uniform(0.2, 0.5))
            
        except Exception as e:
            self.logger.error(f"Keyboard simulation failed: {e}")
    
    async def _simulate_window_interactions(self):
        """Simulate window interactions"""
        try:
            if self.platform == "Windows" and pyautogui:
                # Simulate Alt+Tab
                pyautogui.hotkey('alt', 'tab')
                await asyncio.sleep(1)
                
                # Simulate Windows key
                pyautogui.press('win')
                await asyncio.sleep(0.5)
                pyautogui.press('esc')
                
        except Exception as e:
            self.logger.error(f"Window interaction simulation failed: {e}")
    
    async def _simulate_file_operations(self):
        """Simulate file operations"""
        try:
            # Create and delete temporary files
            temp_file = self.documents_dir / "temp_user_file.txt"
            temp_file.write_text("Temporary file created by user simulation")
            await asyncio.sleep(0.5)
            
            # Simulate file access
            content = temp_file.read_text()
            await asyncio.sleep(0.2)
            
            # Delete temporary file
            temp_file.unlink()
            
        except Exception as e:
            self.logger.error(f"File operation simulation failed: {e}")
    
    async def _simulate_user_idle_time(self):
        """Simulate user idle time"""
        # Wait for a period to trigger time-based malware
        idle_time = random.uniform(2, 5)  # 2-5 seconds
        await asyncio.sleep(idle_time)
    
    async def _simulate_time_delays(self):
        """Simulate time delays to bypass malware delays"""
        try:
            # Some malware waits for specific time periods before activating
            delay_periods = [10, 30, 60, 120]  # seconds
            
            for delay in delay_periods[:2]:  # Limit delays to avoid long waits
                self.logger.info(f"Simulating {delay}s time delay")
                await asyncio.sleep(min(delay, 10))  # Cap at 10 seconds for testing
            
        except Exception as e:
            self.logger.error(f"Time delay simulation failed: {e}")
    
    async def _cleanup_user_environment(self):
        """Cleanup temporary user environment"""
        try:
            if self.temp_user_dir and os.path.exists(self.temp_user_dir):
                shutil.rmtree(self.temp_user_dir)
                self.logger.info("Cleaned up user environment")
        except Exception as e:
            self.logger.error(f"Failed to cleanup user environment: {e}")
    
    def is_available(self) -> bool:
        """Check if user simulation is available"""
        return self.enabled and pyautogui is not None
