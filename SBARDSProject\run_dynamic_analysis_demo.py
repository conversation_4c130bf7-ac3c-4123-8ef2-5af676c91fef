#!/usr/bin/env python3
"""
SBARDS Dynamic Analysis Demonstration
Real-time execution of the enhanced dynamic analysis layer
"""

import os
import sys
import json
import asyncio
import time
import tempfile
import logging
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger("SBARDS.DynamicAnalysisDemo")

async def run_dynamic_analysis_demo():
    """Run comprehensive dynamic analysis demonstration"""
    
    print("🚀 SBARDS Dynamic Analysis Layer - Live Demonstration")
    print("=" * 70)
    print(f"Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    try:
        # Import core components
        from phases.dynamic_analysis.advanced_monitoring_engine import AdvancedMonitoringEngine
        from phases.dynamic_analysis.behavioral_memory_analyzer import BehavioralMemoryAnalyzer
        
        # Load configurations
        print("\n📋 Loading Configuration Files...")
        
        with open('config_advanced_monitoring.json', 'r') as f:
            monitoring_config = json.load(f)
        print("✓ Advanced monitoring configuration loaded")
        
        with open('config_behavioral_memory_analysis.json', 'r') as f:
            behavioral_config = json.load(f)
        print("✓ Behavioral analysis configuration loaded")
        
        # Initialize components
        print("\n🔧 Initializing Dynamic Analysis Components...")
        
        monitoring_engine = AdvancedMonitoringEngine(monitoring_config)
        print("✓ Advanced Monitoring Engine initialized")
        
        behavioral_analyzer = BehavioralMemoryAnalyzer(behavioral_config)
        print("✓ Behavioral Memory Analyzer initialized")
        
        # Start monitoring
        print("\n🎯 Starting Advanced Monitoring...")
        await monitoring_engine.start_monitoring()
        print("✓ Advanced monitoring engine started")
        
        # Start behavioral analysis
        print("\n🧠 Starting Behavioral Analysis...")
        await behavioral_analyzer.start_analysis()
        print("✓ Behavioral analysis engine started")
        
        # Generate test activity
        print("\n⚡ Generating Test Activity for Analysis...")
        print("   Creating files, processes, and network activity...")
        
        # Create temporary directory and files
        temp_dir = tempfile.mkdtemp(prefix="sbards_dynamic_test_")
        print(f"   📁 Created test directory: {temp_dir}")
        
        # Create multiple test files
        test_files = []
        for i in range(10):
            test_file = os.path.join(temp_dir, f"test_file_{i}.txt")
            with open(test_file, 'w') as f:
                f.write(f"Test data for dynamic analysis - File {i}\n" * 100)
            test_files.append(test_file)
        print(f"   📄 Created {len(test_files)} test files")
        
        # Simulate file operations
        for i, test_file in enumerate(test_files):
            # Read file
            with open(test_file, 'r') as f:
                content = f.read()
            
            # Modify file
            with open(test_file, 'a') as f:
                f.write(f"Modified at {datetime.now()}\n")
            
            if i % 3 == 0:
                # Simulate some files being "encrypted" (renamed)
                encrypted_name = test_file + ".encrypted"
                os.rename(test_file, encrypted_name)
                test_files[i] = encrypted_name
        
        print("   🔄 Performed file operations (read, write, rename)")
        
        # Let analysis run for monitoring period
        analysis_duration = 20
        print(f"\n⏳ Running Dynamic Analysis for {analysis_duration} seconds...")
        print("   📊 Monitoring system calls, file access, and behavioral patterns...")
        
        for i in range(analysis_duration):
            await asyncio.sleep(1)
            if i % 5 == 0:
                print(f"   ⏱️  Analysis progress: {i}/{analysis_duration} seconds")
        
        # Collect results
        print("\n📊 Collecting Analysis Results...")
        
        # Get monitoring results
        monitoring_results = await monitoring_engine.get_monitoring_results()
        threat_analysis = await monitoring_engine.get_threat_analysis()
        performance_metrics = await monitoring_engine.get_performance_metrics()
        
        # Get behavioral results
        behavioral_results = await behavioral_analyzer.get_analysis_results()
        threat_summary = await behavioral_analyzer.get_threat_summary()
        behavioral_performance = await behavioral_analyzer.get_performance_metrics()
        
        # Display comprehensive results
        print("\n" + "=" * 70)
        print("📈 DYNAMIC ANALYSIS RESULTS")
        print("=" * 70)
        
        print("\n🔧 Advanced Monitoring Results:")
        print("-" * 40)
        print(f"   📞 System call events: {len(monitoring_results.get('syscall_events', []))}")
        print(f"   📁 File system events: {len(monitoring_results.get('file_events', []))}")
        print(f"   🌐 Network events: {len(monitoring_results.get('network_events', []))}")
        print(f"   ⚙️  Configuration events: {len(monitoring_results.get('config_events', []))}")
        print(f"   🚨 Overall threat level: {threat_analysis.get('overall_threat_level', 'unknown')}")
        
        # Show threat indicators if any
        threat_indicators = threat_analysis.get('threat_indicators', {})
        if threat_indicators:
            print(f"   🔍 Threat indicators detected:")
            for category, count in threat_indicators.items():
                if count > 0:
                    print(f"      - {category}: {count}")
        
        print(f"\n🧠 Behavioral Analysis Results:")
        print("-" * 40)
        print(f"   📊 Resource metrics collected: {len(behavioral_results.get('resource_metrics', []))}")
        print(f"   🎯 Behavioral patterns detected: {len(behavioral_results.get('behavioral_patterns', []))}")
        print(f"   🔍 Process analyses completed: {len(behavioral_results.get('process_analyses', {}))}")
        print(f"   🚨 Threat assessment: {threat_summary.get('overall_threat_level', 'unknown')}")
        
        # Show behavioral patterns if any
        patterns = behavioral_results.get('behavioral_patterns', [])
        if patterns:
            print(f"   📋 Recent behavioral patterns:")
            for pattern in patterns[-5:]:  # Show last 5 patterns
                print(f"      - {pattern.get('pattern_type', 'Unknown')}: {pattern.get('description', 'No description')}")
        
        print(f"\n⚡ Performance Metrics:")
        print("-" * 40)
        print(f"   🖥️  Monitoring engine threads: {performance_metrics.get('active_threads', 0)}")
        print(f"   🧠 Behavioral analyzer threads: {behavioral_performance.get('active_threads', 0)}")
        print(f"   💾 Memory usage: {performance_metrics.get('memory_usage_mb', 0):.1f} MB")
        print(f"   🔄 CPU usage: {performance_metrics.get('cpu_usage_percent', 0):.1f}%")
        
        # Test memory analysis
        print(f"\n🧬 Memory Analysis Test:")
        print("-" * 40)
        current_pid = os.getpid()
        memory_dump = behavioral_analyzer._create_memory_dump(current_pid)
        if memory_dump:
            print(f"   ✓ Memory dump created: {memory_dump.get('dump_path', 'Unknown')}")
            print(f"   📏 Dump size: {memory_dump.get('size', 0)} bytes")
            
            # Test injection detection
            injection_result = behavioral_analyzer._detect_memory_injections(current_pid)
            print(f"   🔍 Injection detection: {'⚠️ Detected' if injection_result else '✅ Clean'}")
            
            # Cleanup
            if memory_dump.get('dump_path') and os.path.exists(memory_dump['dump_path']):
                os.unlink(memory_dump['dump_path'])
                print(f"   🧹 Memory dump cleaned up")
        
        # Export results
        print(f"\n💾 Exporting Analysis Data...")
        print("-" * 40)
        
        # Export monitoring data
        monitoring_export = tempfile.mktemp(suffix='.json')
        await monitoring_engine.export_data(monitoring_export, format='json')
        print(f"   📄 Monitoring data exported: {monitoring_export}")
        
        # Show export file size
        if os.path.exists(monitoring_export):
            export_size = os.path.getsize(monitoring_export)
            print(f"   📏 Export file size: {export_size} bytes")
        
        # Stop analysis components
        print(f"\n🛑 Stopping Analysis Components...")
        await monitoring_engine.stop_monitoring()
        print("   ✓ Advanced monitoring stopped")
        
        await behavioral_analyzer.stop_analysis()
        print("   ✓ Behavioral analysis stopped")
        
        # Cleanup test files
        print(f"\n🧹 Cleaning Up Test Environment...")
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        print(f"   ✓ Test directory cleaned up")
        
        if os.path.exists(monitoring_export):
            os.unlink(monitoring_export)
            print(f"   ✓ Export file cleaned up")
        
        # Final summary
        total_events = (len(monitoring_results.get('syscall_events', [])) + 
                       len(monitoring_results.get('file_events', [])) + 
                       len(monitoring_results.get('network_events', [])) + 
                       len(monitoring_results.get('config_events', [])))
        
        total_patterns = len(behavioral_results.get('behavioral_patterns', []))
        
        print("\n" + "=" * 70)
        print("🎉 DYNAMIC ANALYSIS DEMONSTRATION COMPLETED")
        print("=" * 70)
        print(f"⏱️  Total execution time: {analysis_duration} seconds")
        print(f"📊 Total events captured: {total_events}")
        print(f"🎯 Total patterns detected: {total_patterns}")
        print(f"🚨 Final threat assessment: {threat_analysis.get('overall_threat_level', 'unknown')}")
        print(f"✅ All components operational and functional")
        print(f"🏆 SBARDS Dynamic Analysis: FULLY OPERATIONAL")
        
        return True
        
    except Exception as e:
        logger.error(f"Dynamic analysis demonstration failed: {e}")
        print(f"\n❌ Error during dynamic analysis: {e}")
        return False

async def main():
    """Main execution function"""
    try:
        success = await run_dynamic_analysis_demo()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Analysis interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except Exception as e:
        print(f"Failed to run dynamic analysis demo: {e}")
        sys.exit(1)
