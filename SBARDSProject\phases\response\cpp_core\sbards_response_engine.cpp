/**
 * SBARDS Response Engine - Complete C++ Implementation
 * Primary response system implementation - ALL CORE LOGIC IN C++
 */

#include "sbards_response_engine.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <random>
#include <algorithm>
#include <iomanip>
#include <cstring>

// JSON handling (simple implementation for core functionality)
#include <regex>

namespace SBARDS {
namespace Response {

/**
 * Security Manager Implementation
 */
SecurityManager::SecurityManager(SecurityLevel level) 
    : security_level_(level), cipher_ctx_(nullptr, EVP_CIPHER_CTX_free) {
}

SecurityManager::~SecurityManager() {
    Shutdown();
}

bool SecurityManager::Initialize() {
    std::lock_guard<std::mutex> lock(security_mutex_);
    
    try {
        // Initialize OpenSSL
        if (!InitializeCrypto()) {
            return false;
        }
        
        // Generate master key
        if (!GenerateMasterKey()) {
            return false;
        }
        
        // Initialize cipher context
        cipher_ctx_.reset(EVP_CIPHER_CTX_new());
        if (!cipher_ctx_) {
            return false;
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "SecurityManager initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void SecurityManager::Shutdown() {
    std::lock_guard<std::mutex> lock(security_mutex_);
    
    // Secure cleanup
    if (!master_key_.empty()) {
        SecureMemoryWipe(master_key_.data(), master_key_.size());
        master_key_.clear();
    }
    
    cipher_ctx_.reset();
}

bool SecurityManager::InitializeCrypto() {
#ifdef _WIN32
    // Windows CryptoAPI initialization
    return true;
#else
    // OpenSSL initialization
    return true;
#endif
}

bool SecurityManager::GenerateMasterKey() {
    master_key_.resize(32); // 256-bit key
    
#ifdef _WIN32
    HCRYPTPROV hProv;
    if (!CryptAcquireContext(&hProv, NULL, NULL, PROV_RSA_FULL, CRYPT_VERIFYCONTEXT)) {
        return false;
    }
    
    bool success = CryptGenRandom(hProv, static_cast<DWORD>(master_key_.size()), master_key_.data());
    CryptReleaseContext(hProv, 0);
    return success;
#else
    return RAND_bytes(master_key_.data(), static_cast<int>(master_key_.size())) == 1;
#endif
}

bool SecurityManager::EncryptFile(const std::string& source_path, const std::string& encrypted_path) {
    std::lock_guard<std::mutex> lock(security_mutex_);
    
    try {
        std::ifstream source(source_path, std::ios::binary);
        if (!source.is_open()) {
            return false;
        }
        
        // Read source file
        std::vector<uint8_t> data((std::istreambuf_iterator<char>(source)),
                                 std::istreambuf_iterator<char>());
        source.close();
        
        // Encrypt data
        std::vector<uint8_t> encrypted_data;
        if (!EncryptData(data, encrypted_data)) {
            return false;
        }
        
        // Write encrypted file
        std::ofstream encrypted_file(encrypted_path, std::ios::binary);
        if (!encrypted_file.is_open()) {
            return false;
        }
        
        encrypted_file.write(reinterpret_cast<const char*>(encrypted_data.data()),
                           encrypted_data.size());
        encrypted_file.close();
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "File encryption failed: " << e.what() << std::endl;
        return false;
    }
}

bool SecurityManager::EncryptData(const std::vector<uint8_t>& input, std::vector<uint8_t>& output) {
    try {
        // AES-256-GCM encryption
        const EVP_CIPHER* cipher = EVP_aes_256_gcm();
        
        // Generate random IV
        std::vector<uint8_t> iv(12); // 96-bit IV for GCM
        if (RAND_bytes(iv.data(), static_cast<int>(iv.size())) != 1) {
            return false;
        }
        
        // Initialize encryption
        if (EVP_EncryptInit_ex(cipher_ctx_.get(), cipher, NULL, master_key_.data(), iv.data()) != 1) {
            return false;
        }
        
        // Prepare output buffer
        output.resize(iv.size() + input.size() + 16); // IV + data + tag
        
        // Copy IV to beginning
        std::copy(iv.begin(), iv.end(), output.begin());
        
        // Encrypt
        int len;
        int ciphertext_len;
        
        if (EVP_EncryptUpdate(cipher_ctx_.get(), output.data() + iv.size(), &len, 
                             input.data(), static_cast<int>(input.size())) != 1) {
            return false;
        }
        ciphertext_len = len;
        
        // Finalize
        if (EVP_EncryptFinal_ex(cipher_ctx_.get(), output.data() + iv.size() + len, &len) != 1) {
            return false;
        }
        ciphertext_len += len;
        
        // Get tag
        if (EVP_CIPHER_CTX_ctrl(cipher_ctx_.get(), EVP_CTRL_GCM_GET_TAG, 16, 
                               output.data() + iv.size() + ciphertext_len) != 1) {
            return false;
        }
        
        output.resize(iv.size() + ciphertext_len + 16);
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Data encryption failed: " << e.what() << std::endl;
        return false;
    }
}

std::string SecurityManager::GenerateFileHash(const std::string& file_path) {
    try {
        std::ifstream file(file_path, std::ios::binary);
        if (!file.is_open()) {
            return "";
        }
        
        std::vector<uint8_t> data((std::istreambuf_iterator<char>(file)),
                                 std::istreambuf_iterator<char>());
        file.close();
        
        unsigned char hash[SHA256_DIGEST_LENGTH];
        SHA256_CTX sha256;
        SHA256_Init(&sha256);
        SHA256_Update(&sha256, data.data(), data.size());
        SHA256_Final(hash, &sha256);
        
        std::stringstream ss;
        for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
            ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(hash[i]);
        }
        return ss.str();
        
    } catch (const std::exception& e) {
        std::cerr << "Hash generation failed: " << e.what() << std::endl;
        return "";
    }
}

bool SecurityManager::SecureDelete(const std::string& file_path) {
    try {
        // Get file size
        std::ifstream file(file_path, std::ios::binary | std::ios::ate);
        if (!file.is_open()) {
            return false;
        }
        
        size_t file_size = file.tellg();
        file.close();
        
        // Overwrite with random data (3 passes)
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<uint8_t> dis(0, 255);
        
        for (int pass = 0; pass < 3; ++pass) {
            std::ofstream overwrite_file(file_path, std::ios::binary);
            if (!overwrite_file.is_open()) {
                return false;
            }
            
            for (size_t i = 0; i < file_size; ++i) {
                overwrite_file.put(static_cast<char>(dis(gen)));
            }
            overwrite_file.close();
        }
        
        // Finally delete the file
        return std::filesystem::remove(file_path);
        
    } catch (const std::exception& e) {
        std::cerr << "Secure delete failed: " << e.what() << std::endl;
        return false;
    }
}

bool SecurityManager::SetSecurePermissions(const std::string& file_path, bool read_only) {
    try {
#ifdef _WIN32
        DWORD attributes = GetFileAttributesA(file_path.c_str());
        if (attributes == INVALID_FILE_ATTRIBUTES) {
            return false;
        }
        
        if (read_only) {
            attributes |= FILE_ATTRIBUTE_READONLY;
        } else {
            attributes &= ~FILE_ATTRIBUTE_READONLY;
        }
        
        return SetFileAttributesA(file_path.c_str(), attributes) != 0;
#else
        mode_t mode = read_only ? S_IRUSR : (S_IRUSR | S_IWUSR);
        return chmod(file_path.c_str(), mode) == 0;
#endif
    } catch (const std::exception& e) {
        std::cerr << "Set secure permissions failed: " << e.what() << std::endl;
        return false;
    }
}

void SecurityManager::SecureMemoryWipe(void* ptr, size_t size) {
    volatile uint8_t* p = static_cast<volatile uint8_t*>(ptr);
    for (size_t i = 0; i < size; ++i) {
        p[i] = 0;
    }
}

/**
 * File Operations Manager Implementation
 */
FileOperationsManager::FileOperationsManager(const EngineConfig& config) 
    : base_directory_(config.base_directory) {
    security_manager_ = std::make_unique<SecurityManager>(config.security_level);
}

FileOperationsManager::~FileOperationsManager() {
    Shutdown();
}

bool FileOperationsManager::Initialize() {
    std::lock_guard<std::mutex> lock(file_ops_mutex_);
    
    try {
        // Initialize security manager
        if (!security_manager_->Initialize()) {
            return false;
        }
        
        // Create directory structure
        if (!CreateDirectoryStructure()) {
            return false;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "FileOperationsManager initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void FileOperationsManager::Shutdown() {
    std::lock_guard<std::mutex> lock(file_ops_mutex_);
    
    if (security_manager_) {
        security_manager_->Shutdown();
    }
}

ResponseResult FileOperationsManager::QuarantineFile(const ThreatAssessment& assessment) {
    std::lock_guard<std::mutex> lock(file_ops_mutex_);
    
    ResponseResult result;
    result.action_taken = ResponseAction::QUARANTINE;
    result.source_path = assessment.file_path;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        // Validate file path
        if (!ValidateFilePath(assessment.file_path)) {
            result.error_message = "Invalid file path";
            return result;
        }
        
        // Generate secure filename
        std::string secure_filename = GenerateSecureFilename(assessment.file_path);
        std::string quarantine_path = GetQuarantinePath(secure_filename);
        
        // Move file to quarantine
        if (!MoveFileSecurely(assessment.file_path, quarantine_path)) {
            result.error_message = "Failed to move file to quarantine";
            return result;
        }
        
        // Encrypt quarantined file
        std::string encrypted_path = quarantine_path + ".enc";
        if (!security_manager_->EncryptFile(quarantine_path, encrypted_path)) {
            result.error_message = "Failed to encrypt quarantined file";
            return result;
        }
        
        // Remove unencrypted file
        std::filesystem::remove(quarantine_path);
        
        // Set secure permissions
        security_manager_->SetSecurePermissions(encrypted_path, true);
        
        result.success = true;
        result.target_path = encrypted_path;
        result.operation_id = "QUAR_" + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count());
        
    } catch (const std::exception& e) {
        result.error_message = "Quarantine operation failed: " + std::string(e.what());
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    result.execution_time_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    return result;
}

ResponseResult FileOperationsManager::IsolateInHoneypot(const ThreatAssessment& assessment) {
    std::lock_guard<std::mutex> lock(file_ops_mutex_);
    
    ResponseResult result;
    result.action_taken = ResponseAction::HONEYPOT_ISOLATE;
    result.source_path = assessment.file_path;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        // Validate file path
        if (!ValidateFilePath(assessment.file_path)) {
            result.error_message = "Invalid file path";
            return result;
        }
        
        // Generate secure filename
        std::string secure_filename = GenerateSecureFilename(assessment.file_path);
        std::string honeypot_path = GetHoneypotPath(secure_filename);
        
        // Copy file to honeypot (don't move, keep original for analysis)
        if (!CopyFileSecurely(assessment.file_path, honeypot_path)) {
            result.error_message = "Failed to copy file to honeypot";
            return result;
        }
        
        // Set read-only permissions
        security_manager_->SetSecurePermissions(honeypot_path, true);
        
        result.success = true;
        result.target_path = honeypot_path;
        result.operation_id = "HONEY_" + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count());
        
    } catch (const std::exception& e) {
        result.error_message = "Honeypot isolation failed: " + std::string(e.what());
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    result.execution_time_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    
    return result;
}

bool FileOperationsManager::CreateDirectoryStructure() {
    try {
        std::vector<std::string> directories = {
            base_directory_,
            base_directory_ + "/quarantine",
            base_directory_ + "/honeypot",
            base_directory_ + "/forensics",
            base_directory_ + "/backup",
            base_directory_ + "/logs"
        };
        
        for (const auto& dir : directories) {
            std::filesystem::create_directories(dir);
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Directory structure creation failed: " << e.what() << std::endl;
        return false;
    }
}

bool FileOperationsManager::ValidateFilePath(const std::string& file_path) {
    try {
        return std::filesystem::exists(file_path) && std::filesystem::is_regular_file(file_path);
    } catch (const std::exception&) {
        return false;
    }
}

std::string FileOperationsManager::GenerateSecureFilename(const std::string& original_path) {
    try {
        std::filesystem::path path(original_path);
        std::string filename = path.filename().string();
        
        // Generate timestamp
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S") << "_" << filename;
        
        return ss.str();
        
    } catch (const std::exception&) {
        return "secure_file_" + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count());
    }
}

std::string FileOperationsManager::GetQuarantinePath(const std::string& filename) {
    return base_directory_ + "/quarantine/" + filename;
}

std::string FileOperationsManager::GetHoneypotPath(const std::string& filename) {
    return base_directory_ + "/honeypot/" + filename;
}

bool FileOperationsManager::MoveFileSecurely(const std::string& source, const std::string& destination) {
    try {
        std::filesystem::rename(source, destination);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Secure file move failed: " << e.what() << std::endl;
        return false;
    }
}

bool FileOperationsManager::CopyFileSecurely(const std::string& source, const std::string& destination) {
    try {
        std::filesystem::copy_file(source, destination, std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Secure file copy failed: " << e.what() << std::endl;
        return false;
    }
}
