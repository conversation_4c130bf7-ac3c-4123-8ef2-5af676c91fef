"""
SBARDS Behavioral and Memory Analysis Engine
Advanced behavioral pattern detection and memory forensics

Features:
- Resource usage monitoring and analysis
- Behavioral pattern detection (ransomware, APT, etc.)
- Process and service analysis
- Memory forensics and injection detection
- Volatility framework integration
- Real-time threat correlation
"""

import os
import sys
import asyncio
import logging
import json
import time
import threading
import subprocess
import tempfile
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import ctypes
from ctypes import cdll, Structure, c_char_p, c_int, c_bool, c_uint32, c_uint64, c_double

# Third-party imports (with fallbacks)
try:
    import psutil
except ImportError:
    psutil = None

try:
    import numpy as np
except ImportError:
    # Fallback for numpy functionality
    class NumpyFallback:
        @staticmethod
        def mean(data):
            return sum(data) / len(data) if data else 0
        @staticmethod
        def max(data):
            return max(data) if data else 0
        @staticmethod
        def std(data):
            if not data:
                return 0
            mean_val = sum(data) / len(data)
            variance = sum((x - mean_val) ** 2 for x in data) / len(data)
            return variance ** 0.5
    np = NumpyFallback()

from collections import Counter

@dataclass
class ResourceUsageMetrics:
    """Resource usage metrics"""
    timestamp: datetime
    process_id: int
    process_name: str

    # CPU metrics
    cpu_usage_percent: float
    cpu_time_user: float
    cpu_time_kernel: float
    thread_count: int

    # Memory metrics
    memory_working_set: int
    memory_private_bytes: int
    memory_virtual_size: int
    memory_peak_working_set: int
    memory_leak_detected: bool

    # I/O metrics
    io_read_bytes: int
    io_write_bytes: int
    io_read_operations: int
    io_write_operations: int
    io_read_rate_mbps: float
    io_write_rate_mbps: float

    # Network metrics
    network_bytes_sent: int
    network_bytes_received: int
    network_connections_active: int
    network_send_rate_mbps: float
    network_receive_rate_mbps: float
    network_destinations: List[str]

@dataclass
class BehavioralPattern:
    """Detected behavioral pattern"""
    pattern_type: str
    description: str
    confidence_score: float
    first_detected: datetime
    last_detected: datetime
    occurrence_count: int
    indicators: List[str]
    metadata: Dict[str, Any]

    # Pattern-specific data
    files_encrypted: int = 0
    files_accessed: int = 0
    shadow_copies_deleted: int = 0
    security_tools_disabled: List[str] = None
    child_processes_created: int = 0
    services_installed: List[str] = None
    injection_techniques: List[str] = None

@dataclass
class ProcessAnalysis:
    """Process analysis result"""
    process_id: int
    parent_process_id: int
    process_name: str
    executable_path: str
    command_line: str
    creation_time: datetime
    child_processes: List[int]
    loaded_modules: List[str]
    is_injected: bool
    is_suspicious: bool
    injection_indicators: List[str]
    threat_score: float

@dataclass
class MemoryAnalysisResult:
    """Memory analysis result"""
    process_id: int
    timestamp: datetime
    dump_file_path: str
    dump_size: int
    injection_detected: bool
    stealth_techniques_detected: bool
    extracted_keys: List[str]
    extracted_certificates: List[str]
    network_artifacts: List[str]
    volatility_results: Dict[str, Any]
    suspicious_regions: List[Dict[str, Any]]

class BehavioralMemoryAnalyzer:
    """
    Advanced Behavioral and Memory Analysis Engine

    Combines behavioral pattern detection with memory forensics
    for comprehensive threat analysis
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Behavioral and Memory Analyzer

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.BehavioralMemoryAnalyzer")

        # Analysis configuration
        self.analysis_config = config.get("behavioral_analysis", {})
        self.memory_config = config.get("memory_analysis", {})

        # Component settings
        self.behavioral_enabled = self.analysis_config.get("enabled", True)
        self.memory_enabled = self.memory_config.get("enabled", True)
        self.real_time_analysis = self.analysis_config.get("real_time_analysis", True)
        self.volatility_enabled = self.memory_config.get("volatility_enabled", True)

        # Data storage
        self.resource_metrics: deque = deque(maxlen=10000)
        self.behavioral_patterns: List[BehavioralPattern] = []
        self.process_analyses: Dict[int, ProcessAnalysis] = {}
        self.memory_analyses: List[MemoryAnalysisResult] = []

        # Analysis state
        self.monitoring_active = False
        self.analysis_threads = []
        self.data_lock = threading.Lock()

        # Thresholds and patterns
        self.resource_thresholds = self._load_resource_thresholds()
        self.behavioral_signatures = self._load_behavioral_signatures()

        # Performance tracking
        self.performance_stats = {
            "total_processes_analyzed": 0,
            "behavioral_patterns_detected": 0,
            "memory_dumps_created": 0,
            "injections_detected": 0,
            "stealth_techniques_detected": 0
        }

        # Initialize C++ bridge
        self.cpp_bridge = None
        self._init_cpp_bridge()

        # Volatility integration
        self.volatility_path = self.memory_config.get("volatility_path", "/usr/local/bin/vol.py")
        self.volatility_profile = self.memory_config.get("volatility_profile", "Win10x64_19041")

    def _init_cpp_bridge(self):
        """Initialize C++ behavioral and memory analysis components"""
        try:
            # Try to load the C++ analysis library
            lib_paths = [
                "scanner_core/cpp/build/lib/libsbards_behavioral.so",
                "scanner_core/cpp/build/lib/libsbards_behavioral.dll",
                "scanner_core/cpp/build/lib/libsbards_behavioral.dylib"
            ]

            for lib_path in lib_paths:
                if os.path.exists(lib_path):
                    try:
                        self.cpp_bridge = cdll.LoadLibrary(lib_path)
                        self._setup_cpp_functions()
                        self.logger.info(f"Loaded C++ behavioral analysis library: {lib_path}")
                        return
                    except Exception as e:
                        self.logger.warning(f"Failed to load {lib_path}: {e}")

            self.logger.warning("C++ behavioral analysis library not found, using Python-only analysis")

        except Exception as e:
            self.logger.error(f"Failed to initialize C++ bridge: {e}")

    def _setup_cpp_functions(self):
        """Setup C++ function signatures"""
        if not self.cpp_bridge:
            return

        try:
            # Behavioral analysis functions
            self.cpp_bridge.start_behavioral_monitoring.argtypes = [c_uint32]
            self.cpp_bridge.start_behavioral_monitoring.restype = c_bool

            self.cpp_bridge.get_resource_usage.argtypes = [c_uint32, c_char_p, c_int]
            self.cpp_bridge.get_resource_usage.restype = c_int

            self.cpp_bridge.detect_behavioral_patterns.argtypes = [c_uint32, c_char_p, c_int]
            self.cpp_bridge.detect_behavioral_patterns.restype = c_int

            # Memory analysis functions
            self.cpp_bridge.create_memory_dump.argtypes = [c_uint32, c_char_p]
            self.cpp_bridge.create_memory_dump.restype = c_bool

            self.cpp_bridge.detect_memory_injection.argtypes = [c_uint32, c_char_p, c_int]
            self.cpp_bridge.detect_memory_injection.restype = c_int

            self.cpp_bridge.analyze_memory_regions.argtypes = [c_uint32, c_char_p, c_int]
            self.cpp_bridge.analyze_memory_regions.restype = c_int

            self.logger.info("C++ function signatures configured")

        except Exception as e:
            self.logger.error(f"Failed to setup C++ functions: {e}")
            self.cpp_bridge = None

    def _load_resource_thresholds(self) -> Dict[str, float]:
        """Load resource usage thresholds"""
        return {
            "cpu_usage_percent": self.analysis_config.get("cpu_threshold", 80.0),
            "memory_usage_mb": self.analysis_config.get("memory_threshold_mb", 2048.0),
            "io_rate_mbps": self.analysis_config.get("io_threshold_mbps", 100.0),
            "network_rate_mbps": self.analysis_config.get("network_threshold_mbps", 50.0),
            "memory_leak_mb": self.analysis_config.get("memory_leak_threshold_mb", 100.0)
        }

    def _load_behavioral_signatures(self) -> Dict[str, List[str]]:
        """Load behavioral pattern signatures"""
        return {
            "ransomware": [
                "bulk_file_encryption", "shadow_copy_deletion", "backup_deletion",
                "crypto_api_usage", "ransom_note_creation", "file_extension_changes"
            ],
            "apt": [
                "lateral_movement", "credential_harvesting", "data_staging",
                "encrypted_c2_traffic", "living_off_land", "persistence_establishment"
            ],
            "banking_trojan": [
                "browser_injection", "form_grabbing", "certificate_theft",
                "keylogging", "screen_capture", "financial_data_theft"
            ],
            "process_injection": [
                "dll_injection", "process_hollowing", "atom_bombing",
                "manual_dll_loading", "reflective_dll_loading", "thread_hijacking"
            ],
            "stealth_techniques": [
                "rootkit_behavior", "anti_debugging", "vm_evasion",
                "sandbox_evasion", "packing_obfuscation", "code_injection"
            ]
        }

    async def start_analysis(self, target_process_id: Optional[int] = None) -> bool:
        """
        Start comprehensive behavioral and memory analysis

        Args:
            target_process_id: Specific process to analyze (None for system-wide)

        Returns:
            True if analysis started successfully
        """
        try:
            self.logger.info("Starting behavioral and memory analysis...")

            # Start C++ analysis components
            if self.cpp_bridge:
                success = await self._start_cpp_analysis(target_process_id)
                if not success:
                    self.logger.warning("C++ analysis failed, using Python-only analysis")

            # Start Python analysis components
            await self._start_python_analysis(target_process_id)

            # Start analysis threads
            self._start_analysis_threads()

            self.monitoring_active = True
            self.logger.info("Behavioral and memory analysis started successfully")

            return True

        except Exception as e:
            self.logger.error(f"Failed to start analysis: {e}")
            return False

    async def stop_analysis(self):
        """Stop all analysis"""
        try:
            self.logger.info("Stopping behavioral and memory analysis...")

            self.monitoring_active = False

            # Stop C++ analysis
            if self.cpp_bridge:
                await self._stop_cpp_analysis()

            # Stop Python analysis
            await self._stop_python_analysis()

            # Wait for analysis threads to complete
            for thread in self.analysis_threads:
                if thread.is_alive():
                    thread.join(timeout=5)

            self.logger.info("Behavioral and memory analysis stopped")

        except Exception as e:
            self.logger.error(f"Failed to stop analysis: {e}")

    async def _start_cpp_analysis(self, target_process_id: Optional[int]) -> bool:
        """Start C++ analysis components"""
        try:
            success = True

            # Start behavioral monitoring
            if self.behavioral_enabled:
                pid = target_process_id or 0
                if not self.cpp_bridge.start_behavioral_monitoring(pid):
                    self.logger.error("Failed to start C++ behavioral monitoring")
                    success = False
                else:
                    self.logger.info("C++ behavioral monitoring started")

            return success

        except Exception as e:
            self.logger.error(f"Failed to start C++ analysis: {e}")
            return False

    async def _stop_cpp_analysis(self):
        """Stop C++ analysis components"""
        try:
            if self.behavioral_enabled:
                # Stop behavioral monitoring (would need stop function in C++)
                pass

            self.logger.info("C++ analysis components stopped")

        except Exception as e:
            self.logger.error(f"Failed to stop C++ analysis: {e}")

    async def _start_python_analysis(self, target_process_id: Optional[int]):
        """Start Python analysis components"""
        try:
            # Initialize process monitoring
            if target_process_id:
                self._start_process_analysis(target_process_id)
            else:
                self._start_system_analysis()

            self.logger.info("Python analysis components started")

        except Exception as e:
            self.logger.error(f"Failed to start Python analysis: {e}")

    async def _stop_python_analysis(self):
        """Stop Python analysis components"""
        try:
            # Python analysis cleanup
            self.logger.info("Python analysis components stopped")

        except Exception as e:
            self.logger.error(f"Failed to stop Python analysis: {e}")

    def _start_analysis_threads(self):
        """Start analysis threads"""

        def resource_monitoring_loop():
            """Resource usage monitoring"""
            while self.monitoring_active:
                try:
                    self._collect_resource_metrics()
                    self._analyze_resource_patterns()
                    time.sleep(1)  # Monitor every second
                except Exception as e:
                    self.logger.error(f"Resource monitoring error: {e}")
                    time.sleep(5)

        def behavioral_analysis_loop():
            """Behavioral pattern analysis"""
            while self.monitoring_active:
                try:
                    self._analyze_behavioral_patterns()
                    self._correlate_behavioral_events()
                    time.sleep(5)  # Analyze every 5 seconds
                except Exception as e:
                    self.logger.error(f"Behavioral analysis error: {e}")
                    time.sleep(10)

        def memory_analysis_loop():
            """Memory analysis and forensics"""
            while self.monitoring_active:
                try:
                    self._perform_memory_analysis()
                    self._detect_memory_injections()
                    time.sleep(30)  # Analyze every 30 seconds
                except Exception as e:
                    self.logger.error(f"Memory analysis error: {e}")
                    time.sleep(60)

        def process_analysis_loop():
            """Process and service analysis"""
            while self.monitoring_active:
                try:
                    self._analyze_process_behavior()
                    self._detect_process_injection()
                    self._monitor_service_changes()
                    time.sleep(10)  # Analyze every 10 seconds
                except Exception as e:
                    self.logger.error(f"Process analysis error: {e}")
                    time.sleep(15)

        # Start analysis threads
        threads = [
            threading.Thread(target=resource_monitoring_loop, daemon=True),
            threading.Thread(target=behavioral_analysis_loop, daemon=True),
            threading.Thread(target=memory_analysis_loop, daemon=True),
            threading.Thread(target=process_analysis_loop, daemon=True)
        ]

        for thread in threads:
            thread.start()
            self.analysis_threads.append(thread)

    def _start_process_analysis(self, process_id: int):
        """Start analysis for specific process"""
        def monitor_process():
            try:
                process = psutil.Process(process_id)

                while self.monitoring_active:
                    try:
                        # Collect process metrics
                        metrics = self._collect_process_metrics(process)

                        with self.data_lock:
                            self.resource_metrics.append(metrics)

                        # Analyze for suspicious behavior
                        self._analyze_process_for_threats(process)

                        time.sleep(2)  # Monitor every 2 seconds

                    except psutil.NoSuchProcess:
                        break
                    except Exception as e:
                        self.logger.error(f"Process monitoring error: {e}")
                        time.sleep(5)

            except Exception as e:
                self.logger.error(f"Failed to monitor process {process_id}: {e}")

        thread = threading.Thread(target=monitor_process, daemon=True)
        thread.start()
        self.analysis_threads.append(thread)

    def _start_system_analysis(self):
        """Start system-wide analysis"""
        def monitor_system():
            while self.monitoring_active:
                try:
                    # Monitor all processes
                    for process in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info']):
                        try:
                            metrics = self._collect_process_metrics(process)

                            with self.data_lock:
                                self.resource_metrics.append(metrics)

                            # Check for suspicious behavior
                            if self._is_process_suspicious(process):
                                self._analyze_process_for_threats(process)

                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue

                    time.sleep(5)  # Monitor every 5 seconds for system-wide

                except Exception as e:
                    self.logger.error(f"System monitoring error: {e}")
                    time.sleep(10)

        thread = threading.Thread(target=monitor_system, daemon=True)
        thread.start()
        self.analysis_threads.append(thread)

    def _collect_resource_metrics(self):
        """Collect system resource metrics"""
        try:
            if not psutil:
                # Fallback when psutil is not available
                self.logger.warning("psutil not available, using fallback metrics")
                fallback_metrics = ResourceUsageMetrics(
                    timestamp=datetime.now(),
                    process_id=os.getpid(),
                    process_name="sbards_analyzer",
                    cpu_usage_percent=10.0,  # Simulated values
                    cpu_time_user=0.0,
                    cpu_time_kernel=0.0,
                    thread_count=1,
                    memory_working_set=50 * 1024 * 1024,  # 50MB
                    memory_private_bytes=50 * 1024 * 1024,
                    memory_virtual_size=100 * 1024 * 1024,
                    memory_peak_working_set=50 * 1024 * 1024,
                    memory_leak_detected=False,
                    io_read_bytes=1024,
                    io_write_bytes=1024,
                    io_read_operations=10,
                    io_write_operations=10,
                    io_read_rate_mbps=0.1,
                    io_write_rate_mbps=0.1,
                    network_bytes_sent=512,
                    network_bytes_received=512,
                    network_connections_active=1,
                    network_send_rate_mbps=0.01,
                    network_receive_rate_mbps=0.01,
                    network_destinations=["127.0.0.1"]
                )

                with self.data_lock:
                    self.resource_metrics.append(fallback_metrics)
                return

            # Get system-wide metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk_io = psutil.disk_io_counters()
            network_io = psutil.net_io_counters()

            # Collect per-process metrics
            for process in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'io_counters']):
                try:
                    metrics = self._collect_process_metrics(process)

                    with self.data_lock:
                        self.resource_metrics.append(metrics)

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

        except Exception as e:
            self.logger.error(f"Failed to collect resource metrics: {e}")

    def _collect_process_metrics(self, process) -> ResourceUsageMetrics:
        """Collect metrics for specific process"""
        try:
            if not psutil:
                # Fallback when psutil is not available
                return ResourceUsageMetrics(
                    timestamp=datetime.now(),
                    process_id=0,
                    process_name="Unknown",
                    cpu_usage_percent=0.0,
                    cpu_time_user=0.0,
                    cpu_time_kernel=0.0,
                    thread_count=0,
                    memory_working_set=0,
                    memory_private_bytes=0,
                    memory_virtual_size=0,
                    memory_peak_working_set=0,
                    memory_leak_detected=False,
                    io_read_bytes=0,
                    io_write_bytes=0,
                    io_read_operations=0,
                    io_write_operations=0,
                    io_read_rate_mbps=0.0,
                    io_write_rate_mbps=0.0,
                    network_bytes_sent=0,
                    network_bytes_received=0,
                    network_connections_active=0,
                    network_send_rate_mbps=0.0,
                    network_receive_rate_mbps=0.0,
                    network_destinations=[]
                )

            info = process.as_dict(['pid', 'name', 'cpu_percent', 'memory_info',
                                  'io_counters', 'num_threads', 'connections'])

            # Calculate network destinations
            network_destinations = []
            if info.get('connections'):
                for conn in info['connections']:
                    if hasattr(conn, 'raddr') and conn.raddr:
                        network_destinations.append(conn.raddr.ip)

            # Calculate I/O rates (simplified)
            io_counters = info.get('io_counters')
            io_read_rate = 0.0
            io_write_rate = 0.0
            if io_counters:
                # Would need previous values to calculate actual rates
                io_read_rate = io_counters.read_bytes / (1024 * 1024)  # Convert to MB
                io_write_rate = io_counters.write_bytes / (1024 * 1024)

            memory_info = info.get('memory_info', psutil.pmem(rss=0, vms=0))

            return ResourceUsageMetrics(
                timestamp=datetime.now(),
                process_id=info['pid'],
                process_name=info['name'] or 'Unknown',
                cpu_usage_percent=info.get('cpu_percent', 0.0),
                cpu_time_user=0.0,  # Would need process.cpu_times()
                cpu_time_kernel=0.0,
                thread_count=info.get('num_threads', 0),
                memory_working_set=memory_info.rss,
                memory_private_bytes=memory_info.rss,
                memory_virtual_size=memory_info.vms,
                memory_peak_working_set=memory_info.rss,
                memory_leak_detected=False,
                io_read_bytes=io_counters.read_bytes if io_counters else 0,
                io_write_bytes=io_counters.write_bytes if io_counters else 0,
                io_read_operations=io_counters.read_count if io_counters else 0,
                io_write_operations=io_counters.write_count if io_counters else 0,
                io_read_rate_mbps=io_read_rate,
                io_write_rate_mbps=io_write_rate,
                network_bytes_sent=0,  # Would need network monitoring
                network_bytes_received=0,
                network_connections_active=len(info.get('connections', [])),
                network_send_rate_mbps=0.0,
                network_receive_rate_mbps=0.0,
                network_destinations=network_destinations
            )

        except Exception as e:
            self.logger.error(f"Failed to collect process metrics: {e}")
            return ResourceUsageMetrics(
                timestamp=datetime.now(),
                process_id=0,
                process_name="Unknown",
                cpu_usage_percent=0.0,
                cpu_time_user=0.0,
                cpu_time_kernel=0.0,
                thread_count=0,
                memory_working_set=0,
                memory_private_bytes=0,
                memory_virtual_size=0,
                memory_peak_working_set=0,
                memory_leak_detected=False,
                io_read_bytes=0,
                io_write_bytes=0,
                io_read_operations=0,
                io_write_operations=0,
                io_read_rate_mbps=0.0,
                io_write_rate_mbps=0.0,
                network_bytes_sent=0,
                network_bytes_received=0,
                network_connections_active=0,
                network_send_rate_mbps=0.0,
                network_receive_rate_mbps=0.0,
                network_destinations=[]
            )

    def _analyze_resource_patterns(self):
        """Analyze resource usage patterns for anomalies"""
        try:
            with self.data_lock:
                recent_metrics = list(self.resource_metrics)[-100:]  # Last 100 entries

            if len(recent_metrics) < 10:
                return

            # Group by process
            process_metrics = defaultdict(list)
            for metric in recent_metrics:
                process_metrics[metric.process_id].append(metric)

            # Analyze each process
            for process_id, metrics in process_metrics.items():
                self._analyze_process_resource_patterns(process_id, metrics)

        except Exception as e:
            self.logger.error(f"Resource pattern analysis failed: {e}")

    def _analyze_process_resource_patterns(self, process_id: int, metrics: List[ResourceUsageMetrics]):
        """Analyze resource patterns for specific process"""
        try:
            if len(metrics) < 5:
                return

            latest_metric = metrics[-1]

            # CPU usage analysis
            cpu_values = [m.cpu_usage_percent for m in metrics]
            avg_cpu = np.mean(cpu_values)
            max_cpu = np.max(cpu_values)

            if max_cpu > self.resource_thresholds["cpu_usage_percent"]:
                self._create_behavioral_pattern(
                    "high_cpu_usage",
                    f"Process {latest_metric.process_name} showing high CPU usage: {max_cpu:.1f}%",
                    0.7,
                    [f"Max CPU: {max_cpu:.1f}%", f"Avg CPU: {avg_cpu:.1f}%"],
                    {"process_id": process_id, "max_cpu": max_cpu}
                )

            # Memory usage analysis
            memory_values = [m.memory_working_set / (1024*1024) for m in metrics]  # Convert to MB
            current_memory = memory_values[-1]
            memory_growth = memory_values[-1] - memory_values[0] if len(memory_values) > 1 else 0

            if current_memory > self.resource_thresholds["memory_usage_mb"]:
                self._create_behavioral_pattern(
                    "high_memory_usage",
                    f"Process {latest_metric.process_name} using excessive memory: {current_memory:.1f}MB",
                    0.8,
                    [f"Memory usage: {current_memory:.1f}MB", f"Growth: {memory_growth:.1f}MB"],
                    {"process_id": process_id, "memory_mb": current_memory}
                )

            # Memory leak detection
            if memory_growth > self.resource_thresholds["memory_leak_mb"] and len(metrics) >= 10:
                self._create_behavioral_pattern(
                    "memory_leak",
                    f"Potential memory leak in {latest_metric.process_name}: {memory_growth:.1f}MB growth",
                    0.9,
                    ["Continuous memory growth", f"Growth: {memory_growth:.1f}MB"],
                    {"process_id": process_id, "growth_mb": memory_growth}
                )

            # I/O analysis
            io_read_rates = [m.io_read_rate_mbps for m in metrics]
            io_write_rates = [m.io_write_rate_mbps for m in metrics]
            max_read_rate = np.max(io_read_rates)
            max_write_rate = np.max(io_write_rates)

            if max_read_rate > self.resource_thresholds["io_rate_mbps"] or max_write_rate > self.resource_thresholds["io_rate_mbps"]:
                self._create_behavioral_pattern(
                    "high_io_activity",
                    f"High I/O activity in {latest_metric.process_name}",
                    0.6,
                    [f"Read rate: {max_read_rate:.1f}MB/s", f"Write rate: {max_write_rate:.1f}MB/s"],
                    {"process_id": process_id, "read_rate": max_read_rate, "write_rate": max_write_rate}
                )

        except Exception as e:
            self.logger.error(f"Process resource pattern analysis failed: {e}")

    def _analyze_behavioral_patterns(self):
        """Analyze for specific behavioral patterns"""
        try:
            # Detect file encryption patterns
            self._detect_file_encryption_patterns()

            # Detect shadow copy deletion
            self._detect_shadow_copy_deletion()

            # Detect security tool interference
            self._detect_security_tool_interference()

            # Detect lateral movement
            self._detect_lateral_movement()

            # Detect data exfiltration
            self._detect_data_exfiltration()

        except Exception as e:
            self.logger.error(f"Behavioral pattern analysis failed: {e}")

    def _detect_file_encryption_patterns(self):
        """Detect ransomware file encryption patterns"""
        try:
            # This would integrate with file monitoring
            # For now, simulate detection based on I/O patterns

            with self.data_lock:
                recent_metrics = list(self.resource_metrics)[-50:]

            # Look for processes with high write activity
            high_write_processes = []
            for metric in recent_metrics:
                if metric.io_write_rate_mbps > 50:  # High write threshold
                    high_write_processes.append(metric)

            # Group by process and check for sustained activity
            process_writes = defaultdict(list)
            for metric in high_write_processes:
                process_writes[metric.process_id].append(metric)

            for process_id, writes in process_writes.items():
                if len(writes) > 10:  # Sustained high write activity
                    latest = writes[-1]
                    total_writes = sum(w.io_write_bytes for w in writes)

                    self._create_behavioral_pattern(
                        "potential_file_encryption",
                        f"Potential file encryption activity in {latest.process_name}",
                        0.8,
                        ["High sustained write activity", f"Total writes: {total_writes/(1024*1024):.1f}MB"],
                        {"process_id": process_id, "write_bytes": total_writes},
                        files_encrypted=len(writes)
                    )

        except Exception as e:
            self.logger.error(f"File encryption pattern detection failed: {e}")

    def _detect_shadow_copy_deletion(self):
        """Detect shadow copy deletion attempts"""
        try:
            # This would integrate with command line monitoring
            # For now, check for suspicious process names

            suspicious_commands = [
                "vssadmin", "wmic", "bcdedit", "wbadmin", "diskshadow"
            ]

            with self.data_lock:
                recent_metrics = list(self.resource_metrics)[-100:]

            for metric in recent_metrics:
                process_name = metric.process_name.lower()
                if any(cmd in process_name for cmd in suspicious_commands):
                    self._create_behavioral_pattern(
                        "shadow_copy_deletion",
                        f"Potential shadow copy deletion: {metric.process_name}",
                        0.9,
                        ["Suspicious process name", "Shadow copy manipulation"],
                        {"process_id": metric.process_id, "process_name": metric.process_name},
                        shadow_copies_deleted=1
                    )

        except Exception as e:
            self.logger.error(f"Shadow copy deletion detection failed: {e}")

    def _detect_security_tool_interference(self):
        """Detect attempts to disable security tools"""
        try:
            security_processes = [
                "msmpseng", "avgnt", "avguard", "bdagent", "ccsvchst",
                "ntrtscan", "mcshield", "windefend", "msmpeng"
            ]

            # Check for terminated security processes
            current_processes = set()
            with self.data_lock:
                recent_metrics = list(self.resource_metrics)[-50:]
                for metric in recent_metrics:
                    current_processes.add(metric.process_name.lower())

            missing_security = []
            for sec_proc in security_processes:
                if sec_proc not in current_processes:
                    missing_security.append(sec_proc)

            if missing_security:
                self._create_behavioral_pattern(
                    "security_tool_interference",
                    f"Security processes not running: {', '.join(missing_security)}",
                    0.7,
                    ["Missing security processes", "Potential AV disabling"],
                    {"missing_processes": missing_security},
                    security_tools_disabled=missing_security
                )

        except Exception as e:
            self.logger.error(f"Security tool interference detection failed: {e}")

    def _detect_lateral_movement(self):
        """Detect lateral movement attempts"""
        try:
            # Look for network connections to multiple internal IPs
            network_destinations = defaultdict(set)

            with self.data_lock:
                recent_metrics = list(self.resource_metrics)[-100:]

            for metric in recent_metrics:
                for dest in metric.network_destinations:
                    # Check for internal IP ranges
                    if (dest.startswith("192.168.") or dest.startswith("10.") or
                        dest.startswith("172.")):
                        network_destinations[metric.process_id].add(dest)

            # Check for processes connecting to many internal IPs
            for process_id, destinations in network_destinations.items():
                if len(destinations) > 5:  # Threshold for lateral movement
                    self._create_behavioral_pattern(
                        "lateral_movement",
                        f"Potential lateral movement: connections to {len(destinations)} internal IPs",
                        0.8,
                        ["Multiple internal connections", f"Destinations: {len(destinations)}"],
                        {"process_id": process_id, "destinations": list(destinations)}
                    )

        except Exception as e:
            self.logger.error(f"Lateral movement detection failed: {e}")

    def _detect_data_exfiltration(self):
        """Detect data exfiltration patterns"""
        try:
            # Look for high outbound network activity
            with self.data_lock:
                recent_metrics = list(self.resource_metrics)[-50:]

            high_network_processes = []
            for metric in recent_metrics:
                if metric.network_send_rate_mbps > 10:  # High upload threshold
                    high_network_processes.append(metric)

            # Group by process
            process_uploads = defaultdict(list)
            for metric in high_network_processes:
                process_uploads[metric.process_id].append(metric)

            for process_id, uploads in process_uploads.items():
                if len(uploads) > 5:  # Sustained upload activity
                    latest = uploads[-1]
                    total_sent = sum(u.network_bytes_sent for u in uploads)

                    self._create_behavioral_pattern(
                        "data_exfiltration",
                        f"Potential data exfiltration by {latest.process_name}",
                        0.7,
                        ["High sustained upload activity", f"Data sent: {total_sent/(1024*1024):.1f}MB"],
                        {"process_id": process_id, "bytes_sent": total_sent}
                    )

        except Exception as e:
            self.logger.error(f"Data exfiltration detection failed: {e}")

    def _create_behavioral_pattern(self, pattern_type: str, description: str, confidence: float,
                                 indicators: List[str], metadata: Dict[str, Any], **kwargs):
        """Create and store behavioral pattern"""
        try:
            pattern = BehavioralPattern(
                pattern_type=pattern_type,
                description=description,
                confidence_score=confidence,
                first_detected=datetime.now(),
                last_detected=datetime.now(),
                occurrence_count=1,
                indicators=indicators,
                metadata=metadata,
                **kwargs
            )

            with self.data_lock:
                # Check if similar pattern already exists
                existing_pattern = None
                for p in self.behavioral_patterns:
                    if (p.pattern_type == pattern_type and
                        p.metadata.get("process_id") == metadata.get("process_id")):
                        existing_pattern = p
                        break

                if existing_pattern:
                    # Update existing pattern
                    existing_pattern.last_detected = datetime.now()
                    existing_pattern.occurrence_count += 1
                    existing_pattern.confidence_score = max(existing_pattern.confidence_score, confidence)
                else:
                    # Add new pattern
                    self.behavioral_patterns.append(pattern)
                    self.performance_stats["behavioral_patterns_detected"] += 1

            self.logger.warning(f"Behavioral pattern detected: {description}")

        except Exception as e:
            self.logger.error(f"Failed to create behavioral pattern: {e}")

    def _correlate_behavioral_events(self):
        """Correlate behavioral events for advanced threat detection"""
        try:
            with self.data_lock:
                recent_patterns = [p for p in self.behavioral_patterns
                                 if (datetime.now() - p.last_detected).seconds < 300]  # Last 5 minutes

            if len(recent_patterns) < 2:
                return

            # Group patterns by process
            process_patterns = defaultdict(list)
            for pattern in recent_patterns:
                process_id = pattern.metadata.get("process_id", 0)
                process_patterns[process_id].append(pattern)

            # Analyze pattern combinations
            for process_id, patterns in process_patterns.items():
                self._analyze_pattern_combinations(process_id, patterns)

        except Exception as e:
            self.logger.error(f"Behavioral event correlation failed: {e}")

    def _analyze_pattern_combinations(self, process_id: int, patterns: List[BehavioralPattern]):
        """Analyze combinations of patterns for advanced threats"""
        try:
            pattern_types = [p.pattern_type for p in patterns]

            # Ransomware detection
            ransomware_indicators = ["potential_file_encryption", "shadow_copy_deletion", "security_tool_interference"]
            if len(set(pattern_types) & set(ransomware_indicators)) >= 2:
                self._create_behavioral_pattern(
                    "ransomware_activity",
                    f"Ransomware activity detected in process {process_id}",
                    0.95,
                    ["Multiple ransomware indicators", "Pattern correlation"],
                    {"process_id": process_id, "correlated_patterns": pattern_types}
                )

            # APT detection
            apt_indicators = ["lateral_movement", "data_exfiltration", "high_memory_usage"]
            if len(set(pattern_types) & set(apt_indicators)) >= 2:
                self._create_behavioral_pattern(
                    "apt_activity",
                    f"APT activity detected in process {process_id}",
                    0.9,
                    ["Multiple APT indicators", "Advanced threat pattern"],
                    {"process_id": process_id, "correlated_patterns": pattern_types}
                )

        except Exception as e:
            self.logger.error(f"Pattern combination analysis failed: {e}")

    def _perform_memory_analysis(self):
        """Perform memory analysis and forensics"""
        try:
            if not self.memory_enabled:
                return

            # Get processes to analyze
            processes_to_analyze = []
            with self.data_lock:
                # Analyze suspicious processes
                for pattern in self.behavioral_patterns:
                    process_id = pattern.metadata.get("process_id")
                    if process_id and process_id not in processes_to_analyze:
                        processes_to_analyze.append(process_id)

            # Limit to most recent suspicious processes
            processes_to_analyze = processes_to_analyze[-5:]

            for process_id in processes_to_analyze:
                try:
                    self._analyze_process_memory(process_id)
                except Exception as e:
                    self.logger.error(f"Memory analysis failed for process {process_id}: {e}")

        except Exception as e:
            self.logger.error(f"Memory analysis failed: {e}")

    def _analyze_process_memory(self, process_id: int):
        """Analyze memory of specific process"""
        try:
            # Create memory dump
            dump_result = self._create_memory_dump(process_id)
            if not dump_result:
                return

            # Analyze dump for injections
            injection_detected = self._detect_memory_injections(process_id)

            # Extract forensic artifacts
            keys = self._extract_encryption_keys(process_id)
            certificates = self._extract_certificates(process_id)
            network_artifacts = self._extract_network_artifacts(process_id)

            # Run Volatility analysis if enabled
            volatility_results = {}
            if self.volatility_enabled and dump_result.get("dump_path"):
                volatility_results = self._run_volatility_analysis(dump_result["dump_path"])

            # Create analysis result
            analysis_result = MemoryAnalysisResult(
                process_id=process_id,
                timestamp=datetime.now(),
                dump_file_path=dump_result.get("dump_path", ""),
                dump_size=dump_result.get("size", 0),
                injection_detected=injection_detected,
                stealth_techniques_detected=False,  # Would implement stealth detection
                extracted_keys=keys,
                extracted_certificates=certificates,
                network_artifacts=network_artifacts,
                volatility_results=volatility_results,
                suspicious_regions=[]  # Would implement region analysis
            )

            with self.data_lock:
                self.memory_analyses.append(analysis_result)
                self.performance_stats["memory_dumps_created"] += 1
                if injection_detected:
                    self.performance_stats["injections_detected"] += 1

            self.logger.info(f"Memory analysis completed for process {process_id}")

        except Exception as e:
            self.logger.error(f"Process memory analysis failed: {e}")

    def _create_memory_dump(self, process_id: int) -> Dict[str, Any]:
        """Create memory dump of process"""
        try:
            if self.cpp_bridge:
                # Use C++ implementation
                dump_path = f"memory_dumps/process_{process_id}_{int(time.time())}.dmp"
                os.makedirs(os.path.dirname(dump_path), exist_ok=True)

                success = self.cpp_bridge.create_memory_dump(process_id, dump_path.encode('utf-8'))
                if success:
                    size = os.path.getsize(dump_path) if os.path.exists(dump_path) else 0
                    return {"dump_path": dump_path, "size": size}

            # Fallback to Python implementation (limited)
            try:
                process = psutil.Process(process_id)
                memory_info = process.memory_info()

                # Create simplified dump info
                dump_path = f"memory_dumps/process_{process_id}_{int(time.time())}.json"
                os.makedirs(os.path.dirname(dump_path), exist_ok=True)

                dump_data = {
                    "process_id": process_id,
                    "process_name": process.name(),
                    "memory_info": {
                        "rss": memory_info.rss,
                        "vms": memory_info.vms
                    },
                    "timestamp": datetime.now().isoformat()
                }

                with open(dump_path, 'w') as f:
                    json.dump(dump_data, f, indent=2)

                return {"dump_path": dump_path, "size": os.path.getsize(dump_path)}

            except psutil.NoSuchProcess:
                self.logger.warning(f"Process {process_id} no longer exists")
                return {}

        except Exception as e:
            self.logger.error(f"Memory dump creation failed: {e}")
            return {}

    def _detect_memory_injections(self, process_id: int) -> bool:
        """Detect memory injections in process"""
        try:
            if self.cpp_bridge:
                # Use C++ implementation
                buffer = ctypes.create_string_buffer(4096)
                result = self.cpp_bridge.detect_memory_injection(process_id, buffer, 4096)

                if result > 0:
                    injection_data = buffer.value.decode('utf-8')
                    self.logger.warning(f"Memory injection detected in process {process_id}: {injection_data}")
                    return True

            # Fallback to Python heuristics
            try:
                process = psutil.Process(process_id)

                # Check for suspicious memory usage patterns
                memory_info = process.memory_info()
                if memory_info.vms > memory_info.rss * 10:  # High virtual memory ratio
                    self.logger.warning(f"Suspicious memory pattern in process {process_id}")
                    return True

                # Check for unusual number of threads
                if process.num_threads() > 50:  # High thread count
                    self.logger.warning(f"High thread count in process {process_id}: {process.num_threads()}")
                    return True

            except psutil.NoSuchProcess:
                pass

            return False

        except Exception as e:
            self.logger.error(f"Memory injection detection failed: {e}")
            return False

    def _extract_encryption_keys(self, process_id: int) -> List[str]:
        """Extract encryption keys from process memory"""
        try:
            # This would implement key extraction algorithms
            # For now, return placeholder
            return []

        except Exception as e:
            self.logger.error(f"Key extraction failed: {e}")
            return []

    def _extract_certificates(self, process_id: int) -> List[str]:
        """Extract certificates from process memory"""
        try:
            # This would implement certificate extraction
            # For now, return placeholder
            return []

        except Exception as e:
            self.logger.error(f"Certificate extraction failed: {e}")
            return []

    def _extract_network_artifacts(self, process_id: int) -> List[str]:
        """Extract network artifacts from process memory"""
        try:
            # This would implement network artifact extraction
            # For now, return placeholder
            return []

        except Exception as e:
            self.logger.error(f"Network artifact extraction failed: {e}")
            return []

    def _run_volatility_analysis(self, dump_path: str) -> Dict[str, Any]:
        """Run Volatility analysis on memory dump"""
        try:
            if not os.path.exists(self.volatility_path):
                self.logger.warning(f"Volatility not found at {self.volatility_path}")
                return {}

            results = {}

            # Run basic Volatility plugins
            plugins = ["pslist", "psscan", "dlllist", "netscan", "malfind"]

            for plugin in plugins:
                try:
                    cmd = [
                        "python", self.volatility_path,
                        "-f", dump_path,
                        "--profile", self.volatility_profile,
                        plugin
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                    if result.returncode == 0:
                        results[plugin] = result.stdout
                    else:
                        self.logger.warning(f"Volatility plugin {plugin} failed: {result.stderr}")

                except subprocess.TimeoutExpired:
                    self.logger.warning(f"Volatility plugin {plugin} timed out")
                except Exception as e:
                    self.logger.error(f"Volatility plugin {plugin} error: {e}")

            return results

        except Exception as e:
            self.logger.error(f"Volatility analysis failed: {e}")
            return {}

    def _detect_process_injection(self):
        """Detect process injection techniques"""
        try:
            # This would implement process injection detection
            # For now, placeholder implementation
            pass

        except Exception as e:
            self.logger.error(f"Process injection detection failed: {e}")

    def _analyze_process_behavior(self):
        """Analyze process behavior patterns"""
        try:
            # This would implement process behavior analysis
            # For now, placeholder implementation
            pass

        except Exception as e:
            self.logger.error(f"Process behavior analysis failed: {e}")

    def _monitor_service_changes(self):
        """Monitor service installation and changes"""
        try:
            # This would implement service monitoring
            # For now, placeholder implementation
            pass

        except Exception as e:
            self.logger.error(f"Service monitoring failed: {e}")

    def _is_process_suspicious(self, process) -> bool:
        """Check if process exhibits suspicious characteristics"""
        try:
            # Check CPU usage
            if process.info.get('cpu_percent', 0) > 80:
                return True

            # Check memory usage
            memory_info = process.info.get('memory_info')
            if memory_info and memory_info.rss > 1024 * 1024 * 1024:  # 1GB
                return True

            # Check process name
            suspicious_names = ["cmd.exe", "powershell.exe", "rundll32.exe", "regsvr32.exe"]
            if process.info.get('name', '').lower() in suspicious_names:
                return True

            return False

        except Exception as e:
            self.logger.error(f"Process suspicion check failed: {e}")
            return False

    def _analyze_process_for_threats(self, process):
        """Analyze specific process for threats"""
        try:
            process_id = process.pid

            # Create process analysis
            analysis = ProcessAnalysis(
                process_id=process_id,
                parent_process_id=process.ppid(),
                process_name=process.name(),
                executable_path=process.exe() if hasattr(process, 'exe') else '',
                command_line=' '.join(process.cmdline()) if hasattr(process, 'cmdline') else '',
                creation_time=datetime.fromtimestamp(process.create_time()),
                child_processes=[],  # Would need to enumerate children
                loaded_modules=[],   # Would need to enumerate modules
                is_injected=False,   # Would need injection detection
                is_suspicious=self._is_process_suspicious(process),
                injection_indicators=[],
                threat_score=0.0
            )

            with self.data_lock:
                self.process_analyses[process_id] = analysis
                self.performance_stats["total_processes_analyzed"] += 1

        except Exception as e:
            self.logger.error(f"Process threat analysis failed: {e}")

    # Public API methods

    async def get_analysis_results(self) -> Dict[str, Any]:
        """Get comprehensive analysis results"""
        try:
            with self.data_lock:
                results = {
                    "monitoring_active": self.monitoring_active,
                    "performance_stats": dict(self.performance_stats),
                    "resource_metrics": [asdict(m) for m in list(self.resource_metrics)[-100:]],
                    "behavioral_patterns": [asdict(p) for p in self.behavioral_patterns[-50:]],
                    "process_analyses": {pid: asdict(analysis) for pid, analysis in self.process_analyses.items()},
                    "memory_analyses": [asdict(m) for m in self.memory_analyses[-20:]],
                    "timestamp": datetime.now().isoformat()
                }

            return results

        except Exception as e:
            self.logger.error(f"Failed to get analysis results: {e}")
            return {"error": str(e)}

    async def get_threat_summary(self) -> Dict[str, Any]:
        """Get threat analysis summary"""
        try:
            with self.data_lock:
                # Calculate threat levels
                high_threat_patterns = [p for p in self.behavioral_patterns if p.confidence_score > 0.8]
                medium_threat_patterns = [p for p in self.behavioral_patterns if 0.5 < p.confidence_score <= 0.8]

                # Group by threat type
                threat_types = defaultdict(int)
                for pattern in self.behavioral_patterns:
                    threat_types[pattern.pattern_type] += 1

                summary = {
                    "overall_threat_level": self._calculate_overall_threat_level(),
                    "total_patterns": len(self.behavioral_patterns),
                    "high_threat_patterns": len(high_threat_patterns),
                    "medium_threat_patterns": len(medium_threat_patterns),
                    "threat_types": dict(threat_types),
                    "suspicious_processes": len([p for p in self.process_analyses.values() if p.is_suspicious]),
                    "memory_injections": self.performance_stats["injections_detected"],
                    "recommendations": self._generate_recommendations(),
                    "timestamp": datetime.now().isoformat()
                }

            return summary

        except Exception as e:
            self.logger.error(f"Failed to get threat summary: {e}")
            return {"error": str(e)}

    def _calculate_overall_threat_level(self) -> str:
        """Calculate overall threat level"""
        try:
            if not self.behavioral_patterns:
                return "minimal"

            high_threat_count = len([p for p in self.behavioral_patterns if p.confidence_score > 0.8])
            medium_threat_count = len([p for p in self.behavioral_patterns if 0.5 < p.confidence_score <= 0.8])

            if high_threat_count > 5:
                return "critical"
            elif high_threat_count > 2:
                return "high"
            elif high_threat_count > 0 or medium_threat_count > 5:
                return "medium"
            elif medium_threat_count > 0:
                return "low"
            else:
                return "minimal"

        except Exception as e:
            self.logger.error(f"Threat level calculation failed: {e}")
            return "unknown"

    def _generate_recommendations(self) -> List[str]:
        """Generate threat-specific recommendations"""
        recommendations = []

        try:
            # Check for specific threat patterns
            pattern_types = [p.pattern_type for p in self.behavioral_patterns]

            if "ransomware_activity" in pattern_types:
                recommendations.append("CRITICAL: Ransomware activity detected - isolate affected systems immediately")

            if "apt_activity" in pattern_types:
                recommendations.append("HIGH: APT activity detected - initiate incident response procedures")

            if "potential_file_encryption" in pattern_types:
                recommendations.append("Investigate file encryption activity - potential ransomware")

            if "shadow_copy_deletion" in pattern_types:
                recommendations.append("Shadow copy deletion detected - backup systems may be compromised")

            if "security_tool_interference" in pattern_types:
                recommendations.append("Security tools disabled - restore antivirus and security services")

            if "lateral_movement" in pattern_types:
                recommendations.append("Lateral movement detected - check network segmentation and access controls")

            if "data_exfiltration" in pattern_types:
                recommendations.append("Data exfiltration detected - monitor network traffic and block suspicious connections")

            if self.performance_stats["injections_detected"] > 0:
                recommendations.append("Memory injections detected - scan for advanced malware")

            if not recommendations:
                recommendations.append("Continue monitoring - no immediate threats detected")

        except Exception as e:
            self.logger.error(f"Failed to generate recommendations: {e}")
            recommendations.append("Error generating recommendations")

        return recommendations

    def is_monitoring_active(self) -> bool:
        """Check if monitoring is active"""
        return self.monitoring_active

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return {
            "monitoring_active": self.monitoring_active,
            "cpp_bridge_available": self.cpp_bridge is not None,
            "active_threads": len(self.analysis_threads),
            "data_counts": {
                "resource_metrics": len(self.resource_metrics),
                "behavioral_patterns": len(self.behavioral_patterns),
                "process_analyses": len(self.process_analyses),
                "memory_analyses": len(self.memory_analyses)
            },
            "performance_stats": dict(self.performance_stats)
        }
