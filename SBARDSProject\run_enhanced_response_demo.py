#!/usr/bin/env python3
"""
SBARDS Enhanced Response System Demo
Advanced ML-Driven Response Demonstration

This demo showcases:
- Enhanced threat assessment with multiple ML models
- Adaptive threshold adjustment
- Cross-layer integration
- Real-time learning capabilities
- Advanced response strategies
"""

import os
import sys
import json
import logging
import asyncio
from pathlib import Path
from datetime import datetime, timezone
import hashlib

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import enhanced response system
from phases.response.enhanced_response_system import EnhancedResponseSystem

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_response_demo.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("SBARDS.EnhancedDemo")

def load_enhanced_config():
    """Load enhanced response configuration."""
    try:
        config_path = project_root / "phases" / "response" / "enhanced_response_config.json"
        
        if config_path.exists():
            with open(config_path, 'r') as f:
                config = json.load(f)
        else:
            # Default configuration
            config = {
                "enhanced_response": {
                    "adaptive_thresholds_enabled": True,
                    "cross_layer_integration": True,
                    "real_time_learning": True
                },
                "ml_models": {
                    "auto_retrain_enabled": True,
                    "ensemble_enabled": True
                }
            }
        
        # Add base configuration
        config["comprehensive_response"] = {
            "base_directory": str(project_root / "response_data"),
            "blockchain_enabled": True,
            "cpp_integration_enabled": True,
            "notification_enabled": True,
            "forensics_enabled": True
        }
        
        return config
        
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}

def create_sample_analysis_results(threat_level: str = "suspicious") -> dict:
    """Create sample analysis results for demonstration."""
    
    # Generate sample file hash
    sample_content = f"sample_file_{threat_level}_{datetime.now().isoformat()}"
    file_hash = hashlib.sha256(sample_content.encode()).hexdigest()
    
    # Base analysis results
    analysis_results = {
        "file_path": f"C:\\temp\\sample_{threat_level}_file.exe",
        "file_hash": {
            "md5": hashlib.md5(sample_content.encode()).hexdigest(),
            "sha1": hashlib.sha1(sample_content.encode()).hexdigest(),
            "sha256": file_hash
        },
        "file_info": {
            "size": 1024 * 512,  # 512KB
            "mime_type": "application/x-executable",
            "extension": ".exe",
            "creation_time": "2024-12-01T10:00:00Z",
            "modification_time": "2024-12-01T10:00:00Z"
        },
        "static_analysis": {
            "entropy": 7.2 if threat_level in ["malicious", "critical"] else 5.8,
            "packed": threat_level in ["malicious", "critical"],
            "signed": threat_level == "safe",
            "pe_analysis": {
                "sections": [
                    {"name": ".text", "entropy": 6.1, "executable": True, "writable": False},
                    {"name": ".data", "entropy": 4.2, "executable": False, "writable": True},
                    {"name": ".rsrc", "entropy": 7.8 if threat_level == "malicious" else 3.1, "executable": False, "writable": False}
                ],
                "imports": [
                    "kernel32.dll!CreateProcessA",
                    "advapi32.dll!RegSetValueExA" if threat_level != "safe" else "user32.dll!MessageBoxA",
                    "ntdll.dll!NtWriteVirtualMemory" if threat_level == "malicious" else "kernel32.dll!GetModuleHandleA"
                ]
            },
            "strings": [
                "Hello World" if threat_level == "safe" else "C:\\Windows\\System32\\cmd.exe",
                "Normal operation" if threat_level == "safe" else "Persistence established",
                "User interface" if threat_level == "safe" else "Data exfiltration complete"
            ],
            "suspicious_strings": [] if threat_level == "safe" else [
                "CreateRemoteThread",
                "VirtualAllocEx",
                "WriteProcessMemory"
            ]
        },
        "dynamic_analysis": {
            "api_calls": [
                {"api_name": "CreateFileA", "timestamp": 1000},
                {"api_name": "WriteFile", "timestamp": 1100},
                {"api_name": "CreateProcessA" if threat_level != "safe" else "GetCurrentProcess", "timestamp": 1200}
            ],
            "network_connections": [] if threat_level == "safe" else [
                {
                    "ip": "*************" if threat_level == "suspicious" else "**************",
                    "port": 80 if threat_level == "suspicious" else 443,
                    "protocol": "TCP",
                    "bytes_sent": 1024,
                    "bytes_received": 2048,
                    "suspicious": threat_level in ["malicious", "critical"]
                }
            ],
            "file_operations": [
                {
                    "operation": "write",
                    "file_path": "C:\\temp\\output.txt",
                    "suspicious": False
                }
            ] + ([] if threat_level == "safe" else [
                {
                    "operation": "write",
                    "file_path": "C:\\Windows\\System32\\malware.dll",
                    "suspicious": True
                }
            ]),
            "registry_changes": [] if threat_level == "safe" else [
                {
                    "key": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run",
                    "value": "malware",
                    "data": "C:\\temp\\malware.exe",
                    "suspicious": True
                }
            ],
            "process_info": {
                "main_process": {
                    "pid": 1234,
                    "name": "sample.exe",
                    "cpu_usage": 15.5,
                    "memory_usage": 25.2,
                    "suspicious": threat_level in ["malicious", "critical"],
                    "injection_detected": threat_level == "malicious"
                }
            }
        },
        "yara_analysis": {
            "matches": [] if threat_level == "safe" else [
                {
                    "rule_name": f"{threat_level}_malware_family",
                    "family": "trojan" if threat_level == "suspicious" else "ransomware",
                    "confidence": 0.85 if threat_level == "suspicious" else 0.95,
                    "description": f"Detected {threat_level} malware patterns"
                }
            ]
        },
        "network_analysis": {
            "connections": [],
            "dns_queries": [],
            "suspicious_domains": [] if threat_level == "safe" else ["malicious-domain.com"]
        },
        "threat_intelligence": {
            "iocs": [] if threat_level == "safe" else [
                {
                    "type": "hash",
                    "value": file_hash,
                    "classification": threat_level,
                    "confidence": 0.9
                }
            ],
            "attribution": {
                "apt_groups": [] if threat_level != "critical" else ["APT29", "Cozy Bear"]
            },
            "campaigns": [] if threat_level != "malicious" else ["Operation SolarWinds"],
            "ttps": [] if threat_level == "safe" else ["T1055", "T1012", "T1071"]
        },
        "threat_assessment": {
            "overall_threat_level": threat_level,
            "threat_score": {
                "safe": 0.1,
                "suspicious": 0.6,
                "malicious": 0.85,
                "critical": 0.95
            }.get(threat_level, 0.5),
            "confidence": 0.8,
            "risk_factors": [] if threat_level == "safe" else [
                "network_communication",
                "registry_modification",
                "process_injection" if threat_level == "malicious" else "suspicious_api_calls"
            ]
        }
    }
    
    return analysis_results

async def demonstrate_enhanced_response():
    """Demonstrate enhanced response system capabilities."""
    
    print("🚀 SBARDS Enhanced Response System Demo")
    print("=" * 60)
    
    # Load configuration
    print("📋 Loading enhanced configuration...")
    config = load_enhanced_config()
    
    # Initialize enhanced response system
    print("🔧 Initializing Enhanced Response System...")
    enhanced_response = EnhancedResponseSystem(config)
    
    # Test scenarios
    test_scenarios = [
        ("safe", "Safe File Test"),
        ("suspicious", "Suspicious File Test"),
        ("malicious", "Malicious File Test"),
        ("critical", "Critical Threat Test")
    ]
    
    print("\n🧪 Running Enhanced Response Test Scenarios...")
    print("-" * 60)
    
    for threat_level, scenario_name in test_scenarios:
        print(f"\n📊 {scenario_name} ({threat_level.upper()})")
        print("-" * 40)
        
        try:
            # Create sample analysis results
            analysis_results = create_sample_analysis_results(threat_level)
            
            # Process with enhanced response system
            print(f"🔍 Processing {threat_level} file with enhanced ML analysis...")
            
            start_time = datetime.now()
            response_result = await enhanced_response.process_enhanced_analysis_results(analysis_results)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            # Display results
            print(f"⏱️  Processing Time: {processing_time:.2f} seconds")
            
            if "error" in response_result:
                print(f"❌ Error: {response_result['error']}")
                continue
            
            # Enhanced assessment results
            enhanced_assessment = response_result.get("enhanced_assessment", {})
            print(f"🎯 Original Threat Level: {enhanced_assessment.get('original_threat_level', 'unknown')}")
            print(f"🤖 ML Threat Prediction: {enhanced_assessment.get('ml_threat_prediction', 'unknown')}")
            print(f"📊 ML Confidence: {enhanced_assessment.get('ml_confidence', 0.0):.2f}")
            print(f"🦠 Malware Family: {enhanced_assessment.get('malware_family', 'unknown')}")
            print(f"🎭 Evasion Techniques: {', '.join(enhanced_assessment.get('evasion_techniques', ['none']))}")
            print(f"📈 Behavioral Score: {enhanced_assessment.get('behavioral_score', 0.0):.2f}")
            print(f"🚨 Anomaly Detected: {'Yes' if enhanced_assessment.get('anomaly_detected', False) else 'No'}")
            print(f"⚡ Optimal Response: {enhanced_assessment.get('optimal_response', 'unknown')}")
            
            # Response execution results
            response_execution = response_result.get("response_result", {})
            print(f"✅ Response Success: {'Yes' if response_execution.get('success', False) else 'No'}")
            print(f"🔧 ML Enhanced: {'Yes' if response_execution.get('ml_enhanced', False) else 'No'}")
            print(f"📋 Strategy Used: {response_execution.get('ml_strategy', 'unknown')}")
            
            # Risk factors
            risk_factors = enhanced_assessment.get('risk_factors', [])
            if risk_factors:
                print(f"⚠️  Risk Factors: {', '.join(risk_factors[:3])}{'...' if len(risk_factors) > 3 else ''}")
            
            # Mitigation strategies
            mitigation_strategies = enhanced_assessment.get('mitigation_strategies', [])
            if mitigation_strategies:
                print(f"🛡️  Mitigation: {', '.join(mitigation_strategies[:3])}{'...' if len(mitigation_strategies) > 3 else ''}")
            
        except Exception as e:
            print(f"❌ Error in {scenario_name}: {e}")
            logger.error(f"Error in {scenario_name}: {e}")
    
    # Display system statistics
    print("\n📊 Enhanced Response System Statistics")
    print("-" * 60)
    
    try:
        stats = await enhanced_response.get_enhanced_statistics()
        
        # Enhanced metrics
        enhanced_metrics = stats.get("enhanced_response_metrics", {})
        print(f"📈 Total Responses: {enhanced_metrics.get('total_responses', 0)}")
        print(f"🤖 ML Enhanced Responses: {enhanced_metrics.get('ml_enhanced_responses', 0)}")
        print(f"🎯 Accuracy Improvements: {enhanced_metrics.get('accuracy_improvements', 0)}")
        print(f"📉 False Positive Reductions: {enhanced_metrics.get('false_positive_reductions', 0)}")
        
        # Adaptive thresholds
        adaptive_thresholds = stats.get("adaptive_thresholds", {})
        print(f"🎚️  Safe Threshold: {adaptive_thresholds.get('safe_threshold', 0.3):.2f}")
        print(f"🎚️  Suspicious Threshold: {adaptive_thresholds.get('suspicious_threshold', 0.7):.2f}")
        print(f"🎚️  Malicious Threshold: {adaptive_thresholds.get('malicious_threshold', 0.9):.2f}")
        
        # ML model statistics
        ml_stats = stats.get("ml_model_stats", {})
        print(f"🧠 Models Loaded: {ml_stats.get('models_loaded', 0)}")
        print(f"🔄 Auto Retrain Enabled: {'Yes' if ml_stats.get('auto_retrain_enabled', False) else 'No'}")
        print(f"🎭 Ensemble Enabled: {'Yes' if ml_stats.get('ensemble_enabled', False) else 'No'}")
        
        # Cross-layer integration
        print(f"🔗 Cross-Layer Integration: {'Yes' if stats.get('cross_layer_integration_enabled', False) else 'No'}")
        print(f"📚 Real-Time Learning: {'Yes' if stats.get('real_time_learning_enabled', False) else 'No'}")
        print(f"💾 Cache Size: {stats.get('cache_size', 0)}")
        
    except Exception as e:
        print(f"❌ Error getting statistics: {e}")
        logger.error(f"Error getting statistics: {e}")
    
    # Cleanup
    print("\n🧹 Cleaning up...")
    enhanced_response.shutdown()
    
    print("\n✅ Enhanced Response System Demo Complete!")
    print("=" * 60)

def main():
    """Main demo function."""
    try:
        # Run the enhanced response demo
        asyncio.run(demonstrate_enhanced_response())
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        logger.error(f"Demo failed: {e}")

if __name__ == "__main__":
    main()
