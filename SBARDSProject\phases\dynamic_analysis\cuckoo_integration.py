"""
SBARDS Cuckoo Sandbox Integration
Advanced integration with Cuckoo Sandbox for comprehensive malware analysis

Features:
- Cuckoo API integration
- Automated sample submission
- Result retrieval and parsing
- Multi-machine analysis
- Custom analysis packages
"""

import os
import asyncio
import logging
import json
import time
import requests
import aiohttp
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional
from pathlib import Path
import tempfile
import shutil

class CuckooIntegration:
    """
    Cuckoo Sandbox Integration
    
    Provides seamless integration with Cuckoo Sandbox for advanced malware analysis
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Cuckoo integration
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.CuckooIntegration")
        
        # Cuckoo configuration
        self.cuckoo_config = config.get("dynamic_analysis", {}).get("cuckoo", {})
        self.enabled = self.cuckoo_config.get("enabled", False)
        
        if not self.enabled:
            self.logger.info("Cuckoo integration disabled")
            return
        
        # Cuckoo server settings
        self.cuckoo_host = self.cuckoo_config.get("host", "localhost")
        self.cuckoo_port = self.cuckoo_config.get("port", 8090)
        self.cuckoo_api_token = self.cuckoo_config.get("api_token", "")
        self.cuckoo_base_url = f"http://{self.cuckoo_host}:{self.cuckoo_port}"
        
        # Analysis settings
        self.default_timeout = self.cuckoo_config.get("timeout", 300)
        self.default_machine = self.cuckoo_config.get("default_machine", "")
        self.default_package = self.cuckoo_config.get("default_package", "")
        self.enable_memory_dump = self.cuckoo_config.get("enable_memory_dump", True)
        self.enable_network_capture = self.cuckoo_config.get("enable_network_capture", True)
        
        # Machine configurations
        self.machines = self.cuckoo_config.get("machines", {})
        
        # Request session
        self.session = requests.Session()
        if self.cuckoo_api_token:
            self.session.headers.update({"Authorization": f"Bearer {self.cuckoo_api_token}"})
        
        # Verify connection
        self._verify_connection()
    
    def _verify_connection(self):
        """Verify connection to Cuckoo server"""
        try:
            response = self.session.get(f"{self.cuckoo_base_url}/cuckoo/status", timeout=10)
            if response.status_code == 200:
                status = response.json()
                self.logger.info(f"Connected to Cuckoo: {status}")
            else:
                self.logger.error(f"Failed to connect to Cuckoo: {response.status_code}")
                self.enabled = False
        except Exception as e:
            self.logger.error(f"Cuckoo connection failed: {e}")
            self.enabled = False
    
    async def analyze_file_async(self, file_path: str, timeout: int = None, 
                                machine: str = None, package: str = None) -> Dict[str, Any]:
        """
        Analyze file with Cuckoo Sandbox asynchronously
        
        Args:
            file_path: Path to file to analyze
            timeout: Analysis timeout (uses default if None)
            machine: Target machine (uses default if None)
            package: Analysis package (uses default if None)
            
        Returns:
            Analysis results
        """
        if not self.enabled:
            return {
                "success": False,
                "error": "Cuckoo integration disabled",
                "timestamp": datetime.now().isoformat()
            }
        
        try:
            self.logger.info(f"Starting Cuckoo analysis for: {file_path}")
            
            # Submit file for analysis
            task_id = await self._submit_file_async(file_path, timeout, machine, package)
            if not task_id:
                return {
                    "success": False,
                    "error": "Failed to submit file to Cuckoo",
                    "timestamp": datetime.now().isoformat()
                }
            
            self.logger.info(f"File submitted to Cuckoo with task ID: {task_id}")
            
            # Wait for analysis completion
            result = await self._wait_for_completion_async(task_id, timeout or self.default_timeout)
            
            if result["success"]:
                # Retrieve and parse results
                analysis_results = await self._retrieve_results_async(task_id)
                result.update(analysis_results)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Cuckoo analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _submit_file_async(self, file_path: str, timeout: int = None, 
                                machine: str = None, package: str = None) -> Optional[int]:
        """Submit file to Cuckoo for analysis"""
        try:
            # Prepare submission data
            data = {
                "timeout": timeout or self.default_timeout,
                "priority": 1,
                "memory": self.enable_memory_dump,
                "enforce_timeout": True,
                "clock": datetime.now().strftime("%m-%d-%Y %H:%M:%S")
            }
            
            if machine or self.default_machine:
                data["machine"] = machine or self.default_machine
            
            if package or self.default_package:
                data["package"] = package or self.default_package
            
            # Add custom options
            options = []
            if self.enable_network_capture:
                options.append("network=1")
            if self.enable_memory_dump:
                options.append("memory=1")
            
            if options:
                data["options"] = ",".join(options)
            
            # Submit file
            with open(file_path, 'rb') as f:
                files = {"file": (Path(file_path).name, f, "application/octet-stream")}
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.cuckoo_base_url}/tasks/create/file",
                        data=data,
                        headers=self.session.headers
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            return result.get("task_id")
                        else:
                            self.logger.error(f"Failed to submit file: {response.status}")
                            return None
            
        except Exception as e:
            self.logger.error(f"File submission failed: {e}")
            return None
    
    async def _wait_for_completion_async(self, task_id: int, timeout: int) -> Dict[str, Any]:
        """Wait for analysis completion"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.cuckoo_base_url}/tasks/view/{task_id}",
                        headers=self.session.headers
                    ) as response:
                        if response.status == 200:
                            task_info = await response.json()
                            status = task_info.get("task", {}).get("status")
                            
                            if status == "reported":
                                return {
                                    "success": True,
                                    "task_id": task_id,
                                    "status": status,
                                    "completion_time": datetime.now().isoformat()
                                }
                            elif status in ["failed_analysis", "failed_processing"]:
                                return {
                                    "success": False,
                                    "task_id": task_id,
                                    "status": status,
                                    "error": "Analysis failed in Cuckoo"
                                }
                            
                            # Still running, wait and check again
                            await asyncio.sleep(5)
                        else:
                            self.logger.error(f"Failed to check task status: {response.status}")
                            await asyncio.sleep(10)
            
            except Exception as e:
                self.logger.error(f"Error checking task status: {e}")
                await asyncio.sleep(10)
        
        # Timeout reached
        return {
            "success": False,
            "task_id": task_id,
            "error": "Analysis timeout reached",
            "timeout": timeout
        }
    
    async def _retrieve_results_async(self, task_id: int) -> Dict[str, Any]:
        """Retrieve analysis results from Cuckoo"""
        try:
            results = {}
            
            # Get main report
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.cuckoo_base_url}/tasks/report/{task_id}",
                    headers=self.session.headers
                ) as response:
                    if response.status == 200:
                        report = await response.json()
                        results["report"] = report
                        
                        # Parse key information
                        results.update(self._parse_cuckoo_report(report))
                    else:
                        self.logger.error(f"Failed to retrieve report: {response.status}")
            
            # Get screenshots if available
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.cuckoo_base_url}/tasks/screenshots/{task_id}",
                        headers=self.session.headers
                    ) as response:
                        if response.status == 200:
                            screenshots = await response.json()
                            results["screenshots"] = screenshots
            except:
                pass  # Screenshots are optional
            
            # Get network analysis
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.cuckoo_base_url}/tasks/report/{task_id}/network",
                        headers=self.session.headers
                    ) as response:
                        if response.status == 200:
                            network = await response.json()
                            results["network_analysis"] = network
            except:
                pass  # Network analysis is optional
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve results: {e}")
            return {"error": str(e)}
    
    def _parse_cuckoo_report(self, report: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Cuckoo report and extract key information"""
        try:
            parsed = {
                "cuckoo_version": report.get("info", {}).get("version", "unknown"),
                "analysis_started": report.get("info", {}).get("started", ""),
                "analysis_ended": report.get("info", {}).get("ended", ""),
                "machine_info": report.get("info", {}).get("machine", {}),
                "target_info": report.get("target", {}),
                "signatures": [],
                "behavior_summary": {},
                "network_summary": {},
                "file_operations": [],
                "registry_operations": [],
                "process_tree": [],
                "dropped_files": [],
                "threat_score": 0.0,
                "indicators": []
            }
            
            # Parse signatures
            signatures = report.get("signatures", [])
            for sig in signatures:
                parsed["signatures"].append({
                    "name": sig.get("name", ""),
                    "description": sig.get("description", ""),
                    "severity": sig.get("severity", 0),
                    "confidence": sig.get("confidence", 0),
                    "marks": sig.get("marks", [])
                })
            
            # Calculate threat score based on signatures
            if signatures:
                total_severity = sum(sig.get("severity", 0) for sig in signatures)
                max_possible = len(signatures) * 3  # Assuming max severity is 3
                parsed["threat_score"] = min(total_severity / max_possible if max_possible > 0 else 0, 1.0)
            
            # Parse behavior summary
            behavior = report.get("behavior", {})
            if behavior:
                parsed["behavior_summary"] = {
                    "processes": len(behavior.get("processes", [])),
                    "api_calls": sum(len(proc.get("calls", [])) for proc in behavior.get("processes", [])),
                    "files_written": len([call for proc in behavior.get("processes", []) 
                                        for call in proc.get("calls", []) 
                                        if call.get("api", "").startswith("NtWriteFile")]),
                    "registry_keys": len([call for proc in behavior.get("processes", []) 
                                        for call in proc.get("calls", []) 
                                        if call.get("api", "").startswith("RegSetValue")])
                }
            
            # Parse network summary
            network = report.get("network", {})
            if network:
                parsed["network_summary"] = {
                    "tcp_connections": len(network.get("tcp", [])),
                    "udp_connections": len(network.get("udp", [])),
                    "dns_requests": len(network.get("dns", [])),
                    "http_requests": len(network.get("http", [])),
                    "domains_contacted": len(set(req.get("host", "") for req in network.get("http", []))),
                    "unique_ips": len(set(conn.get("dst", "") for conn in network.get("tcp", [])))
                }
            
            # Extract indicators
            indicators = []
            
            # Network indicators
            for tcp_conn in network.get("tcp", []):
                if tcp_conn.get("dst"):
                    indicators.append(f"TCP connection to {tcp_conn['dst']}:{tcp_conn.get('dport', '')}")
            
            for dns_req in network.get("dns", []):
                if dns_req.get("request"):
                    indicators.append(f"DNS query for {dns_req['request']}")
            
            for http_req in network.get("http", []):
                if http_req.get("host"):
                    indicators.append(f"HTTP request to {http_req['host']}")
            
            # File indicators
            for dropped_file in report.get("dropped", []):
                if dropped_file.get("name"):
                    indicators.append(f"Dropped file: {dropped_file['name']}")
            
            parsed["indicators"] = indicators[:50]  # Limit to first 50 indicators
            
            return parsed
            
        except Exception as e:
            self.logger.error(f"Failed to parse Cuckoo report: {e}")
            return {"parse_error": str(e)}
    
    def get_machine_list(self) -> List[Dict[str, Any]]:
        """Get list of available Cuckoo machines"""
        try:
            response = self.session.get(f"{self.cuckoo_base_url}/machines/list")
            if response.status_code == 200:
                return response.json().get("machines", [])
            else:
                self.logger.error(f"Failed to get machine list: {response.status_code}")
                return []
        except Exception as e:
            self.logger.error(f"Failed to get machine list: {e}")
            return []
    
    def get_task_status(self, task_id: int) -> Dict[str, Any]:
        """Get status of a specific task"""
        try:
            response = self.session.get(f"{self.cuckoo_base_url}/tasks/view/{task_id}")
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}"}
        except Exception as e:
            return {"error": str(e)}
    
    def delete_task(self, task_id: int) -> bool:
        """Delete a task and its results"""
        try:
            response = self.session.get(f"{self.cuckoo_base_url}/tasks/delete/{task_id}")
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Failed to delete task {task_id}: {e}")
            return False
    
    def get_cuckoo_status(self) -> Dict[str, Any]:
        """Get Cuckoo server status"""
        try:
            response = self.session.get(f"{self.cuckoo_base_url}/cuckoo/status")
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}"}
        except Exception as e:
            return {"error": str(e)}
    
    def is_available(self) -> bool:
        """Check if Cuckoo is available and enabled"""
        return self.enabled and self.get_cuckoo_status().get("error") is None
