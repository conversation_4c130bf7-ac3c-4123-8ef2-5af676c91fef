#pragma once

#include "response_engine.hpp"
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <atomic>
#include <mutex>
#include <chrono>

#ifdef _WIN32
    #include <windows.h>
    #include <aclapi.h>
    #include <sddl.h>
    #include <authz.h>
#else
    #include <sys/stat.h>
    #include <sys/types.h>
    #include <pwd.h>
    #include <grp.h>
    #include <sys/capability.h>
    #include <selinux/selinux.h>
#endif

/**
 * @brief Permission levels for access control
 */
enum class PermissionLevel {
    FULL_ACCESS = 0,
    READ_WRITE = 1,
    READ_ONLY = 2,
    EXECUTE_ONLY = 3,
    NO_ACCESS = 4,
    RESTRICTED = 5
};

/**
 * @brief Access control types
 */
enum class AccessControlType {
    FILE_SYSTEM = 0,
    REGISTRY = 1,
    NETWORK = 2,
    PROCESS = 3,
    SERVICE = 4,
    DEVICE = 5
};

/**
 * @brief Permission rule structure
 */
struct PermissionRule {
    std::string rule_id;
    std::string target_path;
    AccessControlType control_type;
    PermissionLevel permission_level;
    std::vector<std::string> allowed_users;
    std::vector<std::string> allowed_groups;
    std::vector<std::string> denied_users;
    std::vector<std::string> denied_groups;
    std::chrono::system_clock::time_point created_time;
    std::chrono::system_clock::time_point expiry_time;
    bool active;
    std::string reason;
    std::map<std::string, std::string> metadata;
};

/**
 * @brief Access attempt information
 */
struct AccessAttempt {
    std::string attempt_id;
    std::string user_name;
    std::string process_name;
    uint32_t process_id;
    std::string target_path;
    AccessControlType access_type;
    std::string requested_permission;
    bool allowed;
    std::string denial_reason;
    std::chrono::system_clock::time_point timestamp;
    std::string source_ip;
    std::map<std::string, std::string> context;
};

/**
 * @brief AppLocker rule configuration
 */
struct AppLockerRule {
    std::string rule_id;
    std::string rule_name;
    std::string rule_type; // Path, Hash, Publisher, etc.
    std::string action; // Allow, Deny
    std::string conditions;
    std::vector<std::string> user_groups;
    bool enabled;
    std::chrono::system_clock::time_point created_time;
};

/**
 * @brief SELinux policy configuration
 */
struct SELinuxPolicy {
    std::string policy_id;
    std::string policy_name;
    std::string domain;
    std::string type;
    std::string role;
    std::string level;
    std::vector<std::string> allowed_operations;
    std::vector<std::string> denied_operations;
    bool enforcing;
    std::chrono::system_clock::time_point created_time;
};

/**
 * @brief Advanced Permission Manager
 * 
 * This class provides comprehensive permission management capabilities including:
 * - Dynamic access control based on threat levels
 * - Integration with Windows AppLocker and Linux SELinux
 * - Real-time permission monitoring and enforcement
 * - Granular file system and registry access control
 * - Process and service permission management
 * - Network access control integration
 * - Audit logging and compliance reporting
 */
class PermissionManager {
public:
    /**
     * @brief Constructor
     * @param config Response configuration
     */
    explicit PermissionManager(const ResponseConfig& config);
    
    /**
     * @brief Destructor
     */
    ~PermissionManager();
    
    /**
     * @brief Initialize the permission manager
     * @return true if initialization successful, false otherwise
     */
    bool initialize();
    
    /**
     * @brief Shutdown the permission manager
     */
    void shutdown();
    
    /**
     * @brief Check if the manager is running
     * @return true if running, false otherwise
     */
    bool is_running() const;
    
    // Access control operations
    ResponseResult allow_access(const std::string& file_path,
                              const std::map<std::string, std::string>& metadata);
    
    ResponseResult restrict_access(const std::string& file_path,
                                 const std::map<std::string, std::string>& metadata);
    
    ResponseResult deny_access(const std::string& file_path,
                             const std::map<std::string, std::string>& metadata);
    
    ResponseResult set_permission_level(const std::string& target_path,
                                       AccessControlType control_type,
                                       PermissionLevel level,
                                       const std::map<std::string, std::string>& metadata);
    
    // Dynamic permission management
    ResponseResult apply_threat_based_permissions(const std::string& file_path,
                                                ThreatLevel threat_level,
                                                const std::map<std::string, std::string>& metadata);
    
    ResponseResult create_temporary_restriction(const std::string& target_path,
                                              AccessControlType control_type,
                                              int duration_minutes,
                                              const std::string& reason);
    
    ResponseResult remove_permission_restriction(const std::string& rule_id);
    
    // Rule management
    bool create_permission_rule(const PermissionRule& rule);
    bool update_permission_rule(const std::string& rule_id, const PermissionRule& rule);
    bool delete_permission_rule(const std::string& rule_id);
    std::vector<PermissionRule> list_permission_rules(bool active_only = true) const;
    PermissionRule get_permission_rule(const std::string& rule_id) const;
    
    // Access monitoring
    bool check_access_permission(const std::string& user_name,
                                const std::string& target_path,
                                AccessControlType access_type,
                                const std::string& requested_permission);
    
    void log_access_attempt(const AccessAttempt& attempt);
    std::vector<AccessAttempt> get_access_attempts(const std::string& target_path,
                                                  int hours_back = 24) const;
    
    // AppLocker integration (Windows)
    bool create_apploader_rule(const AppLockerRule& rule);
    bool delete_apploader_rule(const std::string& rule_id);
    std::vector<AppLockerRule> list_apploader_rules() const;
    bool enable_apploader_enforcement();
    bool disable_apploader_enforcement();
    
    // SELinux integration (Linux)
    bool create_selinux_policy(const SELinuxPolicy& policy);
    bool delete_selinux_policy(const std::string& policy_id);
    std::vector<SELinuxPolicy> list_selinux_policies() const;
    bool set_selinux_enforcing_mode(bool enforcing);
    bool check_selinux_context(const std::string& file_path, std::string& context);
    
    // Statistics and monitoring
    std::map<std::string, uint64_t> get_permission_statistics() const;
    std::vector<AccessAttempt> get_recent_access_attempts(int count = 100) const;
    std::vector<AccessAttempt> get_denied_access_attempts(int hours_back = 24) const;
    
    // Backup and restore
    bool backup_permission_configuration(const std::string& backup_path);
    bool restore_permission_configuration(const std::string& backup_path);

private:
    // Configuration and state
    ResponseConfig config_;
    std::atomic<bool> running_;
    
    // Permission rules management
    mutable std::mutex rules_mutex_;
    std::map<std::string, PermissionRule> permission_rules_;
    
    // Access attempts logging
    mutable std::mutex access_mutex_;
    std::vector<AccessAttempt> access_attempts_;
    
    // AppLocker rules (Windows)
    mutable std::mutex apploader_mutex_;
    std::map<std::string, AppLockerRule> apploader_rules_;
    
    // SELinux policies (Linux)
    mutable std::mutex selinux_mutex_;
    std::map<std::string, SELinuxPolicy> selinux_policies_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    std::map<std::string, uint64_t> permission_statistics_;
    
    // Private methods
    std::string generate_rule_id() const;
    std::string generate_attempt_id() const;
    void update_statistics(const std::string& metric, uint64_t value = 1);
    
    // Permission enforcement
    bool apply_file_system_permissions(const std::string& file_path,
                                     const PermissionRule& rule);
    
    bool apply_registry_permissions(const std::string& registry_path,
                                  const PermissionRule& rule);
    
    bool apply_process_permissions(const std::string& process_name,
                                 const PermissionRule& rule);
    
    // Rule evaluation
    bool evaluate_permission_rules(const std::string& user_name,
                                 const std::string& target_path,
                                 AccessControlType access_type,
                                 const std::string& requested_permission);
    
    PermissionLevel determine_effective_permission_level(const std::string& target_path,
                                                       const std::string& user_name);
    
    // Cleanup and maintenance
    bool cleanup_expired_rules();
    bool validate_rule_consistency();
    
    // Platform-specific implementations
#ifdef _WIN32
    bool initialize_windows_permissions();
    void cleanup_windows_permissions();
    
    bool set_windows_file_permissions(const std::string& file_path,
                                    const PermissionRule& rule);
    
    bool set_windows_registry_permissions(const std::string& registry_path,
                                        const PermissionRule& rule);
    
    bool create_windows_apploader_rule(const AppLockerRule& rule);
    bool delete_windows_apploader_rule(const std::string& rule_id);
    
    bool get_windows_user_groups(const std::string& user_name,
                               std::vector<std::string>& groups);
    
#else
    bool initialize_linux_permissions();
    void cleanup_linux_permissions();
    
    bool set_linux_file_permissions(const std::string& file_path,
                                   const PermissionRule& rule);
    
    bool create_linux_selinux_policy(const SELinuxPolicy& policy);
    bool delete_linux_selinux_policy(const std::string& policy_id);
    
    bool get_linux_user_groups(const std::string& user_name,
                              std::vector<std::string>& groups);
    
    bool set_file_capabilities(const std::string& file_path,
                             const std::vector<std::string>& capabilities);
    
#endif
    
    // Utility methods
    bool is_user_in_group(const std::string& user_name, const std::string& group_name);
    std::vector<std::string> get_user_groups(const std::string& user_name);
    bool path_matches_pattern(const std::string& path, const std::string& pattern);
    
    // Logging and auditing
    void log_permission_event(const std::string& event_type,
                             const std::string& rule_id,
                             const std::string& details);
    
    void audit_permission_change(const std::string& target_path,
                               const std::string& old_permissions,
                               const std::string& new_permissions,
                               const std::string& reason);
};
