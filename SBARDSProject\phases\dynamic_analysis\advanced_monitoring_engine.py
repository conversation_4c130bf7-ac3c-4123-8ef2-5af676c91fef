"""
SBARDS Advanced Monitoring Engine
Python orchestration layer for comprehensive API hooking and monitoring

Features:
- System call monitoring with kernel-level hooks
- File access pattern analysis and encryption detection
- Deep network protocol analysis and C2 detection
- Registry and configuration monitoring
- Real-time threat analysis and correlation
"""

import os
import asyncio
import logging
import json
import time
import threading
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional, Set, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import ctypes
from ctypes import cdll, Structure, c_char_p, c_int, c_bool, c_void_p, c_uint32, c_uint64

# Third-party imports (with fallbacks)
try:
    import psutil
except ImportError:
    psutil = None

try:
    import numpy as np
except ImportError:
    # Fallback for numpy functionality
    class NumpyFallback:
        @staticmethod
        def mean(data):
            return sum(data) / len(data) if data else 0
        @staticmethod
        def max(data):
            return max(data) if data else 0
        @staticmethod
        def std(data):
            if not data:
                return 0
            mean_val = sum(data) / len(data)
            variance = sum((x - mean_val) ** 2 for x in data) / len(data)
            return variance ** 0.5
    np = NumpyFallback()

from collections import defaultdict, deque
import re
import hashlib

@dataclass
class SystemCallEvent:
    """System call monitoring event"""
    timestamp: datetime
    process_id: int
    thread_id: int
    syscall_number: int
    syscall_name: str
    parameters: List[str]
    return_value: str
    duration_microseconds: int
    call_stack: str
    threat_level: str = "low"
    indicators: List[str] = None

@dataclass
class FileAccessEvent:
    """File access monitoring event"""
    timestamp: datetime
    operation: str  # create, open, read, write, delete, rename
    file_path: str
    process_name: str
    process_id: int
    access_mode: int
    bytes_transferred: int
    is_sensitive_file: bool
    access_pattern: str  # sequential, random, bulk
    encryption_detected: bool
    threat_level: str = "low"
    indicators: List[str] = None

@dataclass
class NetworkEvent:
    """Network monitoring event"""
    timestamp: datetime
    protocol: str  # TCP, UDP, ICMP
    local_address: str
    local_port: int
    remote_address: str
    remote_port: int
    connection_state: str
    bytes_sent: int
    bytes_received: int
    is_encrypted: bool
    is_suspicious: bool
    c2_indicators: List[str]
    dns_queries: List[str]
    http_requests: List[str]
    threat_level: str = "low"

@dataclass
class ConfigurationEvent:
    """Configuration change monitoring event"""
    timestamp: datetime
    change_type: str  # registry, config_file, service, startup
    key_path: str
    value_name: str
    old_value: str
    new_value: str
    process_name: str
    process_id: int
    is_security_related: bool
    is_persistence_mechanism: bool
    threat_level: str = "low"
    indicators: List[str] = None

class AdvancedMonitoringEngine:
    """
    Advanced Monitoring Engine

    Orchestrates comprehensive system monitoring using C++ components
    and provides intelligent analysis and correlation
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Advanced Monitoring Engine

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger("SBARDS.AdvancedMonitoringEngine")

        # Monitoring configuration
        self.monitoring_config = config.get("dynamic_analysis", {}).get("monitoring", {})

        # Component settings
        self.api_hooking_enabled = self.monitoring_config.get("api_hooking", {}).get("enabled", True)
        self.file_monitoring_enabled = self.monitoring_config.get("file_system", {}).get("enabled", True)
        self.network_monitoring_enabled = self.monitoring_config.get("network", {}).get("enabled", True)
        self.registry_monitoring_enabled = self.monitoring_config.get("registry", {}).get("enabled", True)

        # Advanced settings
        self.kernel_level_hooks = self.monitoring_config.get("api_hooking", {}).get("hook_level", "user") == "kernel"
        self.deep_analysis = self.monitoring_config.get("api_hooking", {}).get("deep_call_analysis", True)
        self.real_time_analysis = True

        # Event storage
        self.syscall_events: deque = deque(maxlen=10000)
        self.file_events: deque = deque(maxlen=10000)
        self.network_events: deque = deque(maxlen=10000)
        self.config_events: deque = deque(maxlen=10000)

        # Analysis state
        self.monitoring_active = False
        self.analysis_threads = []
        self.event_lock = threading.Lock()

        # Threat detection
        self.threat_patterns = self._load_threat_patterns()
        self.suspicious_processes = set()
        self.c2_indicators = set()
        self.persistence_mechanisms = set()

        # Performance tracking
        self.performance_stats = {
            "total_syscalls": 0,
            "total_file_operations": 0,
            "total_network_connections": 0,
            "total_config_changes": 0,
            "threats_detected": 0,
            "false_positives": 0
        }

        # Initialize C++ bridge
        self.cpp_bridge = None
        self._init_cpp_bridge()

    def _init_cpp_bridge(self):
        """Initialize C++ monitoring components"""
        try:
            # Try to load the C++ monitoring library
            lib_paths = [
                "scanner_core/cpp/build/lib/libsbards_monitoring.so",
                "scanner_core/cpp/build/lib/libsbards_monitoring.dll",
                "scanner_core/cpp/build/lib/libsbards_monitoring.dylib"
            ]

            for lib_path in lib_paths:
                if os.path.exists(lib_path):
                    try:
                        self.cpp_bridge = cdll.LoadLibrary(lib_path)
                        self._setup_cpp_functions()
                        self.logger.info(f"Loaded C++ monitoring library: {lib_path}")
                        return
                    except Exception as e:
                        self.logger.warning(f"Failed to load {lib_path}: {e}")

            self.logger.warning("C++ monitoring library not found, using Python-only monitoring")

        except Exception as e:
            self.logger.error(f"Failed to initialize C++ bridge: {e}")

    def _setup_cpp_functions(self):
        """Setup C++ function signatures"""
        if not self.cpp_bridge:
            return

        try:
            # System call monitoring functions
            self.cpp_bridge.start_syscall_monitoring.argtypes = [c_uint32]
            self.cpp_bridge.start_syscall_monitoring.restype = c_bool

            self.cpp_bridge.stop_syscall_monitoring.argtypes = []
            self.cpp_bridge.stop_syscall_monitoring.restype = c_bool

            self.cpp_bridge.get_syscall_events.argtypes = [c_char_p, c_int]
            self.cpp_bridge.get_syscall_events.restype = c_int

            # File monitoring functions
            self.cpp_bridge.start_file_monitoring.argtypes = [c_char_p]
            self.cpp_bridge.start_file_monitoring.restype = c_bool

            self.cpp_bridge.stop_file_monitoring.argtypes = []
            self.cpp_bridge.stop_file_monitoring.restype = c_bool

            self.cpp_bridge.get_file_events.argtypes = [c_char_p, c_int]
            self.cpp_bridge.get_file_events.restype = c_int

            # Network monitoring functions
            self.cpp_bridge.start_network_monitoring.argtypes = [c_char_p]
            self.cpp_bridge.start_network_monitoring.restype = c_bool

            self.cpp_bridge.stop_network_monitoring.argtypes = []
            self.cpp_bridge.stop_network_monitoring.restype = c_bool

            self.cpp_bridge.get_network_events.argtypes = [c_char_p, c_int]
            self.cpp_bridge.get_network_events.restype = c_int

            # Configuration monitoring functions
            self.cpp_bridge.start_config_monitoring.argtypes = [c_char_p]
            self.cpp_bridge.start_config_monitoring.restype = c_bool

            self.cpp_bridge.stop_config_monitoring.argtypes = []
            self.cpp_bridge.stop_config_monitoring.restype = c_bool

            self.cpp_bridge.get_config_events.argtypes = [c_char_p, c_int]
            self.cpp_bridge.get_config_events.restype = c_int

            self.logger.info("C++ function signatures configured")

        except Exception as e:
            self.logger.error(f"Failed to setup C++ functions: {e}")
            self.cpp_bridge = None

    def _load_threat_patterns(self) -> Dict[str, List[str]]:
        """Load threat detection patterns"""
        return {
            "ransomware_syscalls": [
                "NtCreateFile", "NtWriteFile", "NtSetInformationFile",
                "CryptEncrypt", "CryptGenKey", "CryptAcquireContext"
            ],
            "process_injection": [
                "NtCreateProcess", "NtWriteVirtualMemory", "NtSetContextThread",
                "NtResumeThread", "NtQueueApcThread"
            ],
            "persistence_registry": [
                "Run", "RunOnce", "Winlogon", "Services", "AppInit_DLLs",
                "Image File Execution Options", "ServiceDll"
            ],
            "network_c2": [
                "socket", "connect", "send", "recv", "WSAStartup",
                "InternetOpen", "InternetConnect", "HttpOpenRequest"
            ],
            "evasion_techniques": [
                "NtDelayExecution", "Sleep", "NtQuerySystemInformation",
                "IsDebuggerPresent", "CheckRemoteDebuggerPresent"
            ]
        }

    async def start_monitoring(self, target_process_id: Optional[int] = None) -> bool:
        """
        Start comprehensive monitoring

        Args:
            target_process_id: Specific process to monitor (None for system-wide)

        Returns:
            True if monitoring started successfully
        """
        try:
            self.logger.info("Starting advanced monitoring engine...")

            # Start C++ monitoring components
            if self.cpp_bridge:
                success = await self._start_cpp_monitoring(target_process_id)
                if not success:
                    self.logger.warning("C++ monitoring failed, falling back to Python monitoring")

            # Start Python monitoring components
            await self._start_python_monitoring(target_process_id)

            # Start analysis threads
            self._start_analysis_threads()

            self.monitoring_active = True
            self.logger.info("Advanced monitoring engine started successfully")

            return True

        except Exception as e:
            self.logger.error(f"Failed to start monitoring: {e}")
            return False

    async def stop_monitoring(self):
        """Stop all monitoring"""
        try:
            self.logger.info("Stopping advanced monitoring engine...")

            self.monitoring_active = False

            # Stop C++ monitoring
            if self.cpp_bridge:
                await self._stop_cpp_monitoring()

            # Stop Python monitoring
            await self._stop_python_monitoring()

            # Wait for analysis threads to complete
            for thread in self.analysis_threads:
                if thread.is_alive():
                    thread.join(timeout=5)

            self.logger.info("Advanced monitoring engine stopped")

        except Exception as e:
            self.logger.error(f"Failed to stop monitoring: {e}")

    async def _start_cpp_monitoring(self, target_process_id: Optional[int]) -> bool:
        """Start C++ monitoring components"""
        try:
            success = True

            # Start system call monitoring
            if self.api_hooking_enabled:
                pid = target_process_id or 0
                if not self.cpp_bridge.start_syscall_monitoring(pid):
                    self.logger.error("Failed to start syscall monitoring")
                    success = False
                else:
                    self.logger.info("System call monitoring started")

            # Start file monitoring
            if self.file_monitoring_enabled:
                config_json = json.dumps(self.monitoring_config.get("file_system", {}))
                if not self.cpp_bridge.start_file_monitoring(config_json.encode('utf-8')):
                    self.logger.error("Failed to start file monitoring")
                    success = False
                else:
                    self.logger.info("File monitoring started")

            # Start network monitoring
            if self.network_monitoring_enabled:
                config_json = json.dumps(self.monitoring_config.get("network", {}))
                if not self.cpp_bridge.start_network_monitoring(config_json.encode('utf-8')):
                    self.logger.error("Failed to start network monitoring")
                    success = False
                else:
                    self.logger.info("Network monitoring started")

            # Start configuration monitoring
            if self.registry_monitoring_enabled:
                config_json = json.dumps(self.monitoring_config.get("registry", {}))
                if not self.cpp_bridge.start_config_monitoring(config_json.encode('utf-8')):
                    self.logger.error("Failed to start configuration monitoring")
                    success = False
                else:
                    self.logger.info("Configuration monitoring started")

            return success

        except Exception as e:
            self.logger.error(f"Failed to start C++ monitoring: {e}")
            return False

    async def _stop_cpp_monitoring(self):
        """Stop C++ monitoring components"""
        try:
            if self.api_hooking_enabled:
                self.cpp_bridge.stop_syscall_monitoring()

            if self.file_monitoring_enabled:
                self.cpp_bridge.stop_file_monitoring()

            if self.network_monitoring_enabled:
                self.cpp_bridge.stop_network_monitoring()

            if self.registry_monitoring_enabled:
                self.cpp_bridge.stop_config_monitoring()

            self.logger.info("C++ monitoring components stopped")

        except Exception as e:
            self.logger.error(f"Failed to stop C++ monitoring: {e}")

    async def _start_python_monitoring(self, target_process_id: Optional[int]):
        """Start Python monitoring components"""
        try:
            # Start process monitoring
            if target_process_id:
                self._start_process_monitoring(target_process_id)
            else:
                self._start_system_monitoring()

            self.logger.info("Python monitoring components started")

        except Exception as e:
            self.logger.error(f"Failed to start Python monitoring: {e}")

    async def _stop_python_monitoring(self):
        """Stop Python monitoring components"""
        try:
            # Python monitoring cleanup
            self.logger.info("Python monitoring components stopped")

        except Exception as e:
            self.logger.error(f"Failed to stop Python monitoring: {e}")

    def _start_process_monitoring(self, process_id: int):
        """Start monitoring specific process"""
        def monitor_process():
            try:
                process = psutil.Process(process_id)

                while self.monitoring_active:
                    try:
                        # Monitor process activity
                        connections = process.connections()
                        open_files = process.open_files()

                        # Process network connections
                        for conn in connections:
                            network_event = NetworkEvent(
                                timestamp=datetime.now(),
                                protocol=conn.type.name,
                                local_address=conn.laddr.ip if conn.laddr else "",
                                local_port=conn.laddr.port if conn.laddr else 0,
                                remote_address=conn.raddr.ip if conn.raddr else "",
                                remote_port=conn.raddr.port if conn.raddr else 0,
                                connection_state=conn.status,
                                bytes_sent=0,
                                bytes_received=0,
                                is_encrypted=self._is_encrypted_port(conn.raddr.port if conn.raddr else 0),
                                is_suspicious=self._is_suspicious_connection(conn),
                                c2_indicators=[],
                                dns_queries=[],
                                http_requests=[]
                            )

                            with self.event_lock:
                                self.network_events.append(network_event)

                        # Process file operations
                        for file_info in open_files:
                            file_event = FileAccessEvent(
                                timestamp=datetime.now(),
                                operation="open",
                                file_path=file_info.path,
                                process_name=process.name(),
                                process_id=process_id,
                                access_mode=file_info.mode,
                                bytes_transferred=0,
                                is_sensitive_file=self._is_sensitive_file(file_info.path),
                                access_pattern="unknown",
                                encryption_detected=False
                            )

                            with self.event_lock:
                                self.file_events.append(file_event)

                        time.sleep(1)  # Monitor every second

                    except psutil.NoSuchProcess:
                        break
                    except Exception as e:
                        self.logger.error(f"Process monitoring error: {e}")
                        time.sleep(5)

            except Exception as e:
                self.logger.error(f"Failed to monitor process {process_id}: {e}")

        thread = threading.Thread(target=monitor_process, daemon=True)
        thread.start()
        self.analysis_threads.append(thread)

    def _start_system_monitoring(self):
        """Start system-wide monitoring"""
        def monitor_system():
            while self.monitoring_active:
                try:
                    # Monitor system-wide network connections
                    connections = psutil.net_connections()

                    for conn in connections:
                        if conn.pid:  # Only process connections with known PIDs
                            try:
                                process = psutil.Process(conn.pid)

                                network_event = NetworkEvent(
                                    timestamp=datetime.now(),
                                    protocol=conn.type.name,
                                    local_address=conn.laddr.ip if conn.laddr else "",
                                    local_port=conn.laddr.port if conn.laddr else 0,
                                    remote_address=conn.raddr.ip if conn.raddr else "",
                                    remote_port=conn.raddr.port if conn.raddr else 0,
                                    connection_state=conn.status,
                                    bytes_sent=0,
                                    bytes_received=0,
                                    is_encrypted=self._is_encrypted_port(conn.raddr.port if conn.raddr else 0),
                                    is_suspicious=self._is_suspicious_connection(conn),
                                    c2_indicators=[],
                                    dns_queries=[],
                                    http_requests=[]
                                )

                                with self.event_lock:
                                    self.network_events.append(network_event)

                            except psutil.NoSuchProcess:
                                continue

                    time.sleep(5)  # Monitor every 5 seconds for system-wide

                except Exception as e:
                    self.logger.error(f"System monitoring error: {e}")
                    time.sleep(10)

        thread = threading.Thread(target=monitor_system, daemon=True)
        thread.start()
        self.analysis_threads.append(thread)

    def _start_analysis_threads(self):
        """Start real-time analysis threads"""

        def analyze_events():
            """Real-time event analysis"""
            while self.monitoring_active:
                try:
                    # Analyze recent events
                    self._analyze_syscall_patterns()
                    self._analyze_file_patterns()
                    self._analyze_network_patterns()
                    self._analyze_config_changes()

                    # Correlate events for advanced threat detection
                    self._correlate_events()

                    time.sleep(2)  # Analyze every 2 seconds

                except Exception as e:
                    self.logger.error(f"Analysis error: {e}")
                    time.sleep(5)

        def collect_cpp_events():
            """Collect events from C++ components"""
            while self.monitoring_active and self.cpp_bridge:
                try:
                    # Collect syscall events
                    if self.api_hooking_enabled:
                        self._collect_syscall_events()

                    # Collect file events
                    if self.file_monitoring_enabled:
                        self._collect_file_events()

                    # Collect network events
                    if self.network_monitoring_enabled:
                        self._collect_network_events()

                    # Collect config events
                    if self.registry_monitoring_enabled:
                        self._collect_config_events()

                    time.sleep(0.5)  # Collect every 500ms

                except Exception as e:
                    self.logger.error(f"Event collection error: {e}")
                    time.sleep(2)

        # Start analysis threads
        analysis_thread = threading.Thread(target=analyze_events, daemon=True)
        analysis_thread.start()
        self.analysis_threads.append(analysis_thread)

        if self.cpp_bridge:
            collection_thread = threading.Thread(target=collect_cpp_events, daemon=True)
            collection_thread.start()
            self.analysis_threads.append(collection_thread)

    def _collect_syscall_events(self):
        """Collect system call events from C++ component"""
        try:
            buffer = ctypes.create_string_buffer(65536)  # 64KB buffer
            count = self.cpp_bridge.get_syscall_events(buffer, 65536)

            if count > 0:
                events_json = buffer.value.decode('utf-8')
                events_data = json.loads(events_json)

                for event_data in events_data:
                    syscall_event = SystemCallEvent(
                        timestamp=datetime.fromisoformat(event_data['timestamp']),
                        process_id=event_data['process_id'],
                        thread_id=event_data['thread_id'],
                        syscall_number=event_data['syscall_number'],
                        syscall_name=event_data['syscall_name'],
                        parameters=event_data['parameters'],
                        return_value=event_data['return_value'],
                        duration_microseconds=event_data['duration_microseconds'],
                        call_stack=event_data.get('call_stack', ''),
                        threat_level=self._assess_syscall_threat(event_data),
                        indicators=self._extract_syscall_indicators(event_data)
                    )

                    with self.event_lock:
                        self.syscall_events.append(syscall_event)
                        self.performance_stats["total_syscalls"] += 1

        except Exception as e:
            self.logger.error(f"Failed to collect syscall events: {e}")

    def _collect_file_events(self):
        """Collect file access events from C++ component"""
        try:
            buffer = ctypes.create_string_buffer(65536)
            count = self.cpp_bridge.get_file_events(buffer, 65536)

            if count > 0:
                events_json = buffer.value.decode('utf-8')
                events_data = json.loads(events_json)

                for event_data in events_data:
                    file_event = FileAccessEvent(
                        timestamp=datetime.fromisoformat(event_data['timestamp']),
                        operation=event_data['operation'],
                        file_path=event_data['file_path'],
                        process_name=event_data['process_name'],
                        process_id=event_data['process_id'],
                        access_mode=event_data['access_mode'],
                        bytes_transferred=event_data['bytes_transferred'],
                        is_sensitive_file=event_data['is_sensitive_file'],
                        access_pattern=event_data['access_pattern'],
                        encryption_detected=event_data['encryption_detected'],
                        threat_level=self._assess_file_threat(event_data),
                        indicators=self._extract_file_indicators(event_data)
                    )

                    with self.event_lock:
                        self.file_events.append(file_event)
                        self.performance_stats["total_file_operations"] += 1

        except Exception as e:
            self.logger.error(f"Failed to collect file events: {e}")

    def _collect_network_events(self):
        """Collect network events from C++ component"""
        try:
            buffer = ctypes.create_string_buffer(65536)
            count = self.cpp_bridge.get_network_events(buffer, 65536)

            if count > 0:
                events_json = buffer.value.decode('utf-8')
                events_data = json.loads(events_json)

                for event_data in events_data:
                    network_event = NetworkEvent(
                        timestamp=datetime.fromisoformat(event_data['timestamp']),
                        protocol=event_data['protocol'],
                        local_address=event_data['local_address'],
                        local_port=event_data['local_port'],
                        remote_address=event_data['remote_address'],
                        remote_port=event_data['remote_port'],
                        connection_state=event_data['connection_state'],
                        bytes_sent=event_data['bytes_sent'],
                        bytes_received=event_data['bytes_received'],
                        is_encrypted=event_data['is_encrypted'],
                        is_suspicious=event_data['is_suspicious'],
                        c2_indicators=event_data.get('c2_indicators', []),
                        dns_queries=event_data.get('dns_queries', []),
                        http_requests=event_data.get('http_requests', []),
                        threat_level=self._assess_network_threat(event_data)
                    )

                    with self.event_lock:
                        self.network_events.append(network_event)
                        self.performance_stats["total_network_connections"] += 1

        except Exception as e:
            self.logger.error(f"Failed to collect network events: {e}")

    def _collect_config_events(self):
        """Collect configuration change events from C++ component"""
        try:
            buffer = ctypes.create_string_buffer(65536)
            count = self.cpp_bridge.get_config_events(buffer, 65536)

            if count > 0:
                events_json = buffer.value.decode('utf-8')
                events_data = json.loads(events_json)

                for event_data in events_data:
                    config_event = ConfigurationEvent(
                        timestamp=datetime.fromisoformat(event_data['timestamp']),
                        change_type=event_data['change_type'],
                        key_path=event_data['key_path'],
                        value_name=event_data['value_name'],
                        old_value=event_data['old_value'],
                        new_value=event_data['new_value'],
                        process_name=event_data['process_name'],
                        process_id=event_data['process_id'],
                        is_security_related=event_data['is_security_related'],
                        is_persistence_mechanism=event_data['is_persistence_mechanism'],
                        threat_level=self._assess_config_threat(event_data),
                        indicators=self._extract_config_indicators(event_data)
                    )

                    with self.event_lock:
                        self.config_events.append(config_event)
                        self.performance_stats["total_config_changes"] += 1

        except Exception as e:
            self.logger.error(f"Failed to collect config events: {e}")

    def _analyze_syscall_patterns(self):
        """Analyze system call patterns for threats"""
        try:
            with self.event_lock:
                recent_syscalls = list(self.syscall_events)[-100:]  # Last 100 syscalls

            if not recent_syscalls:
                return

            # Analyze for ransomware patterns
            crypto_calls = [event for event in recent_syscalls
                          if any(pattern in event.syscall_name for pattern in self.threat_patterns["ransomware_syscalls"])]

            if len(crypto_calls) > 10:  # Threshold for crypto activity
                self.logger.warning(f"Potential ransomware activity detected: {len(crypto_calls)} crypto-related syscalls")
                for call in crypto_calls:
                    call.threat_level = "high"
                    if not call.indicators:
                        call.indicators = []
                    call.indicators.append("Ransomware crypto pattern")

            # Analyze for process injection
            injection_calls = [event for event in recent_syscalls
                             if any(pattern in event.syscall_name for pattern in self.threat_patterns["process_injection"])]

            if len(injection_calls) > 5:
                self.logger.warning(f"Potential process injection detected: {len(injection_calls)} injection-related syscalls")
                for call in injection_calls:
                    call.threat_level = "high"
                    if not call.indicators:
                        call.indicators = []
                    call.indicators.append("Process injection pattern")

            # Analyze call frequency and timing
            self._analyze_syscall_frequency(recent_syscalls)

        except Exception as e:
            self.logger.error(f"Syscall pattern analysis failed: {e}")

    def _analyze_file_patterns(self):
        """Analyze file access patterns for threats"""
        try:
            with self.event_lock:
                recent_files = list(self.file_events)[-100:]

            if not recent_files:
                return

            # Analyze for bulk encryption
            write_operations = [event for event in recent_files if event.operation == "write"]
            if len(write_operations) > 20:  # Bulk write threshold
                total_bytes = sum(event.bytes_transferred for event in write_operations)
                if total_bytes > 100 * 1024 * 1024:  # 100MB threshold
                    self.logger.warning(f"Potential bulk encryption detected: {len(write_operations)} writes, {total_bytes} bytes")

                    for event in write_operations:
                        event.threat_level = "high"
                        if not event.indicators:
                            event.indicators = []
                        event.indicators.append("Bulk encryption pattern")

            # Analyze sensitive file access
            sensitive_accesses = [event for event in recent_files if event.is_sensitive_file]
            if len(sensitive_accesses) > 5:
                self.logger.warning(f"Multiple sensitive file accesses: {len(sensitive_accesses)}")

                for event in sensitive_accesses:
                    event.threat_level = "medium"
                    if not event.indicators:
                        event.indicators = []
                    event.indicators.append("Sensitive file access")

            # Analyze file extension changes (potential encryption)
            self._analyze_file_extensions(recent_files)

        except Exception as e:
            self.logger.error(f"File pattern analysis failed: {e}")

    def _analyze_network_patterns(self):
        """Analyze network patterns for C2 communication"""
        try:
            with self.event_lock:
                recent_network = list(self.network_events)[-50:]

            if not recent_network:
                return

            # Analyze for C2 beaconing
            connection_counts = defaultdict(int)
            for event in recent_network:
                if event.remote_address:
                    connection_counts[event.remote_address] += 1

            # Detect high-frequency connections (potential beaconing)
            for address, count in connection_counts.items():
                if count > 10:  # Beaconing threshold
                    self.logger.warning(f"Potential C2 beaconing to {address}: {count} connections")
                    self.c2_indicators.add(address)

                    # Mark related events as suspicious
                    for event in recent_network:
                        if event.remote_address == address:
                            event.threat_level = "high"
                            event.is_suspicious = True
                            if address not in event.c2_indicators:
                                event.c2_indicators.append(f"Beaconing pattern: {count} connections")

            # Analyze for suspicious ports
            self._analyze_suspicious_ports(recent_network)

            # Analyze for encrypted connections to unusual ports
            self._analyze_encrypted_connections(recent_network)

        except Exception as e:
            self.logger.error(f"Network pattern analysis failed: {e}")

    def _analyze_config_changes(self):
        """Analyze configuration changes for persistence mechanisms"""
        try:
            with self.event_lock:
                recent_config = list(self.config_events)[-50:]

            if not recent_config:
                return

            # Analyze for persistence mechanisms
            persistence_changes = [event for event in recent_config if event.is_persistence_mechanism]

            if persistence_changes:
                self.logger.warning(f"Persistence mechanisms detected: {len(persistence_changes)} changes")

                for event in persistence_changes:
                    event.threat_level = "high"
                    if not event.indicators:
                        event.indicators = []
                    event.indicators.append("Persistence mechanism")
                    self.persistence_mechanisms.add(event.key_path)

            # Analyze for security setting modifications
            security_changes = [event for event in recent_config if event.is_security_related]

            if security_changes:
                self.logger.warning(f"Security setting modifications: {len(security_changes)} changes")

                for event in security_changes:
                    event.threat_level = "medium"
                    if not event.indicators:
                        event.indicators = []
                    event.indicators.append("Security setting modification")

        except Exception as e:
            self.logger.error(f"Config change analysis failed: {e}")

    def _correlate_events(self):
        """Correlate events across different monitoring types"""
        try:
            # Get recent events from all types
            with self.event_lock:
                recent_syscalls = list(self.syscall_events)[-50:]
                recent_files = list(self.file_events)[-50:]
                recent_network = list(self.network_events)[-20:]
                recent_config = list(self.config_events)[-20:]

            # Correlate by process ID and time window
            time_window = timedelta(seconds=30)
            current_time = datetime.now()

            # Group events by process ID
            process_events = defaultdict(lambda: {"syscalls": [], "files": [], "network": [], "config": []})

            for event in recent_syscalls:
                if current_time - event.timestamp <= time_window:
                    process_events[event.process_id]["syscalls"].append(event)

            for event in recent_files:
                if current_time - event.timestamp <= time_window:
                    process_events[event.process_id]["files"].append(event)

            for event in recent_network:
                if current_time - event.timestamp <= time_window:
                    # Network events might not have process_id, use 0 as default
                    process_events[0]["network"].append(event)

            for event in recent_config:
                if current_time - event.timestamp <= time_window:
                    process_events[event.process_id]["config"].append(event)

            # Analyze correlated events for advanced threats
            for process_id, events in process_events.items():
                self._analyze_process_behavior(process_id, events)

        except Exception as e:
            self.logger.error(f"Event correlation failed: {e}")

    def _analyze_process_behavior(self, process_id: int, events: Dict[str, List]):
        """Analyze correlated process behavior for advanced threats"""
        try:
            syscalls = events["syscalls"]
            files = events["files"]
            network = events["network"]
            config = events["config"]

            # Advanced threat detection based on correlated events
            threat_score = 0
            threat_indicators = []

            # Ransomware behavior: crypto syscalls + bulk file writes + config changes
            crypto_syscalls = [e for e in syscalls if any(p in e.syscall_name for p in self.threat_patterns["ransomware_syscalls"])]
            bulk_writes = [e for e in files if e.operation == "write" and e.bytes_transferred > 1024*1024]
            persistence_config = [e for e in config if e.is_persistence_mechanism]

            if len(crypto_syscalls) > 5 and len(bulk_writes) > 10:
                threat_score += 0.8
                threat_indicators.append("Ransomware pattern: crypto + bulk writes")
                self.logger.critical(f"RANSOMWARE DETECTED in process {process_id}")

            # APT behavior: process injection + network C2 + persistence
            injection_syscalls = [e for e in syscalls if any(p in e.syscall_name for p in self.threat_patterns["process_injection"])]
            c2_network = [e for e in network if e.is_suspicious]

            if len(injection_syscalls) > 3 and len(c2_network) > 2 and len(persistence_config) > 0:
                threat_score += 0.7
                threat_indicators.append("APT pattern: injection + C2 + persistence")
                self.logger.critical(f"APT ACTIVITY DETECTED in process {process_id}")

            # Banking trojan: network activity + sensitive file access
            sensitive_files = [e for e in files if e.is_sensitive_file]
            network_activity = len(network)

            if len(sensitive_files) > 3 and network_activity > 5:
                threat_score += 0.6
                threat_indicators.append("Banking trojan pattern: sensitive files + network")
                self.logger.warning(f"BANKING TROJAN ACTIVITY in process {process_id}")

            # Update threat statistics
            if threat_score > 0.5:
                self.performance_stats["threats_detected"] += 1
                self.suspicious_processes.add(process_id)

        except Exception as e:
            self.logger.error(f"Process behavior analysis failed: {e}")

    def _assess_syscall_threat(self, event_data: Dict[str, Any]) -> str:
        """Assess threat level of system call"""
        syscall_name = event_data.get("syscall_name", "")

        # High-risk syscalls
        high_risk = ["NtWriteVirtualMemory", "NtSetContextThread", "NtCreateProcess",
                    "CryptEncrypt", "CryptGenKey", "NtTerminateProcess"]

        # Medium-risk syscalls
        medium_risk = ["NtCreateFile", "NtWriteFile", "NtSetInformationFile",
                      "RegSetValueEx", "RegCreateKeyEx", "CreateRemoteThread"]

        if any(risk in syscall_name for risk in high_risk):
            return "high"
        elif any(risk in syscall_name for risk in medium_risk):
            return "medium"
        else:
            return "low"

    def _extract_syscall_indicators(self, event_data: Dict[str, Any]) -> List[str]:
        """Extract indicators from system call"""
        indicators = []
        syscall_name = event_data.get("syscall_name", "")
        parameters = event_data.get("parameters", [])

        # Check for suspicious parameters
        for param in parameters:
            param_str = str(param).lower()
            if any(sus in param_str for sus in ["cmd.exe", "powershell", "rundll32", "regsvr32"]):
                indicators.append(f"Suspicious process: {param}")

            if any(ext in param_str for ext in [".exe", ".dll", ".bat", ".ps1", ".vbs"]):
                indicators.append(f"Executable parameter: {param}")

        # Check for injection-related syscalls
        if "WriteVirtualMemory" in syscall_name or "SetContextThread" in syscall_name:
            indicators.append("Process injection technique")

        return indicators

    def _assess_file_threat(self, event_data: Dict[str, Any]) -> str:
        """Assess threat level of file operation"""
        file_path = event_data.get("file_path", "").lower()
        operation = event_data.get("operation", "")
        bytes_transferred = event_data.get("bytes_transferred", 0)

        # High-risk file operations
        if event_data.get("is_sensitive_file", False):
            return "high"

        if operation == "write" and bytes_transferred > 10 * 1024 * 1024:  # 10MB
            return "medium"

        if any(ext in file_path for ext in [".encrypted", ".locked", ".crypto"]):
            return "high"

        if "system32" in file_path or "windows" in file_path:
            return "medium"

        return "low"

    def _extract_file_indicators(self, event_data: Dict[str, Any]) -> List[str]:
        """Extract indicators from file operation"""
        indicators = []
        file_path = event_data.get("file_path", "")
        operation = event_data.get("operation", "")

        if event_data.get("encryption_detected", False):
            indicators.append("File encryption detected")

        if event_data.get("is_sensitive_file", False):
            indicators.append("Sensitive file access")

        if operation == "delete" and any(backup in file_path.lower() for backup in ["backup", "shadow", "restore"]):
            indicators.append("Backup file deletion")

        return indicators

    def _assess_network_threat(self, event_data: Dict[str, Any]) -> str:
        """Assess threat level of network connection"""
        remote_port = event_data.get("remote_port", 0)
        is_encrypted = event_data.get("is_encrypted", False)
        is_suspicious = event_data.get("is_suspicious", False)

        if is_suspicious:
            return "high"

        # Suspicious ports
        suspicious_ports = [4444, 5555, 6666, 7777, 8080, 9999, 1337, 31337]
        if remote_port in suspicious_ports:
            return "high"

        # Encrypted connections to unusual ports
        if is_encrypted and remote_port not in [443, 993, 995, 465, 587]:
            return "medium"

        return "low"

    def _assess_config_threat(self, event_data: Dict[str, Any]) -> str:
        """Assess threat level of configuration change"""
        if event_data.get("is_persistence_mechanism", False):
            return "high"

        if event_data.get("is_security_related", False):
            return "medium"

        return "low"

    def _extract_config_indicators(self, event_data: Dict[str, Any]) -> List[str]:
        """Extract indicators from configuration change"""
        indicators = []

        if event_data.get("is_persistence_mechanism", False):
            indicators.append("Persistence mechanism")

        if event_data.get("is_security_related", False):
            indicators.append("Security setting modification")

        key_path = event_data.get("key_path", "").lower()
        if "run" in key_path or "startup" in key_path:
            indicators.append("Startup modification")

        return indicators

    def _analyze_syscall_frequency(self, syscalls: List[SystemCallEvent]):
        """Analyze system call frequency patterns"""
        try:
            if len(syscalls) < 10:
                return

            # Count syscall frequencies
            call_counts = defaultdict(int)
            for call in syscalls:
                call_counts[call.syscall_name] += 1

            # Detect unusual frequency patterns
            total_calls = len(syscalls)
            for call_name, count in call_counts.items():
                frequency = count / total_calls

                # High frequency of specific calls might indicate automation
                if frequency > 0.3 and count > 20:
                    self.logger.warning(f"High frequency syscall detected: {call_name} ({count}/{total_calls})")

                    # Mark related events as suspicious
                    for call in syscalls:
                        if call.syscall_name == call_name:
                            call.threat_level = "medium"
                            if not call.indicators:
                                call.indicators = []
                            call.indicators.append(f"High frequency pattern: {frequency:.2%}")

        except Exception as e:
            self.logger.error(f"Syscall frequency analysis failed: {e}")

    def _analyze_file_extensions(self, file_events: List[FileAccessEvent]):
        """Analyze file extension changes for encryption detection"""
        try:
            extension_changes = defaultdict(list)

            for event in file_events:
                if event.operation in ["write", "rename"]:
                    file_path = event.file_path.lower()
                    if "." in file_path:
                        ext = file_path.split(".")[-1]
                        extension_changes[ext].append(event)

            # Detect suspicious extension patterns
            suspicious_extensions = ["encrypted", "locked", "crypto", "crypt", "enc", "aes"]

            for ext, events in extension_changes.items():
                if ext in suspicious_extensions and len(events) > 5:
                    self.logger.warning(f"Multiple files with suspicious extension: .{ext} ({len(events)} files)")

                    for event in events:
                        event.threat_level = "high"
                        event.encryption_detected = True
                        if not event.indicators:
                            event.indicators = []
                        event.indicators.append(f"Suspicious extension: .{ext}")

        except Exception as e:
            self.logger.error(f"File extension analysis failed: {e}")

    def _analyze_suspicious_ports(self, network_events: List[NetworkEvent]):
        """Analyze network connections for suspicious ports"""
        try:
            suspicious_ports = [4444, 5555, 6666, 7777, 8080, 9999, 1337, 31337, 6667, 6697]

            for event in network_events:
                if event.remote_port in suspicious_ports:
                    event.threat_level = "high"
                    event.is_suspicious = True
                    if f"Suspicious port: {event.remote_port}" not in event.c2_indicators:
                        event.c2_indicators.append(f"Suspicious port: {event.remote_port}")

                    self.logger.warning(f"Connection to suspicious port {event.remote_port}: {event.remote_address}")

        except Exception as e:
            self.logger.error(f"Suspicious port analysis failed: {e}")

    def _analyze_encrypted_connections(self, network_events: List[NetworkEvent]):
        """Analyze encrypted connections to unusual ports"""
        try:
            standard_encrypted_ports = [443, 993, 995, 465, 587, 636, 989, 990]

            for event in network_events:
                if event.is_encrypted and event.remote_port not in standard_encrypted_ports:
                    event.threat_level = "medium"
                    event.is_suspicious = True
                    if f"Encrypted connection to unusual port: {event.remote_port}" not in event.c2_indicators:
                        event.c2_indicators.append(f"Encrypted connection to unusual port: {event.remote_port}")

                    self.logger.warning(f"Encrypted connection to unusual port {event.remote_port}: {event.remote_address}")

        except Exception as e:
            self.logger.error(f"Encrypted connection analysis failed: {e}")

    def _is_encrypted_port(self, port: int) -> bool:
        """Check if port typically uses encryption"""
        encrypted_ports = [443, 993, 995, 465, 587, 636, 989, 990, 22, 21]
        return port in encrypted_ports

    def _is_suspicious_connection(self, connection) -> bool:
        """Check if network connection is suspicious"""
        if not hasattr(connection, 'raddr') or not connection.raddr:
            return False

        # Check for suspicious ports
        suspicious_ports = [4444, 5555, 6666, 7777, 8080, 9999]
        if connection.raddr.port in suspicious_ports:
            return True

        # Check for suspicious IP ranges (simplified)
        remote_ip = connection.raddr.ip
        if remote_ip.startswith("10.") or remote_ip.startswith("192.168.") or remote_ip.startswith("172."):
            return False  # Local network, less suspicious

        return False

    def _is_sensitive_file(self, file_path: str) -> bool:
        """Check if file is sensitive"""
        sensitive_patterns = [
            "/etc/passwd", "/etc/shadow", "/etc/hosts",
            "system32", "windows/system32", "config/sam",
            ".ssh/", "id_rsa", "id_dsa", "authorized_keys",
            "wallet.dat", "keystore", "private.key"
        ]

        file_path_lower = file_path.lower()
        return any(pattern in file_path_lower for pattern in sensitive_patterns)

    # Public API methods

    async def get_monitoring_results(self) -> Dict[str, Any]:
        """Get comprehensive monitoring results"""
        try:
            with self.event_lock:
                results = {
                    "monitoring_active": self.monitoring_active,
                    "performance_stats": dict(self.performance_stats),
                    "syscall_events": [asdict(event) for event in list(self.syscall_events)[-100:]],
                    "file_events": [asdict(event) for event in list(self.file_events)[-100:]],
                    "network_events": [asdict(event) for event in list(self.network_events)[-50:]],
                    "config_events": [asdict(event) for event in list(self.config_events)[-50:]],
                    "threat_summary": {
                        "suspicious_processes": list(self.suspicious_processes),
                        "c2_indicators": list(self.c2_indicators),
                        "persistence_mechanisms": list(self.persistence_mechanisms),
                        "total_threats": self.performance_stats["threats_detected"]
                    },
                    "timestamp": datetime.now().isoformat()
                }

            return results

        except Exception as e:
            self.logger.error(f"Failed to get monitoring results: {e}")
            return {"error": str(e)}

    async def get_threat_analysis(self) -> Dict[str, Any]:
        """Get detailed threat analysis"""
        try:
            with self.event_lock:
                # Analyze recent high-threat events
                high_threat_syscalls = [event for event in self.syscall_events if event.threat_level == "high"]
                high_threat_files = [event for event in self.file_events if event.threat_level == "high"]
                high_threat_network = [event for event in self.network_events if event.threat_level == "high"]
                high_threat_config = [event for event in self.config_events if event.threat_level == "high"]

                # Calculate threat scores by category
                threat_analysis = {
                    "overall_threat_level": self._calculate_overall_threat_level(),
                    "threat_categories": {
                        "ransomware_indicators": len([e for e in high_threat_files if e.encryption_detected]),
                        "process_injection": len([e for e in high_threat_syscalls if "injection" in str(e.indicators)]),
                        "c2_communication": len([e for e in high_threat_network if e.is_suspicious]),
                        "persistence_mechanisms": len([e for e in high_threat_config if e.is_persistence_mechanism]),
                        "security_modifications": len([e for e in high_threat_config if e.is_security_related])
                    },
                    "top_threats": {
                        "syscalls": [asdict(event) for event in high_threat_syscalls[-10:]],
                        "files": [asdict(event) for event in high_threat_files[-10:]],
                        "network": [asdict(event) for event in high_threat_network[-10:]],
                        "config": [asdict(event) for event in high_threat_config[-10:]]
                    },
                    "recommendations": self._generate_threat_recommendations(),
                    "timestamp": datetime.now().isoformat()
                }

            return threat_analysis

        except Exception as e:
            self.logger.error(f"Failed to get threat analysis: {e}")
            return {"error": str(e)}

    def _calculate_overall_threat_level(self) -> str:
        """Calculate overall threat level based on all events"""
        try:
            total_events = len(self.syscall_events) + len(self.file_events) + len(self.network_events) + len(self.config_events)
            if total_events == 0:
                return "none"

            high_threat_count = 0
            medium_threat_count = 0

            for event_list in [self.syscall_events, self.file_events, self.network_events, self.config_events]:
                for event in event_list:
                    if hasattr(event, 'threat_level'):
                        if event.threat_level == "high":
                            high_threat_count += 1
                        elif event.threat_level == "medium":
                            medium_threat_count += 1

            high_ratio = high_threat_count / total_events
            medium_ratio = medium_threat_count / total_events

            if high_ratio > 0.1:  # More than 10% high-threat events
                return "critical"
            elif high_ratio > 0.05 or medium_ratio > 0.2:
                return "high"
            elif high_ratio > 0.01 or medium_ratio > 0.1:
                return "medium"
            elif medium_ratio > 0.05:
                return "low"
            else:
                return "minimal"

        except Exception as e:
            self.logger.error(f"Failed to calculate threat level: {e}")
            return "unknown"

    def _generate_threat_recommendations(self) -> List[str]:
        """Generate threat-specific recommendations"""
        recommendations = []

        try:
            # Check for specific threat patterns
            if len(self.suspicious_processes) > 0:
                recommendations.append(f"Investigate {len(self.suspicious_processes)} suspicious processes")

            if len(self.c2_indicators) > 0:
                recommendations.append(f"Block {len(self.c2_indicators)} potential C2 destinations")

            if len(self.persistence_mechanisms) > 0:
                recommendations.append(f"Review {len(self.persistence_mechanisms)} persistence mechanisms")

            # Check for encryption activity
            encryption_events = [e for e in self.file_events if e.encryption_detected]
            if len(encryption_events) > 10:
                recommendations.append("Potential ransomware activity - isolate affected systems")

            # Check for injection activity
            injection_events = [e for e in self.syscall_events if "injection" in str(e.indicators)]
            if len(injection_events) > 5:
                recommendations.append("Process injection detected - scan for advanced threats")

            # General recommendations
            if self.performance_stats["threats_detected"] > 10:
                recommendations.append("High threat activity - consider incident response")

            if not recommendations:
                recommendations.append("Continue monitoring - no immediate threats detected")

        except Exception as e:
            self.logger.error(f"Failed to generate recommendations: {e}")
            recommendations.append("Error generating recommendations")

        return recommendations

    async def export_monitoring_data(self, output_file: str, format: str = "json") -> bool:
        """Export monitoring data to file"""
        try:
            results = await self.get_monitoring_results()

            if format.lower() == "json":
                with open(output_file, 'w') as f:
                    json.dump(results, f, indent=2, default=str)
            elif format.lower() == "csv":
                # Export to CSV format (simplified)
                import csv
                with open(output_file, 'w', newline='') as f:
                    writer = csv.writer(f)

                    # Write syscall events
                    writer.writerow(["Event Type", "Timestamp", "Process ID", "Details", "Threat Level"])
                    for event in results["syscall_events"]:
                        writer.writerow(["Syscall", event["timestamp"], event["process_id"],
                                       event["syscall_name"], event["threat_level"]])

                    # Write file events
                    for event in results["file_events"]:
                        writer.writerow(["File", event["timestamp"], event["process_id"],
                                       f"{event['operation']} {event['file_path']}", event["threat_level"]])
            else:
                self.logger.error(f"Unsupported export format: {format}")
                return False

            self.logger.info(f"Monitoring data exported to {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to export monitoring data: {e}")
            return False

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return {
            "monitoring_active": self.monitoring_active,
            "cpp_bridge_available": self.cpp_bridge is not None,
            "active_threads": len(self.analysis_threads),
            "event_counts": {
                "syscalls": len(self.syscall_events),
                "files": len(self.file_events),
                "network": len(self.network_events),
                "config": len(self.config_events)
            },
            "performance_stats": dict(self.performance_stats),
            "memory_usage_mb": self._get_memory_usage(),
            "cpu_usage_percent": self._get_cpu_usage()
        }

    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0.0

    def _get_cpu_usage(self) -> float:
        """Get current CPU usage percentage"""
        try:
            import psutil
            return psutil.cpu_percent(interval=0.1)
        except:
            return 0.0

    def is_monitoring_active(self) -> bool:
        """Check if monitoring is active"""
        return self.monitoring_active

    def get_monitored_processes(self) -> Set[int]:
        """Get set of monitored process IDs"""
        return self.suspicious_processes.copy()

    def get_c2_indicators(self) -> Set[str]:
        """Get C2 indicators"""
        return self.c2_indicators.copy()

    def get_persistence_mechanisms(self) -> Set[str]:
        """Get detected persistence mechanisms"""
        return self.persistence_mechanisms.copy()

    async def reset_monitoring_data(self):
        """Reset all monitoring data"""
        try:
            with self.event_lock:
                self.syscall_events.clear()
                self.file_events.clear()
                self.network_events.clear()
                self.config_events.clear()

                self.suspicious_processes.clear()
                self.c2_indicators.clear()
                self.persistence_mechanisms.clear()

                # Reset performance stats
                self.performance_stats = {
                    "total_syscalls": 0,
                    "total_file_operations": 0,
                    "total_network_connections": 0,
                    "total_config_changes": 0,
                    "threats_detected": 0,
                    "false_positives": 0
                }

            self.logger.info("Monitoring data reset")

        except Exception as e:
            self.logger.error(f"Failed to reset monitoring data: {e}")

    def __del__(self):
        """Destructor"""
        try:
            if self.monitoring_active:
                asyncio.create_task(self.stop_monitoring())
        except:
            pass
