#!/usr/bin/env python3
"""
SBARDS Response System - Working Demo
Demonstration of the response layer functionality
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Add current directory to path
sys.path.insert(0, '.')

def print_banner():
    """Print demo banner."""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🚀 SBARDS Response System - Working Demo                  ║
║                                                                              ║
║  🛡️ File Quarantine & Isolation                                             ║
║  📧 Alert Notifications                                                      ║
║  🔒 Permission Management                                                    ║
║  📊 Response Tracking                                                        ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def create_test_files():
    """Create test files for demonstration."""
    print("\n📁 Creating Test Files:")
    print("=" * 30)
    
    test_files = []
    
    # Create test directory
    test_dir = Path("demo_test_files")
    test_dir.mkdir(exist_ok=True)
    
    # Test files data
    files_data = [
        {
            "name": "safe_document.txt",
            "content": "This is a safe document with normal content.",
            "threat_level": "safe"
        },
        {
            "name": "suspicious_script.py", 
            "content": "import os\nprint('Suspicious script')\n# os.system('rm -rf /')",
            "threat_level": "suspicious"
        },
        {
            "name": "malware.exe",
            "content": "MZ\x90\x00\x03\x00\x00\x00\x04\x00\x00\x00\xff\xff\x00\x00\nFake malware executable",
            "threat_level": "malicious"
        }
    ]
    
    for file_data in files_data:
        file_path = test_dir / file_data["name"]
        
        with open(file_path, 'w', encoding='utf-8', errors='ignore') as f:
            f.write(file_data["content"])
        
        test_files.append({
            "path": str(file_path),
            "name": file_data["name"],
            "threat_level": file_data["threat_level"]
        })
        
        print(f"📄 Created: {file_data['name']} ({file_data['threat_level']})")
    
    return test_files

def test_response_system():
    """Test the response system."""
    print("\n🔧 Testing Response System:")
    print("=" * 35)
    
    try:
        # Import response module
        from response import ResponseLayer
        print("✅ Response module imported successfully")
        
        # Create configuration
        config = {
            "response": {
                "quarantine_directory": "demo_quarantine",
                "honeypot_directory": "demo_honeypot",
                "auto_quarantine": True,
                "notification_methods": ["log"],
                "email_settings": {}
            }
        }
        
        # Initialize response system
        response_system = ResponseLayer(config)
        print("✅ Response system initialized")
        
        # Create test files
        test_files = create_test_files()
        
        print("\n🛡️ Testing Response Actions:")
        print("-" * 40)
        
        for i, test_file in enumerate(test_files, 1):
            print(f"\n📋 Test {i}: {test_file['name']}")
            
            # Create mock analysis results
            analysis_results = {
                "workflow_id": f"test_workflow_{i}",
                "phases": {
                    "capture": {
                        "file_path": test_file["path"],
                        "file_info": {
                            "original_filename": test_file["name"],
                            "file_size": os.path.getsize(test_file["path"]),
                            "file_hash": f"test_hash_{i}"
                        }
                    }
                },
                "final_decision": {
                    "decision": "QUARANTINED" if test_file["threat_level"] == "malicious" 
                              else "ISOLATED" if test_file["threat_level"] == "suspicious"
                              else "ALLOWED",
                    "reason": f"File classified as {test_file['threat_level']}"
                }
            }
            
            # Test appropriate response action
            if test_file["threat_level"] == "malicious":
                print("🔒 Testing quarantine action...")
                result = response_system.quarantine_file(test_file["path"], analysis_results)
                
                if result.get("success"):
                    print(f"✅ File quarantined successfully")
                    print(f"📁 Quarantine ID: {result.get('quarantine_id')}")
                    print(f"📂 Quarantine Path: {result.get('quarantine_path')}")
                else:
                    print(f"❌ Quarantine failed: {result.get('error')}")
            
            elif test_file["threat_level"] == "suspicious":
                print("🕳️ Testing honeypot isolation...")
                result = response_system.isolate_in_honeypot(test_file["path"], analysis_results)
                
                if result.get("success"):
                    print(f"✅ File isolated in honeypot")
                    print(f"📁 Isolation ID: {result.get('isolation_id')}")
                    print(f"📂 Honeypot Path: {result.get('honeypot_path')}")
                else:
                    print(f"❌ Isolation failed: {result.get('error')}")
            
            else:
                print("📧 Testing alert notifications...")
                alert_result = response_system.send_alerts(analysis_results)
                print(f"✅ Alerts sent: {alert_result.get('alerts_sent', [])}")
                
                print("🔐 Testing permission updates...")
                perm_result = response_system.update_permissions(analysis_results)
                print(f"✅ Permissions updated: {perm_result.get('success', False)}")
        
        # Test response history
        print(f"\n📊 Response History: {len(response_system.response_history)} actions recorded")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in response system test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """Test backward compatibility function."""
    print("\n🔄 Testing Backward Compatibility:")
    print("=" * 40)
    
    try:
        from response import Response
        print("✅ Response function imported")
        
        # Test analysis results
        analysis_results = {
            "workflow_id": "compat_test",
            "phases": {
                "capture": {
                    "file_path": "demo_test_files/safe_document.txt",
                    "file_info": {
                        "original_filename": "safe_document.txt",
                        "file_size": 100,
                        "file_hash": "test_hash"
                    }
                }
            },
            "final_decision": {
                "decision": "ALLOWED",
                "reason": "File is safe"
            }
        }
        
        config = {
            "response": {
                "quarantine_directory": "demo_quarantine",
                "honeypot_directory": "demo_honeypot",
                "notification_methods": ["log"]
            }
        }
        
        result = Response(analysis_results, config)
        print(f"✅ Backward compatibility test passed")
        print(f"📊 Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Backward compatibility test failed: {e}")
        return False

def cleanup_demo_files():
    """Cleanup demo files and directories."""
    print("\n🧹 Cleaning up demo files:")
    print("=" * 30)
    
    cleanup_dirs = [
        "demo_test_files",
        "demo_quarantine", 
        "demo_honeypot"
    ]
    
    for dir_name in cleanup_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            try:
                import shutil
                shutil.rmtree(dir_path)
                print(f"✅ Cleaned up: {dir_name}")
            except Exception as e:
                print(f"⚠️  Warning cleaning {dir_name}: {e}")

def main():
    """Main demo function."""
    print_banner()
    
    success_count = 0
    total_tests = 2
    
    # Test main response system
    if test_response_system():
        success_count += 1
    
    # Test backward compatibility
    if test_backward_compatibility():
        success_count += 1
    
    # Summary
    print("\n" + "="*70)
    print("📊 Demo Summary:")
    print(f"✅ Successful Tests: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 SBARDS Response System is fully operational!")
        print("🛡️ All response actions working correctly")
        print("📧 Alert system functional")
        print("🔒 File quarantine and isolation working")
        print("📊 Response tracking active")
    elif success_count > 0:
        print("⚠️  Response system partially working")
        print("🔧 Some features may need attention")
    else:
        print("❌ Response system needs troubleshooting")
    
    print("="*70)
    
    # Cleanup
    cleanup_demo_files()

if __name__ == "__main__":
    main()
