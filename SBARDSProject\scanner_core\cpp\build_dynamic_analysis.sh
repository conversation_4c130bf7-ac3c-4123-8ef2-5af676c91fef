#!/bin/bash

# SBARDS Dynamic Analysis C++ Components Build Script
# Builds all C++ components for high-performance dynamic analysis

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="$SCRIPT_DIR/build"
INSTALL_DIR="$SCRIPT_DIR/install"
BUILD_TYPE="${BUILD_TYPE:-Release}"
PARALLEL_JOBS="${PARALLEL_JOBS:-$(nproc 2>/dev/null || echo 4)}"

log "SBARDS Dynamic Analysis C++ Build Script"
log "========================================="
log "Script directory: $SCRIPT_DIR"
log "Build directory: $BUILD_DIR"
log "Install directory: $INSTALL_DIR"
log "Build type: $BUILD_TYPE"
log "Parallel jobs: $PARALLEL_JOBS"

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check for CMake
    if ! command -v cmake &> /dev/null; then
        error "CMake is required but not installed"
        echo "Please install CMake (version 3.16 or higher)"
        exit 1
    fi
    
    CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
    log "Found CMake version: $CMAKE_VERSION"
    
    # Check for C++ compiler
    if command -v g++ &> /dev/null; then
        CXX_COMPILER="g++"
        CXX_VERSION=$(g++ --version | head -n1)
        log "Found C++ compiler: $CXX_VERSION"
    elif command -v clang++ &> /dev/null; then
        CXX_COMPILER="clang++"
        CXX_VERSION=$(clang++ --version | head -n1)
        log "Found C++ compiler: $CXX_VERSION"
    else
        error "No C++ compiler found (g++ or clang++ required)"
        exit 1
    fi
    
    # Check for Python development headers (for Python bridge)
    if command -v python3-config &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        log "Found Python: $PYTHON_VERSION"
    else
        warning "Python development headers not found, Python bridge may not build"
    fi
    
    # Platform-specific checks
    case "$(uname -s)" in
        Linux*)
            log "Platform: Linux"
            # Check for required libraries
            if ! ldconfig -p | grep -q libpthread; then
                error "pthread library not found"
                exit 1
            fi
            ;;
        Darwin*)
            log "Platform: macOS"
            ;;
        CYGWIN*|MINGW32*|MSYS*|MINGW*)
            log "Platform: Windows"
            # Check for Windows SDK
            if [ ! -d "/c/Program Files (x86)/Windows Kits" ] && [ ! -d "/c/Program Files/Windows Kits" ]; then
                warning "Windows SDK not found, some features may not build"
            fi
            ;;
        *)
            warning "Unknown platform: $(uname -s)"
            ;;
    esac
    
    success "Prerequisites check completed"
}

# Clean build directory
clean_build() {
    if [ "$1" = "--clean" ] || [ "$1" = "-c" ]; then
        log "Cleaning build directory..."
        rm -rf "$BUILD_DIR"
        rm -rf "$INSTALL_DIR"
        success "Build directory cleaned"
    fi
}

# Create build directory
create_build_dir() {
    log "Creating build directory..."
    mkdir -p "$BUILD_DIR"
    mkdir -p "$INSTALL_DIR"
    success "Build directory created"
}

# Configure CMake
configure_cmake() {
    log "Configuring CMake..."
    
    cd "$BUILD_DIR"
    
    # CMake configuration options
    CMAKE_ARGS=(
        "-DCMAKE_BUILD_TYPE=$BUILD_TYPE"
        "-DCMAKE_INSTALL_PREFIX=$INSTALL_DIR"
        "-DCMAKE_CXX_STANDARD=17"
        "-DCMAKE_CXX_STANDARD_REQUIRED=ON"
        "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
    )
    
    # Platform-specific options
    case "$(uname -s)" in
        Linux*)
            CMAKE_ARGS+=("-DCMAKE_CXX_FLAGS=-Wall -Wextra -O2")
            ;;
        Darwin*)
            CMAKE_ARGS+=("-DCMAKE_CXX_FLAGS=-Wall -Wextra -O2")
            ;;
        CYGWIN*|MINGW32*|MSYS*|MINGW*)
            CMAKE_ARGS+=("-G" "MinGW Makefiles")
            CMAKE_ARGS+=("-DCMAKE_CXX_FLAGS=/W4 /O2")
            ;;
    esac
    
    # Add source directory
    CMAKE_ARGS+=("$SCRIPT_DIR")
    
    log "Running: cmake ${CMAKE_ARGS[*]}"
    
    if cmake "${CMAKE_ARGS[@]}"; then
        success "CMake configuration completed"
    else
        error "CMake configuration failed"
        exit 1
    fi
}

# Build components
build_components() {
    log "Building C++ components..."
    
    cd "$BUILD_DIR"
    
    # Build with parallel jobs
    if cmake --build . --config "$BUILD_TYPE" --parallel "$PARALLEL_JOBS"; then
        success "Build completed successfully"
    else
        error "Build failed"
        exit 1
    fi
}

# Install components
install_components() {
    log "Installing components..."
    
    cd "$BUILD_DIR"
    
    if cmake --install . --config "$BUILD_TYPE"; then
        success "Installation completed"
    else
        error "Installation failed"
        exit 1
    fi
}

# Run tests
run_tests() {
    if [ "$1" = "--test" ] || [ "$1" = "-t" ]; then
        log "Running tests..."
        
        cd "$BUILD_DIR"
        
        if ctest --output-on-failure --parallel "$PARALLEL_JOBS"; then
            success "All tests passed"
        else
            error "Some tests failed"
            exit 1
        fi
    fi
}

# Create Python wheel (if requested)
create_python_wheel() {
    if [ "$1" = "--python" ] || [ "$1" = "-p" ]; then
        log "Creating Python wheel..."
        
        # Check if Python bridge was built
        if [ -f "$INSTALL_DIR/lib/sbards_python_bridge.so" ] || [ -f "$INSTALL_DIR/lib/sbards_python_bridge.dll" ]; then
            cd "$SCRIPT_DIR"
            
            # Create setup.py for wheel
            cat > setup.py << EOF
from setuptools import setup, Extension
import pybind11

setup(
    name="sbards-dynamic-analysis",
    version="1.0.0",
    description="SBARDS Dynamic Analysis C++ Components",
    author="SBARDS Team",
    packages=["sbards_dynamic"],
    package_data={
        "sbards_dynamic": ["*.so", "*.dll", "*.dylib"]
    },
    zip_safe=False,
)
EOF
            
            # Create package directory
            mkdir -p sbards_dynamic
            cp "$INSTALL_DIR"/lib/sbards_python_bridge.* sbards_dynamic/ 2>/dev/null || true
            touch sbards_dynamic/__init__.py
            
            # Build wheel
            if python3 setup.py bdist_wheel; then
                success "Python wheel created"
            else
                warning "Python wheel creation failed"
            fi
            
            # Cleanup
            rm -rf build dist *.egg-info setup.py sbards_dynamic
        else
            warning "Python bridge not found, skipping wheel creation"
        fi
    fi
}

# Display build summary
display_summary() {
    log "Build Summary"
    log "============="
    
    echo "Built components:"
    
    # Check for executables
    if [ -f "$INSTALL_DIR/bin/sbards_dynamic_analyzer" ] || [ -f "$INSTALL_DIR/bin/sbards_dynamic_analyzer.exe" ]; then
        echo "  ✓ Dynamic Analyzer"
    else
        echo "  ✗ Dynamic Analyzer"
    fi
    
    # Check for libraries
    if ls "$INSTALL_DIR"/lib/libsbards_*.* &>/dev/null; then
        echo "  ✓ Static Libraries"
    else
        echo "  ✗ Static Libraries"
    fi
    
    if [ -f "$INSTALL_DIR/lib/sbards_python_bridge.so" ] || [ -f "$INSTALL_DIR/lib/sbards_python_bridge.dll" ]; then
        echo "  ✓ Python Bridge"
    else
        echo "  ✗ Python Bridge"
    fi
    
    # Check for headers
    if ls "$INSTALL_DIR"/include/sbards/*.hpp &>/dev/null; then
        echo "  ✓ Header Files"
    else
        echo "  ✗ Header Files"
    fi
    
    echo ""
    echo "Installation directory: $INSTALL_DIR"
    echo "Build type: $BUILD_TYPE"
    echo ""
    
    # Usage instructions
    echo "Usage:"
    echo "  Add $INSTALL_DIR/bin to your PATH"
    echo "  Add $INSTALL_DIR/lib to your LD_LIBRARY_PATH (Linux/macOS)"
    echo "  Include headers from $INSTALL_DIR/include"
    echo ""
}

# Main build process
main() {
    # Parse command line arguments
    CLEAN=false
    TEST=false
    PYTHON=false
    
    for arg in "$@"; do
        case $arg in
            --clean|-c)
                CLEAN=true
                ;;
            --test|-t)
                TEST=true
                ;;
            --python|-p)
                PYTHON=true
                ;;
            --help|-h)
                echo "SBARDS Dynamic Analysis C++ Build Script"
                echo ""
                echo "Usage: $0 [options]"
                echo ""
                echo "Options:"
                echo "  --clean, -c     Clean build directory before building"
                echo "  --test, -t      Run tests after building"
                echo "  --python, -p    Create Python wheel"
                echo "  --help, -h      Show this help message"
                echo ""
                echo "Environment variables:"
                echo "  BUILD_TYPE      Build type (Debug, Release, RelWithDebInfo)"
                echo "  PARALLEL_JOBS   Number of parallel build jobs"
                echo ""
                exit 0
                ;;
            *)
                warning "Unknown argument: $arg"
                ;;
        esac
    done
    
    # Execute build steps
    check_prerequisites
    
    if [ "$CLEAN" = true ]; then
        clean_build --clean
    fi
    
    create_build_dir
    configure_cmake
    build_components
    install_components
    
    if [ "$TEST" = true ]; then
        run_tests --test
    fi
    
    if [ "$PYTHON" = true ]; then
        create_python_wheel --python
    fi
    
    display_summary
    
    success "Build process completed successfully!"
}

# Run main function with all arguments
main "$@"
