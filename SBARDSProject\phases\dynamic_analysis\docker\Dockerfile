# SBARDS Dynamic Analysis Docker Environment
# Secure and isolated environment for malware analysis

FROM ubuntu:20.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Set working directory
WORKDIR /analysis

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Basic utilities
    curl \
    wget \
    unzip \
    zip \
    tar \
    gzip \
    file \
    tree \
    htop \
    psmisc \
    procps \
    net-tools \
    netstat-nat \
    tcpdump \
    wireshark-common \
    tshark \
    # Development tools
    build-essential \
    gcc \
    g++ \
    make \
    cmake \
    git \
    # Python and dependencies
    python3 \
    python3-pip \
    python3-dev \
    python3-venv \
    # Security tools
    strace \
    ltrace \
    gdb \
    hexdump \
    xxd \
    strings \
    binutils \
    # Network analysis
    nmap \
    netcat \
    socat \
    # File analysis
    binwalk \
    foremost \
    # Monitoring tools
    sysstat \
    iotop \
    # Wine for Windows executables
    wine \
    wine32 \
    wine64 \
    # Additional utilities
    vim \
    nano \
    less \
    grep \
    sed \
    awk \
    jq \
    # Cleanup
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Install Python packages for analysis
RUN pip3 install --no-cache-dir \
    # Core analysis libraries
    yara-python \
    pefile \
    python-magic \
    ssdeep \
    tlsh \
    # Network analysis
    scapy \
    dpkt \
    # Cryptography
    cryptography \
    pycryptodome \
    # Data processing
    pandas \
    numpy \
    # Utilities
    requests \
    psutil \
    # Forensics
    volatility3 \
    # Malware analysis
    capstone \
    keystone-engine \
    unicorn

# Create analysis user (non-root for security)
RUN useradd -m -s /bin/bash analyst && \
    usermod -aG sudo analyst && \
    echo "analyst:analyst" | chpasswd

# Create analysis directories
RUN mkdir -p /analysis/input \
             /analysis/output \
             /analysis/logs \
             /analysis/tools \
             /analysis/temp \
             /analysis/quarantine && \
    chown -R analyst:analyst /analysis

# Install YARA (latest version)
RUN cd /tmp && \
    wget https://github.com/VirusTotal/yara/archive/v4.2.3.tar.gz && \
    tar -xzf v4.2.3.tar.gz && \
    cd yara-4.2.3 && \
    ./bootstrap.sh && \
    ./configure --enable-cuckoo --enable-magic --enable-dotnet && \
    make && \
    make install && \
    ldconfig && \
    cd / && \
    rm -rf /tmp/yara-4.2.3 /tmp/v4.2.3.tar.gz

# Install additional analysis tools
RUN cd /analysis/tools && \
    # Install radare2
    git clone https://github.com/radareorg/radare2 && \
    cd radare2 && \
    sys/install.sh && \
    cd .. && \
    rm -rf radare2 && \
    # Install ClamAV
    apt-get update && \
    apt-get install -y clamav clamav-daemon && \
    freshclam && \
    apt-get clean

# Copy analysis scripts
COPY scripts/ /analysis/scripts/
RUN chmod +x /analysis/scripts/*.sh && \
    chown -R analyst:analyst /analysis/scripts

# Copy YARA rules
COPY rules/ /analysis/rules/
RUN chown -R analyst:analyst /analysis/rules

# Set up Wine environment for Windows analysis
USER analyst
RUN winecfg && \
    # Initialize Wine
    wineboot --init && \
    # Install basic Windows components
    winetricks -q vcrun2019 dotnet48 && \
    # Clean up
    rm -rf ~/.cache/winetricks

# Switch back to root for final setup
USER root

# Configure system limits and security
RUN echo "analyst soft nproc 1024" >> /etc/security/limits.conf && \
    echo "analyst hard nproc 2048" >> /etc/security/limits.conf && \
    echo "analyst soft nofile 1024" >> /etc/security/limits.conf && \
    echo "analyst hard nofile 2048" >> /etc/security/limits.conf

# Configure network isolation (will be overridden by Docker run parameters)
RUN iptables -P INPUT DROP && \
    iptables -P FORWARD DROP && \
    iptables -P OUTPUT DROP && \
    iptables -A INPUT -i lo -j ACCEPT && \
    iptables -A OUTPUT -o lo -j ACCEPT

# Create startup script
RUN cat > /analysis/startup.sh << 'EOF'
#!/bin/bash

# Initialize analysis environment
echo "SBARDS Dynamic Analysis Environment"
echo "==================================="
echo "Starting at: $(date)"
echo "User: $(whoami)"
echo "Working directory: $(pwd)"
echo "Available tools:"
echo "  - YARA: $(yara --version)"
echo "  - Python: $(python3 --version)"
echo "  - ClamAV: $(clamscan --version)"
echo "  - Radare2: $(r2 -version)"
echo ""

# Set up environment variables
export ANALYSIS_START_TIME=$(date +%s)
export ANALYSIS_ID=${ANALYSIS_ID:-$(date +%Y%m%d_%H%M%S)}
export ANALYSIS_TIMEOUT=${ANALYSIS_TIMEOUT:-300}

# Create session log
mkdir -p /analysis/logs
echo "Analysis session started: $(date)" > /analysis/logs/session_${ANALYSIS_ID}.log

# Monitor system resources
(
    while true; do
        echo "$(date): CPU: $(cat /proc/loadavg | cut -d' ' -f1), MEM: $(free -m | grep Mem | awk '{print $3"/"$2" MB"}')" >> /analysis/logs/resources_${ANALYSIS_ID}.log
        sleep 10
    done
) &
MONITOR_PID=$!

# Set up signal handlers
cleanup() {
    echo "Cleaning up analysis environment..."
    kill $MONITOR_PID 2>/dev/null
    echo "Analysis session ended: $(date)" >> /analysis/logs/session_${ANALYSIS_ID}.log
    exit 0
}

trap cleanup SIGTERM SIGINT

# Execute the analysis command if provided
if [ "$1" ]; then
    echo "Executing: $@"
    exec "$@"
else
    echo "No command provided, starting interactive shell"
    exec /bin/bash
fi
EOF

RUN chmod +x /analysis/startup.sh

# Set up health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD ps aux | grep -v grep | grep -q analyst || exit 1

# Security hardening
RUN # Remove unnecessary packages
    apt-get autoremove -y && \
    # Clear package cache
    apt-get clean && \
    # Remove temporary files
    rm -rf /tmp/* /var/tmp/* && \
    # Set secure permissions
    chmod 755 /analysis && \
    chmod 700 /analysis/quarantine && \
    # Disable unnecessary services
    systemctl disable ssh 2>/dev/null || true && \
    systemctl disable cron 2>/dev/null || true

# Set resource limits
RUN echo "* soft core 0" >> /etc/security/limits.conf && \
    echo "* hard core 0" >> /etc/security/limits.conf && \
    echo "* soft memlock 64" >> /etc/security/limits.conf && \
    echo "* hard memlock 64" >> /etc/security/limits.conf

# Final user and permissions setup
RUN chown -R analyst:analyst /analysis && \
    chmod -R 755 /analysis/scripts && \
    chmod -R 644 /analysis/rules

# Switch to analysis user
USER analyst

# Set environment variables
ENV PATH="/analysis/scripts:$PATH"
ENV PYTHONPATH="/analysis/scripts:$PYTHONPATH"
ENV ANALYSIS_HOME="/analysis"

# Set working directory
WORKDIR /analysis

# Default command
ENTRYPOINT ["/analysis/startup.sh"]
CMD ["/bin/bash"]

# Labels for metadata
LABEL maintainer="SBARDS Team"
LABEL version="1.0"
LABEL description="SBARDS Dynamic Analysis Environment"
LABEL security.isolation="high"
LABEL analysis.type="malware"
