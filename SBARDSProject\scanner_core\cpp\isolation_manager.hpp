#pragma once

#include "response_engine.hpp"
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>

#ifdef _WIN32
    #include <windows.h>
    #include <psapi.h>
    #include <tlhelp32.h>
    #include <winternl.h>
    #include <iphlpapi.h>
#else
    #include <unistd.h>
    #include <sys/types.h>
    #include <sys/wait.h>
    #include <signal.h>
    #include <sys/ptrace.h>
    #include <sys/prctl.h>
    #include <linux/netlink.h>
    #include <linux/rtnetlink.h>
#endif

/**
 * @brief Isolation types for different levels of containment
 */
enum class IsolationType {
    LIGHT_MONITORING = 0,
    DEEP_MONITORING = 1,
    PROCESS_ISOLATION = 2,
    NETWORK_ISOLATION = 3,
    COMPLETE_ISOLATION = 4,
    SYSTEM_LOCKDOWN = 5
};

/**
 * @brief Isolation session information
 */
struct IsolationSession {
    std::string session_id;
    std::string file_path;
    IsolationType isolation_type;
    std::vector<uint32_t> isolated_processes;
    std::vector<std::string> blocked_network_connections;
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    bool active;
    std::map<std::string, std::string> metadata;
    std::string isolation_directory;
    std::vector<std::string> monitoring_logs;
};

/**
 * @brief Network isolation rule
 */
struct NetworkIsolationRule {
    std::string rule_id;
    std::string source_ip;
    std::string destination_ip;
    uint16_t port;
    std::string protocol;
    bool blocked;
    std::chrono::system_clock::time_point created_time;
};

/**
 * @brief Process monitoring information
 */
struct ProcessMonitorInfo {
    uint32_t process_id;
    std::string process_name;
    std::string executable_path;
    std::vector<uint32_t> child_processes;
    std::vector<std::string> network_connections;
    std::vector<std::string> file_accesses;
    std::chrono::system_clock::time_point start_time;
    bool is_isolated;
    std::map<std::string, std::string> behavioral_indicators;
};

/**
 * @brief Advanced Isolation Manager
 * 
 * This class provides comprehensive isolation capabilities including:
 * - Multi-level process isolation
 * - Network isolation and monitoring
 * - File system access control
 * - System-wide lockdown capabilities
 * - Real-time monitoring and behavioral analysis
 */
class IsolationManager {
public:
    /**
     * @brief Constructor
     * @param config Response configuration
     */
    explicit IsolationManager(const ResponseConfig& config);
    
    /**
     * @brief Destructor
     */
    ~IsolationManager();
    
    /**
     * @brief Initialize the isolation manager
     * @return true if initialization successful, false otherwise
     */
    bool initialize();
    
    /**
     * @brief Shutdown the isolation manager
     */
    void shutdown();
    
    /**
     * @brief Check if the manager is running
     * @return true if running, false otherwise
     */
    bool is_running() const;
    
    // Light monitoring methods
    ResponseResult setup_light_monitoring(const std::string& file_path, 
                                        const std::map<std::string, std::string>& metadata);
    
    // Deep monitoring methods
    ResponseResult setup_deep_monitoring(const std::string& file_path, 
                                       const std::map<std::string, std::string>& metadata);
    
    // Process isolation methods
    ResponseResult isolate_process(const std::string& file_path, 
                                 const std::map<std::string, std::string>& metadata);
    
    ResponseResult terminate_process(const std::string& file_path, 
                                   const std::map<std::string, std::string>& metadata);
    
    ResponseResult terminate_all_related_processes(const std::string& file_path, 
                                                  const std::map<std::string, std::string>& metadata);
    
    // Network isolation methods
    ResponseResult isolate_network(const std::string& file_path, 
                                 const std::map<std::string, std::string>& metadata);
    
    ResponseResult complete_network_isolation(const std::map<std::string, std::string>& metadata);
    
    ResponseResult complete_network_disconnection(const std::map<std::string, std::string>& metadata);
    
    // System-wide methods
    ResponseResult immediate_containment(const std::string& file_path, 
                                       const std::map<std::string, std::string>& metadata);
    
    ResponseResult system_lockdown(const std::map<std::string, std::string>& metadata);
    
    // Session management
    std::vector<IsolationSession> get_active_sessions() const;
    bool terminate_isolation_session(const std::string& session_id);
    IsolationSession get_session_info(const std::string& session_id) const;
    
    // Monitoring and statistics
    std::map<std::string, uint64_t> get_isolation_statistics() const;
    std::vector<ProcessMonitorInfo> get_monitored_processes() const;
    std::vector<NetworkIsolationRule> get_network_rules() const;

private:
    // Configuration and state
    ResponseConfig config_;
    std::atomic<bool> running_;
    
    // Session management
    mutable std::mutex sessions_mutex_;
    std::map<std::string, IsolationSession> active_sessions_;
    
    // Process monitoring
    mutable std::mutex processes_mutex_;
    std::map<uint32_t, ProcessMonitorInfo> monitored_processes_;
    std::thread process_monitor_thread_;
    
    // Network isolation
    mutable std::mutex network_mutex_;
    std::vector<NetworkIsolationRule> network_rules_;
    std::thread network_monitor_thread_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    std::map<std::string, uint64_t> isolation_statistics_;
    
    // Private methods
    std::string generate_session_id() const;
    void update_statistics(const std::string& metric, uint64_t value = 1);
    
    // Monitoring threads
    void process_monitoring_loop();
    void network_monitoring_loop();
    
    // Process management
    std::vector<uint32_t> find_processes_by_file(const std::string& file_path);
    std::vector<uint32_t> find_child_processes(uint32_t parent_pid);
    bool terminate_process_tree(uint32_t root_pid);
    bool isolate_process_tree(uint32_t root_pid, const std::string& session_id);
    
    // Network management
    bool create_network_isolation_rule(const std::string& rule_id, 
                                     const std::string& source_ip,
                                     const std::string& dest_ip,
                                     uint16_t port,
                                     const std::string& protocol);
    bool remove_network_isolation_rule(const std::string& rule_id);
    bool block_all_network_traffic();
    bool restore_network_access();
    
    // File system isolation
    bool create_isolated_directory(const std::string& session_id, std::string& isolated_path);
    bool setup_file_system_isolation(const std::string& file_path, const std::string& session_id);
    bool cleanup_isolated_directory(const std::string& session_id);
    
    // Platform-specific implementations
#ifdef _WIN32
    bool initialize_windows_isolation();
    void cleanup_windows_isolation();
    
    std::vector<uint32_t> enumerate_windows_processes();
    std::vector<uint32_t> find_windows_processes_by_file(const std::string& file_path);
    bool terminate_windows_process(uint32_t pid);
    bool isolate_windows_process(uint32_t pid, const std::string& session_id);
    
    bool create_windows_network_rule(const NetworkIsolationRule& rule);
    bool remove_windows_network_rule(const std::string& rule_id);
    bool block_windows_network_traffic();
    
    bool create_windows_job_object(const std::string& session_id, HANDLE& job_handle);
    bool assign_process_to_job(HANDLE job_handle, uint32_t pid);
    
#else
    bool initialize_linux_isolation();
    void cleanup_linux_isolation();
    
    std::vector<uint32_t> enumerate_linux_processes();
    std::vector<uint32_t> find_linux_processes_by_file(const std::string& file_path);
    bool terminate_linux_process(uint32_t pid);
    bool isolate_linux_process(uint32_t pid, const std::string& session_id);
    
    bool create_linux_network_rule(const NetworkIsolationRule& rule);
    bool remove_linux_network_rule(const std::string& rule_id);
    bool block_linux_network_traffic();
    
    bool create_linux_namespace(const std::string& session_id);
    bool setup_cgroup_isolation(uint32_t pid, const std::string& session_id);
    
#endif
    
    // Utility methods
    bool is_process_running(uint32_t pid);
    std::string get_process_executable_path(uint32_t pid);
    std::vector<std::string> get_process_network_connections(uint32_t pid);
    std::vector<std::string> get_process_file_accesses(uint32_t pid);
    
    // Logging and reporting
    void log_isolation_event(const std::string& event_type, 
                           const std::string& session_id,
                           const std::string& details);
    
    void generate_isolation_report(const IsolationSession& session);
};
