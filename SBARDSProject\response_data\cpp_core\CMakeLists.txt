# SBARDS Response Data Engine - CMake Build Configuration
# Cross-platform build system for C++ Data Core

cmake_minimum_required(VERSION 3.16)
project(SBARDSDataEngine VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific options
if(MSVC)
    # Visual Studio
    add_compile_options(/W4 /WX /permissive-)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
else()
    # GCC/Clang
    add_compile_options(-Wall -Wextra -Werror -pedantic)
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options(-O3 -DNDEBUG)
    else()
        add_compile_options(-g -O0)
    endif()
endif()

# Platform-specific settings
if(WIN32)
    add_compile_definitions(WIN32_LEAN_AND_MEAN NOMINMAX)
    set(PLATFORM_LIBS ws2_32 crypt32 advapi32)
elseif(UNIX AND NOT APPLE)
    set(PLATFORM_LIBS pthread dl z)
elseif(APPLE)
    set(PLATFORM_LIBS pthread z)
    find_library(SECURITY_FRAMEWORK Security)
    list(APPEND PLATFORM_LIBS ${SECURITY_FRAMEWORK})
endif()

# Find required packages
find_package(OpenSSL REQUIRED)
find_package(Threads REQUIRED)

# Find zlib for compression
find_package(ZLIB REQUIRED)

# Optional packages
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(JSONCPP jsoncpp)
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${OPENSSL_INCLUDE_DIR})
include_directories(${ZLIB_INCLUDE_DIRS})

# Source files
set(DATA_ENGINE_SOURCES
    data_engine.cpp
    data_engine.hpp
)

# Create shared library
add_library(sbards_data_engine SHARED ${DATA_ENGINE_SOURCES})

# Create static library
add_library(sbards_data_engine_static STATIC ${DATA_ENGINE_SOURCES})

# Set library properties
set_target_properties(sbards_data_engine PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    OUTPUT_NAME "sbards_data_engine"
    POSITION_INDEPENDENT_CODE ON
)

set_target_properties(sbards_data_engine_static PROPERTIES
    OUTPUT_NAME "sbards_data_engine_static"
    POSITION_INDEPENDENT_CODE ON
)

# Link libraries
target_link_libraries(sbards_data_engine
    ${OPENSSL_LIBRARIES}
    ${ZLIB_LIBRARIES}
    ${PLATFORM_LIBS}
    Threads::Threads
)

target_link_libraries(sbards_data_engine_static
    ${OPENSSL_LIBRARIES}
    ${ZLIB_LIBRARIES}
    ${PLATFORM_LIBS}
    Threads::Threads
)

# Compiler definitions
target_compile_definitions(sbards_data_engine PRIVATE
    SBARDS_DATA_ENGINE_EXPORTS
    OPENSSL_API_COMPAT=0x10100000L
)

target_compile_definitions(sbards_data_engine_static PRIVATE
    SBARDS_DATA_ENGINE_STATIC
    OPENSSL_API_COMPAT=0x10100000L
)

# Installation
install(TARGETS sbards_data_engine sbards_data_engine_static
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES data_engine.hpp
    DESTINATION include/sbards
)

# Create test executable (optional)
option(BUILD_DATA_TESTS "Build data test executable" ON)
if(BUILD_DATA_TESTS)
    add_executable(data_engine_test test_data_main.cpp)
    target_link_libraries(data_engine_test sbards_data_engine_static)
    
    # Add test
    enable_testing()
    add_test(NAME DataEngineTest COMMAND data_engine_test)
endif()

# Create Python extension module
option(BUILD_DATA_PYTHON_MODULE "Build Python data extension module" ON)
if(BUILD_DATA_PYTHON_MODULE)
    find_package(Python3 COMPONENTS Interpreter Development)
    if(Python3_FOUND)
        add_library(sbards_data_python MODULE ${DATA_ENGINE_SOURCES})
        target_link_libraries(sbards_data_python
            ${OPENSSL_LIBRARIES}
            ${ZLIB_LIBRARIES}
            ${PLATFORM_LIBS}
            Threads::Threads
            Python3::Python
        )
        
        set_target_properties(sbards_data_python PROPERTIES
            PREFIX ""
            OUTPUT_NAME "sbards_data_engine"
            SUFFIX "${Python3_SOABI}${CMAKE_SHARED_LIBRARY_SUFFIX}"
        )
        
        target_compile_definitions(sbards_data_python PRIVATE
            SBARDS_DATA_PYTHON_MODULE
            OPENSSL_API_COMPAT=0x10100000L
        )
    endif()
endif()

# Documentation
option(BUILD_DATA_DOCS "Build data documentation" OFF)
if(BUILD_DATA_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
        
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(data_docs ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    endif()
endif()

# Package configuration
include(CMakePackageConfigHelpers)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/SBARDSDataEngineConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/SBARDSDataEngineConfig.cmake"
    INSTALL_DESTINATION lib/cmake/SBARDSDataEngine
)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/SBARDSDataEngineConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/SBARDSDataEngineConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/SBARDSDataEngineConfigVersion.cmake"
    DESTINATION lib/cmake/SBARDSDataEngine
)

# Export targets
install(EXPORT SBARDSDataEngineTargets
    FILE SBARDSDataEngineTargets.cmake
    NAMESPACE SBARDS::
    DESTINATION lib/cmake/SBARDSDataEngine
)

export(EXPORT SBARDSDataEngineTargets
    FILE "${CMAKE_CURRENT_BINARY_DIR}/SBARDSDataEngineTargets.cmake"
    NAMESPACE SBARDS::
)

# Print configuration summary
message(STATUS "SBARDS Data Engine Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  OpenSSL version: ${OPENSSL_VERSION}")
message(STATUS "  ZLIB version: ${ZLIB_VERSION_STRING}")
message(STATUS "  Build tests: ${BUILD_DATA_TESTS}")
message(STATUS "  Build Python module: ${BUILD_DATA_PYTHON_MODULE}")
message(STATUS "  Build documentation: ${BUILD_DATA_DOCS}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
