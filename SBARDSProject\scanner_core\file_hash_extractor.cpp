#include "cpp_integration.hpp"
#include <iostream>
#include <fstream>
#include <chrono>
#include <stdexcept>

namespace SBARDS {

FileHashExtractor::FileHashExtractor() {
    // Initialize OpenSSL
    OpenSSL_add_all_algorithms();
    ERR_load_crypto_strings();
}

FileHashExtractor::~FileHashExtractor() {
    EVP_cleanup();
    ERR_free_strings();
}

FileHashExtractor::HashResult FileHashExtractor::extractHashes(const std::string& file_path) {
    HashResult result;
    auto start_time = std::chrono::high_resolution_clock::now();

    try {
        // Check if file exists and is readable
        std::ifstream file(file_path, std::ios::binary);
        if (!file.is_open()) {
            result.verified = false;
            result.error_message = "Cannot open file: " + file_path;
            return result;
        }
        file.close();

        // First extraction round
        std::string sha256_1 = calculateSHA256(file_path);
        std::string sha1_1 = calculateSHA1(file_path);
        std::string md5_1 = calculateMD5(file_path);

        // Second extraction round for verification (التحقق المزدوج)
        std::string sha256_2 = calculateSHA256(file_path);
        std::string sha1_2 = calculateSHA1(file_path);
        std::string md5_2 = calculateMD5(file_path);

        // Verify consistency between both rounds
        if (sha256_1 == sha256_2 && sha1_1 == sha1_2 && md5_1 == md5_2) {
            result.sha256 = sha256_1;
            result.sha1 = sha1_1;
            result.md5 = md5_1;
            result.verified = true;
            result.error_message = "";
        } else {
            result.verified = false;
            result.error_message = "Hash verification failed - inconsistent results between extraction rounds";
        }

        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        result.extraction_time_ms = duration.count() / 1000.0;

    } catch (const std::exception& e) {
        result.verified = false;
        result.error_message = "Exception during hash extraction: " + std::string(e.what());
    }

    return result;
}

bool FileHashExtractor::verifyHashIntegrity(const std::string& file_path, const HashResult& original_hash) {
    try {
        HashResult new_hash = extractHashes(file_path);

        return new_hash.verified &&
               new_hash.sha256 == original_hash.sha256 &&
               new_hash.sha1 == original_hash.sha1 &&
               new_hash.md5 == original_hash.md5;
    } catch (...) {
        return false;
    }
}

std::string FileHashExtractor::calculateSHA256(const std::string& file_path) {
    std::ifstream file(file_path, std::ios::binary);
    if (!file.is_open()) {
        throw std::runtime_error("Cannot open file for SHA256 calculation");
    }

    EVP_MD_CTX* ctx = EVP_MD_CTX_new();
    if (!ctx) {
        throw std::runtime_error("Failed to create EVP context");
    }

    if (EVP_DigestInit_ex(ctx, EVP_sha256(), nullptr) != 1) {
        EVP_MD_CTX_free(ctx);
        throw std::runtime_error("Failed to initialize SHA256 digest");
    }

    char buffer[8192];
    while (file.read(buffer, sizeof(buffer)) || file.gcount() > 0) {
        if (EVP_DigestUpdate(ctx, buffer, file.gcount()) != 1) {
            EVP_MD_CTX_free(ctx);
            throw std::runtime_error("Failed to update SHA256 digest");
        }
    }

    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int hash_len;
    if (EVP_DigestFinal_ex(ctx, hash, &hash_len) != 1) {
        EVP_MD_CTX_free(ctx);
        throw std::runtime_error("Failed to finalize SHA256 digest");
    }

    EVP_MD_CTX_free(ctx);
    return bytesToHex(hash, hash_len);
}

std::string FileHashExtractor::calculateSHA1(const std::string& file_path) {
    std::ifstream file(file_path, std::ios::binary);
    if (!file.is_open()) {
        throw std::runtime_error("Cannot open file for SHA1 calculation");
    }

    EVP_MD_CTX* ctx = EVP_MD_CTX_new();
    if (!ctx) {
        throw std::runtime_error("Failed to create EVP context");
    }

    if (EVP_DigestInit_ex(ctx, EVP_sha1(), nullptr) != 1) {
        EVP_MD_CTX_free(ctx);
        throw std::runtime_error("Failed to initialize SHA1 digest");
    }

    char buffer[8192];
    while (file.read(buffer, sizeof(buffer)) || file.gcount() > 0) {
        if (EVP_DigestUpdate(ctx, buffer, file.gcount()) != 1) {
            EVP_MD_CTX_free(ctx);
            throw std::runtime_error("Failed to update SHA1 digest");
        }
    }

    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int hash_len;
    if (EVP_DigestFinal_ex(ctx, hash, &hash_len) != 1) {
        EVP_MD_CTX_free(ctx);
        throw std::runtime_error("Failed to finalize SHA1 digest");
    }

    EVP_MD_CTX_free(ctx);
    return bytesToHex(hash, hash_len);
}

std::string FileHashExtractor::calculateMD5(const std::string& file_path) {
    std::ifstream file(file_path, std::ios::binary);
    if (!file.is_open()) {
        throw std::runtime_error("Cannot open file for MD5 calculation");
    }

    EVP_MD_CTX* ctx = EVP_MD_CTX_new();
    if (!ctx) {
        throw std::runtime_error("Failed to create EVP context");
    }

    if (EVP_DigestInit_ex(ctx, EVP_md5(), nullptr) != 1) {
        EVP_MD_CTX_free(ctx);
        throw std::runtime_error("Failed to initialize MD5 digest");
    }

    char buffer[8192];
    while (file.read(buffer, sizeof(buffer)) || file.gcount() > 0) {
        if (EVP_DigestUpdate(ctx, buffer, file.gcount()) != 1) {
            EVP_MD_CTX_free(ctx);
            throw std::runtime_error("Failed to update MD5 digest");
        }
    }

    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int hash_len;
    if (EVP_DigestFinal_ex(ctx, hash, &hash_len) != 1) {
        EVP_MD_CTX_free(ctx);
        throw std::runtime_error("Failed to finalize MD5 digest");
    }

    EVP_MD_CTX_free(ctx);
    return bytesToHex(hash, hash_len);
}

std::string FileHashExtractor::bytesToHex(const unsigned char* bytes, size_t length) {
    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (size_t i = 0; i < length; ++i) {
        ss << std::setw(2) << static_cast<unsigned int>(bytes[i]);
    }
    return ss.str();
}

} // namespace SBARDS


