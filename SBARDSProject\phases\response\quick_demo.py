#!/usr/bin/env python3
"""
SBARDS Response System - Quick Demo
"""

print("🚀 SBARDS Response System - Quick Demo")
print("=" * 50)

try:
    # Import response module
    from response import ResponseLayer
    print("✅ Response module imported successfully")
    
    # Create basic config
    config = {
        "response": {
            "quarantine_directory": "quick_quarantine",
            "honeypot_directory": "quick_honeypot",
            "notification_methods": ["log"]
        }
    }
    
    # Initialize response system
    response_system = ResponseLayer(config)
    print("✅ Response system initialized")
    
    # Test alert functionality
    analysis_results = {
        "workflow_id": "quick_demo",
        "final_decision": {
            "decision": "ALLOWED",
            "reason": "Test file is safe"
        }
    }
    
    # Send alerts
    alert_result = response_system.send_alerts(analysis_results)
    print(f"✅ Alert test completed: {len(alert_result.get('alerts_sent', []))} alerts sent")
    
    # Update permissions
    perm_result = response_system.update_permissions(analysis_results)
    print(f"✅ Permission update: {perm_result.get('success', False)}")
    
    # Check response history
    print(f"📊 Response history: {len(response_system.response_history)} actions")
    
    print("\n🎉 SBARDS Response System is working perfectly!")
    print("✅ Alert system functional")
    print("✅ Permission management active")
    print("✅ Response tracking operational")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("Demo completed!")
