/**
 * SBARDS Response Engine - Test Suite
 * Comprehensive testing for C++ Response Core
 */

#include "response_engine.hpp"
#include <iostream>
#include <cassert>
#include <chrono>
#include <thread>
#include <fstream>
#include <filesystem>

using namespace SBARDS::Response;

class ResponseEngineTest {
private:
    std::unique_ptr<ResponseEngine> engine_;
    ResponseConfig config_;
    
public:
    ResponseEngineTest() {
        SetupTestConfiguration();
        engine_ = std::make_unique<ResponseEngine>(config_);
    }
    
    ~ResponseEngineTest() {
        if (engine_) {
            engine_->Shutdown();
        }
        CleanupTestFiles();
    }
    
    void SetupTestConfiguration() {
        config_.security_level = SecurityLevel::ENHANCED;
        config_.base_directory = "test_response_data";
        config_.quarantine_directory = "test_response_data/quarantine";
        config_.backup_directory = "test_response_data/backup";
        config_.log_directory = "test_response_data/logs";
        config_.encryption_enabled = true;
        config_.blockchain_logging = false;
        config_.real_time_monitoring = true;
        config_.max_concurrent_responses = 4;
        config_.response_timeout_seconds = 60;
    }
    
    void CleanupTestFiles() {
        try {
            if (std::filesystem::exists(config_.base_directory)) {
                std::filesystem::remove_all(config_.base_directory);
            }
        } catch (const std::exception& e) {
            std::cerr << "Cleanup failed: " << e.what() << std::endl;
        }
    }
    
    bool TestEngineInitialization() {
        std::cout << "Testing engine initialization..." << std::endl;
        
        bool result = engine_->Initialize();
        if (!result) {
            std::cerr << "Engine initialization failed" << std::endl;
            return false;
        }
        
        // Verify directories were created
        if (!std::filesystem::exists(config_.quarantine_directory)) {
            std::cerr << "Quarantine directory not created" << std::endl;
            return false;
        }
        
        if (!std::filesystem::exists(config_.backup_directory)) {
            std::cerr << "Backup directory not created" << std::endl;
            return false;
        }
        
        std::cout << "✓ Engine initialization test passed" << std::endl;
        return true;
    }
    
    bool TestFileCreation() {
        std::cout << "Testing test file creation..." << std::endl;
        
        // Create test file
        std::string test_file = config_.base_directory + "/test_file.txt";
        std::ofstream file(test_file);
        if (!file.is_open()) {
            std::cerr << "Failed to create test file" << std::endl;
            return false;
        }
        
        file << "This is a test file for SBARDS Response Engine testing.\n";
        file << "It contains some sample content for analysis.\n";
        file << "Suspicious behavior: CreateProcess, WriteFile, RegSetValue\n";
        file.close();
        
        if (!std::filesystem::exists(test_file)) {
            std::cerr << "Test file was not created successfully" << std::endl;
            return false;
        }
        
        std::cout << "✓ Test file creation passed" << std::endl;
        return true;
    }
    
    bool TestMonitorStrategy() {
        std::cout << "Testing monitor strategy..." << std::endl;
        
        FileAnalysisResult analysis;
        analysis.file_path = config_.base_directory + "/test_file.txt";
        analysis.file_hash_sha256 = "test_hash_monitor";
        analysis.file_size = 1024;
        analysis.threat_level = ThreatLevel::SAFE;
        analysis.threat_score = 0.1;
        analysis.confidence = 0.9;
        analysis.detected_threats.push_back("low_risk_behavior");
        analysis.analysis_timestamp = std::chrono::system_clock::now();
        
        auto result = engine_->ProcessFile(analysis);
        
        if (!result.success) {
            std::cerr << "Monitor strategy failed: " << result.error_message << std::endl;
            return false;
        }
        
        if (result.strategy_used != ResponseStrategy::MONITOR) {
            std::cerr << "Wrong strategy used: expected MONITOR" << std::endl;
            return false;
        }
        
        std::cout << "✓ Monitor strategy test passed" << std::endl;
        return true;
    }
    
    bool TestQuarantineStrategy() {
        std::cout << "Testing quarantine strategy..." << std::endl;
        
        // Create another test file for quarantine
        std::string test_file = config_.base_directory + "/suspicious_file.exe";
        std::ofstream file(test_file);
        file << "Suspicious executable content\n";
        file << "malicious_api: CreateRemoteThread\n";
        file.close();
        
        FileAnalysisResult analysis;
        analysis.file_path = test_file;
        analysis.file_hash_sha256 = "test_hash_quarantine";
        analysis.file_size = 2048;
        analysis.threat_level = ThreatLevel::SUSPICIOUS;
        analysis.threat_score = 0.6;
        analysis.confidence = 0.8;
        analysis.detected_threats.push_back("suspicious_behavior");
        analysis.detected_threats.push_back("malicious_api");
        analysis.analysis_timestamp = std::chrono::system_clock::now();
        
        auto result = engine_->ProcessFile(analysis);
        
        if (!result.success) {
            std::cerr << "Quarantine strategy failed: " << result.error_message << std::endl;
            return false;
        }
        
        if (result.strategy_used != ResponseStrategy::QUARANTINE) {
            std::cerr << "Wrong strategy used: expected QUARANTINE" << std::endl;
            return false;
        }
        
        // Verify file was quarantined
        if (std::filesystem::exists(test_file)) {
            std::cerr << "Original file still exists after quarantine" << std::endl;
            return false;
        }
        
        if (result.quarantine_path.empty()) {
            std::cerr << "Quarantine path not set" << std::endl;
            return false;
        }
        
        std::cout << "✓ Quarantine strategy test passed" << std::endl;
        return true;
    }
    
    bool TestBlockStrategy() {
        std::cout << "Testing block strategy..." << std::endl;
        
        // Create malicious test file
        std::string test_file = config_.base_directory + "/malicious_file.exe";
        std::ofstream file(test_file);
        file << "Malicious executable\n";
        file << "packer: UPX\n";
        file << "obfuscation: heavy\n";
        file.close();
        
        FileAnalysisResult analysis;
        analysis.file_path = test_file;
        analysis.file_hash_sha256 = "test_hash_block";
        analysis.file_size = 4096;
        analysis.threat_level = ThreatLevel::MALICIOUS;
        analysis.threat_score = 0.85;
        analysis.confidence = 0.95;
        analysis.detected_threats.push_back("packer");
        analysis.detected_threats.push_back("obfuscation");
        analysis.detected_threats.push_back("malicious_api");
        analysis.analysis_timestamp = std::chrono::system_clock::now();
        
        auto result = engine_->ProcessFile(analysis);
        
        if (!result.success) {
            std::cerr << "Block strategy failed: " << result.error_message << std::endl;
            return false;
        }
        
        if (result.strategy_used != ResponseStrategy::BLOCK) {
            std::cerr << "Wrong strategy used: expected BLOCK" << std::endl;
            return false;
        }
        
        if (result.backup_path.empty()) {
            std::cerr << "Backup path not set" << std::endl;
            return false;
        }
        
        std::cout << "✓ Block strategy test passed" << std::endl;
        return true;
    }
    
    bool TestPerformanceMetrics() {
        std::cout << "Testing performance metrics..." << std::endl;
        
        auto metrics = engine_->GetPerformanceMetrics();
        
        if (metrics.total_files_processed.load() == 0) {
            std::cerr << "No files processed recorded" << std::endl;
            return false;
        }
        
        if (metrics.successful_responses.load() == 0) {
            std::cerr << "No successful responses recorded" << std::endl;
            return false;
        }
        
        std::cout << "Performance Metrics:" << std::endl;
        std::cout << "  Total files processed: " << metrics.total_files_processed.load() << std::endl;
        std::cout << "  Successful responses: " << metrics.successful_responses.load() << std::endl;
        std::cout << "  Failed responses: " << metrics.failed_responses.load() << std::endl;
        std::cout << "  Average response time: " << metrics.average_response_time_ms.load() << " ms" << std::endl;
        
        std::cout << "✓ Performance metrics test passed" << std::endl;
        return true;
    }
    
    bool TestConcurrentProcessing() {
        std::cout << "Testing concurrent processing..." << std::endl;
        
        const int num_files = 5;
        std::vector<std::future<ResponseActionResult>> futures;
        
        // Create multiple test files and process them concurrently
        for (int i = 0; i < num_files; ++i) {
            std::string test_file = config_.base_directory + "/concurrent_test_" + std::to_string(i) + ".txt";
            std::ofstream file(test_file);
            file << "Concurrent test file " << i << std::endl;
            file.close();
            
            FileAnalysisResult analysis;
            analysis.file_path = test_file;
            analysis.file_hash_sha256 = "concurrent_hash_" + std::to_string(i);
            analysis.file_size = 512;
            analysis.threat_level = ThreatLevel::SAFE;
            analysis.threat_score = 0.1;
            analysis.confidence = 0.9;
            analysis.analysis_timestamp = std::chrono::system_clock::now();
            
            futures.push_back(engine_->ProcessFileAsync(analysis));
        }
        
        // Wait for all results
        int successful = 0;
        for (auto& future : futures) {
            try {
                auto result = future.get();
                if (result.success) {
                    successful++;
                }
            } catch (const std::exception& e) {
                std::cerr << "Concurrent processing exception: " << e.what() << std::endl;
            }
        }
        
        if (successful != num_files) {
            std::cerr << "Concurrent processing failed: " << successful << "/" << num_files << " successful" << std::endl;
            return false;
        }
        
        std::cout << "✓ Concurrent processing test passed (" << successful << "/" << num_files << ")" << std::endl;
        return true;
    }
    
    bool RunAllTests() {
        std::cout << "=== SBARDS Response Engine Test Suite ===" << std::endl;
        
        bool all_passed = true;
        
        all_passed &= TestEngineInitialization();
        all_passed &= TestFileCreation();
        all_passed &= TestMonitorStrategy();
        all_passed &= TestQuarantineStrategy();
        all_passed &= TestBlockStrategy();
        all_passed &= TestPerformanceMetrics();
        all_passed &= TestConcurrentProcessing();
        
        std::cout << std::endl;
        if (all_passed) {
            std::cout << "🎉 All tests PASSED! Response Engine is working correctly." << std::endl;
        } else {
            std::cout << "❌ Some tests FAILED! Please check the implementation." << std::endl;
        }
        
        return all_passed;
    }
};

int main() {
    try {
        ResponseEngineTest test;
        bool success = test.RunAllTests();
        return success ? 0 : 1;
    } catch (const std::exception& e) {
        std::cerr << "Test suite failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
