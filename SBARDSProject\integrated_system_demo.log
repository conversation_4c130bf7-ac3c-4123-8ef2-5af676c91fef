2025-05-30 21:02:39,397 - SBARDS.ComprehensiveResponse - INFO - Initializing C++ Response Integration...
2025-05-30 21:02:39,397 - SBARDS.CPPResponseIntegration - INFO - Initializing C++ Response Integration...
2025-05-30 21:02:39,399 - SBARDS.CPPResponseIntegration - ERROR - C++ response library not found
2025-05-30 21:02:39,399 - SBARDS.CPPResponseIntegration - ERROR - Failed to load C++ response library
2025-05-30 21:02:39,400 - SBARDS.CPPResponseIntegrationFactory - WARNING - Failed to create CPP Response Integration: Failed to initialize CPP Response Integration
2025-05-30 21:02:39,400 - SBARDS.ComprehensiveResponse - WARNING - C++ Response Integration failed to initialize, using Python fallback
2025-05-30 21:02:39,400 - SBARDS.ComprehensiveResponse - INFO - Initializing Blockchain Integration...
2025-05-30 21:02:39,413 - SBARDS.ComprehensiveResponse - WARNING - Blockchain integration initialization failed: ('Could not deserialize key data. The data may be in an incorrect format, the provided password may be incorrect, it may be encrypted with an unsupported algorithm, or it may be an unsupported key type (e.g. EC curves with explicit parameters).', [<OpenSSLError(code=478150756, lib=57, reason=100, reason_text=bad decrypt)>, <OpenSSLError(code=293601396, lib=35, reason=116, reason_text=pkcs12 cipherfinal error)>, <OpenSSLError(code=478150756, lib=57, reason=100, reason_text=bad decrypt)>, <OpenSSLError(code=293601396, lib=35, reason=116, reason_text=pkcs12 cipherfinal error)>])
2025-05-30 21:02:39,413 - SBARDS.ComprehensiveResponse - INFO - Continuing without blockchain integration
2025-05-30 21:02:39,415 - SBARDS.ComprehensiveResponse - INFO - Response directories initialized
2025-05-30 21:02:39,421 - SBARDS.ComprehensiveResponse - INFO - Response databases initialized
2025-05-30 21:02:39,422 - SBARDS.ComprehensiveResponse - INFO - Notification systems initialized
2025-05-30 21:02:39,422 - SBARDS.ComprehensiveResponse - INFO - Security systems initialized
2025-05-30 21:02:39,422 - SBARDS.ComprehensiveResponse - INFO - Initializing Blockchain Integration...
2025-05-30 21:02:39,427 - SBARDS.ComprehensiveResponse - WARNING - Blockchain integration initialization failed: ('Could not deserialize key data. The data may be in an incorrect format, the provided password may be incorrect, it may be encrypted with an unsupported algorithm, or it may be an unsupported key type (e.g. EC curves with explicit parameters).', [<OpenSSLError(code=478150756, lib=57, reason=100, reason_text=bad decrypt)>, <OpenSSLError(code=293601396, lib=35, reason=116, reason_text=pkcs12 cipherfinal error)>, <OpenSSLError(code=478150756, lib=57, reason=100, reason_text=bad decrypt)>, <OpenSSLError(code=293601396, lib=35, reason=116, reason_text=pkcs12 cipherfinal error)>])
2025-05-30 21:02:39,427 - SBARDS.ComprehensiveResponse - INFO - Continuing without blockchain integration
2025-05-30 21:02:39,427 - SBARDS.ComprehensiveResponse - INFO - ML model management initialized
2025-05-30 21:02:39,428 - SBARDS.ComprehensiveResponse - INFO - Comprehensive Response System initialized
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Initializing model: threat_classifier
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Created new model: threat_classifier
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Initializing model: malware_family_detector
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Created new model: malware_family_detector
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Initializing model: anomaly_detector
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Created new model: anomaly_detector
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Initializing model: behavioral_analyzer
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Created new model: behavioral_analyzer
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Initializing model: evasion_detector
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Created new model: evasion_detector
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Initializing model: response_optimizer
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - ERROR - Error creating model response_optimizer: Unknown algorithm: ensemble
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Created new model: response_optimizer
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - INFO - Advanced ML Models Manager initialized successfully
2025-05-30 21:02:39,475 - SBARDS.EnhancedResponse - INFO - Enhanced Response System initialized successfully
2025-05-30 21:02:39,475 - SBARDS.SystemIntegration - INFO - SBARDS System Integration initialized successfully
2025-05-30 21:02:39,475 - SBARDS.IntegratedDemo - INFO - Created 4 test files in C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds
2025-05-30 21:02:39,475 - SBARDS.SystemIntegration - INFO - Starting comprehensive analysis for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\safe_document.txt
2025-05-30 21:02:39,475 - SBARDS.EnhancedResponse - INFO - Processing enhanced analysis results with ML integration
2025-05-30 21:02:39,475 - SBARDS.MLModelsManager - ERROR - Error in threat level prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,488 - SBARDS.MLModelsManager - ERROR - Error in malware family detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,488 - SBARDS.MLModelsManager - ERROR - Error in evasion detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,488 - SBARDS.MLModelsManager - ERROR - Error in behavioral analysis: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,488 - SBARDS.MLModelsManager - ERROR - Error in anomaly detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,488 - SBARDS.EnhancedResponse - ERROR - Error in enhanced threat assessment: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,489 - SBARDS.ComprehensiveResponse - INFO - Executing safe file strategy for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\safe_document.txt
2025-05-30 21:02:39,501 - SBARDS.ComprehensiveResponse - INFO - Safe file database inserted: 
2025-05-30 21:02:39,502 - SBARDS.ComprehensiveResponse - INFO - ML models updated with safe file data
2025-05-30 21:02:39,503 - SBARDS.ComprehensiveResponse - INFO - Normal access policies applied: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\safe_document.txt
2025-05-30 21:02:39,511 - SBARDS.ComprehensiveResponse - INFO - Light monitoring setup for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\safe_document.txt
2025-05-30 21:02:39,511 - SBARDS.ComprehensiveResponse - INFO - Safe file notification: safe_document.txt
2025-05-30 21:02:39,511 - SBARDS.ComprehensiveResponse - INFO - Safe file strategy completed successfully for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\safe_document.txt
2025-05-30 21:02:39,511 - SBARDS.EnhancedResponse - ERROR - Error in enhanced monitoring: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,511 - SBARDS.EnhancedResponse - ERROR - Error updating models with feedback: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,513 - SBARDS.EnhancedResponse - INFO - Enhanced analysis processing completed successfully
2025-05-30 21:02:39,513 - SBARDS.SystemIntegration - INFO - Comprehensive analysis completed for C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\safe_document.txt in 0.04s
2025-05-30 21:02:39,514 - SBARDS.SystemIntegration - INFO - Starting comprehensive analysis for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\suspicious_script.bat
2025-05-30 21:02:39,514 - SBARDS.EnhancedResponse - INFO - Processing enhanced analysis results with ML integration
2025-05-30 21:02:39,514 - SBARDS.MLModelsManager - ERROR - Error in threat level prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,514 - SBARDS.MLModelsManager - ERROR - Error in malware family detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,514 - SBARDS.MLModelsManager - ERROR - Error in evasion detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,515 - SBARDS.MLModelsManager - ERROR - Error in behavioral analysis: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,515 - SBARDS.MLModelsManager - ERROR - Error in anomaly detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,515 - SBARDS.EnhancedResponse - ERROR - Error in enhanced threat assessment: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,515 - SBARDS.ComprehensiveResponse - INFO - Executing safe file strategy for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\suspicious_script.bat
2025-05-30 21:02:39,529 - SBARDS.ComprehensiveResponse - INFO - Safe file database updated: 
2025-05-30 21:02:39,529 - SBARDS.ComprehensiveResponse - INFO - ML models updated with safe file data
2025-05-30 21:02:39,530 - SBARDS.ComprehensiveResponse - INFO - Normal access policies applied: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\suspicious_script.bat
2025-05-30 21:02:39,531 - SBARDS.ComprehensiveResponse - INFO - Light monitoring setup for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\suspicious_script.bat
2025-05-30 21:02:39,531 - SBARDS.ComprehensiveResponse - INFO - Safe file notification: suspicious_script.bat
2025-05-30 21:02:39,531 - SBARDS.ComprehensiveResponse - INFO - Safe file strategy completed successfully for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\suspicious_script.bat
2025-05-30 21:02:39,531 - SBARDS.EnhancedResponse - ERROR - Error in enhanced monitoring: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,532 - SBARDS.EnhancedResponse - ERROR - Error updating models with feedback: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,532 - SBARDS.EnhancedResponse - INFO - Enhanced analysis processing completed successfully
2025-05-30 21:02:39,532 - SBARDS.SystemIntegration - INFO - Comprehensive analysis completed for C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\suspicious_script.bat in 0.02s
2025-05-30 21:02:39,532 - SBARDS.SystemIntegration - INFO - Starting comprehensive analysis for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\malware.exe
2025-05-30 21:02:39,532 - SBARDS.EnhancedResponse - INFO - Processing enhanced analysis results with ML integration
2025-05-30 21:02:39,532 - SBARDS.MLModelsManager - ERROR - Error in threat level prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,532 - SBARDS.MLModelsManager - ERROR - Error in malware family detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,532 - SBARDS.MLModelsManager - ERROR - Error in evasion detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,532 - SBARDS.MLModelsManager - ERROR - Error in behavioral analysis: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,532 - SBARDS.MLModelsManager - ERROR - Error in anomaly detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,532 - SBARDS.EnhancedResponse - ERROR - Error in enhanced threat assessment: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,532 - SBARDS.ComprehensiveResponse - INFO - Executing safe file strategy for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\malware.exe
2025-05-30 21:02:39,548 - SBARDS.ComprehensiveResponse - INFO - Safe file database updated: 
2025-05-30 21:02:39,549 - SBARDS.ComprehensiveResponse - INFO - ML models updated with safe file data
2025-05-30 21:02:39,549 - SBARDS.ComprehensiveResponse - INFO - Normal access policies applied: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\malware.exe
2025-05-30 21:02:39,550 - SBARDS.ComprehensiveResponse - INFO - Light monitoring setup for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\malware.exe
2025-05-30 21:02:39,550 - SBARDS.ComprehensiveResponse - INFO - Safe file notification: malware.exe
2025-05-30 21:02:39,550 - SBARDS.ComprehensiveResponse - INFO - Safe file strategy completed successfully for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\malware.exe
2025-05-30 21:02:39,550 - SBARDS.EnhancedResponse - ERROR - Error in enhanced monitoring: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,550 - SBARDS.EnhancedResponse - ERROR - Error updating models with feedback: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,551 - SBARDS.EnhancedResponse - INFO - Enhanced analysis processing completed successfully
2025-05-30 21:02:39,551 - SBARDS.SystemIntegration - INFO - Comprehensive analysis completed for C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\malware.exe in 0.02s
2025-05-30 21:02:39,552 - SBARDS.SystemIntegration - INFO - Starting comprehensive analysis for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\archive.zip
2025-05-30 21:02:39,553 - SBARDS.EnhancedResponse - INFO - Processing enhanced analysis results with ML integration
2025-05-30 21:02:39,553 - SBARDS.MLModelsManager - ERROR - Error in threat level prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,553 - SBARDS.MLModelsManager - ERROR - Error in malware family detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,553 - SBARDS.MLModelsManager - ERROR - Error in evasion detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,553 - SBARDS.MLModelsManager - ERROR - Error in behavioral analysis: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,553 - SBARDS.MLModelsManager - ERROR - Error in anomaly detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,553 - SBARDS.EnhancedResponse - ERROR - Error in enhanced threat assessment: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,553 - SBARDS.ComprehensiveResponse - INFO - Executing safe file strategy for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\archive.zip
2025-05-30 21:02:39,567 - SBARDS.ComprehensiveResponse - INFO - Safe file database updated: 
2025-05-30 21:02:39,567 - SBARDS.ComprehensiveResponse - INFO - ML models updated with safe file data
2025-05-30 21:02:39,567 - SBARDS.ComprehensiveResponse - INFO - Normal access policies applied: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\archive.zip
2025-05-30 21:02:39,567 - SBARDS.ComprehensiveResponse - INFO - Light monitoring setup for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\archive.zip
2025-05-30 21:02:39,567 - SBARDS.ComprehensiveResponse - INFO - Safe file notification: archive.zip
2025-05-30 21:02:39,567 - SBARDS.ComprehensiveResponse - INFO - Safe file strategy completed successfully for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\archive.zip
2025-05-30 21:02:39,567 - SBARDS.EnhancedResponse - ERROR - Error in enhanced monitoring: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,567 - SBARDS.EnhancedResponse - ERROR - Error updating models with feedback: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,567 - SBARDS.EnhancedResponse - INFO - Enhanced analysis processing completed successfully
2025-05-30 21:02:39,567 - SBARDS.SystemIntegration - INFO - Comprehensive analysis completed for C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\archive.zip in 0.01s
2025-05-30 21:02:39,567 - SBARDS.SystemIntegration - INFO - Processing batch of 4 files with max 3 concurrent
2025-05-30 21:02:39,567 - SBARDS.SystemIntegration - INFO - Starting comprehensive analysis for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\safe_document.txt
2025-05-30 21:02:39,572 - SBARDS.EnhancedResponse - INFO - Processing enhanced analysis results with ML integration
2025-05-30 21:02:39,572 - SBARDS.MLModelsManager - ERROR - Error in threat level prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,573 - SBARDS.MLModelsManager - ERROR - Error in malware family detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,573 - SBARDS.MLModelsManager - ERROR - Error in evasion detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,574 - SBARDS.MLModelsManager - ERROR - Error in behavioral analysis: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,574 - SBARDS.MLModelsManager - ERROR - Error in anomaly detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,574 - SBARDS.EnhancedResponse - ERROR - Error in enhanced threat assessment: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,574 - SBARDS.ComprehensiveResponse - INFO - Executing safe file strategy for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\safe_document.txt
2025-05-30 21:02:39,588 - SBARDS.ComprehensiveResponse - INFO - Safe file database updated: 
2025-05-30 21:02:39,588 - SBARDS.ComprehensiveResponse - INFO - ML models updated with safe file data
2025-05-30 21:02:39,588 - SBARDS.ComprehensiveResponse - INFO - Normal access policies applied: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\safe_document.txt
2025-05-30 21:02:39,589 - SBARDS.ComprehensiveResponse - INFO - Light monitoring setup for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\safe_document.txt
2025-05-30 21:02:39,589 - SBARDS.ComprehensiveResponse - INFO - Safe file notification: safe_document.txt
2025-05-30 21:02:39,589 - SBARDS.ComprehensiveResponse - INFO - Safe file strategy completed successfully for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\safe_document.txt
2025-05-30 21:02:39,589 - SBARDS.EnhancedResponse - ERROR - Error in enhanced monitoring: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,589 - SBARDS.EnhancedResponse - ERROR - Error updating models with feedback: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,589 - SBARDS.EnhancedResponse - INFO - Enhanced analysis processing completed successfully
2025-05-30 21:02:39,589 - SBARDS.SystemIntegration - INFO - Comprehensive analysis completed for C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\safe_document.txt in 0.02s
2025-05-30 21:02:39,589 - SBARDS.SystemIntegration - INFO - Starting comprehensive analysis for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\suspicious_script.bat
2025-05-30 21:02:39,589 - SBARDS.EnhancedResponse - INFO - Processing enhanced analysis results with ML integration
2025-05-30 21:02:39,589 - SBARDS.MLModelsManager - ERROR - Error in threat level prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,589 - SBARDS.MLModelsManager - ERROR - Error in malware family detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,589 - SBARDS.MLModelsManager - ERROR - Error in evasion detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,589 - SBARDS.MLModelsManager - ERROR - Error in behavioral analysis: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,589 - SBARDS.MLModelsManager - ERROR - Error in anomaly detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,589 - SBARDS.EnhancedResponse - ERROR - Error in enhanced threat assessment: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,589 - SBARDS.ComprehensiveResponse - INFO - Executing safe file strategy for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\suspicious_script.bat
2025-05-30 21:02:39,605 - SBARDS.ComprehensiveResponse - INFO - Safe file database updated: 
2025-05-30 21:02:39,606 - SBARDS.ComprehensiveResponse - INFO - ML models updated with safe file data
2025-05-30 21:02:39,606 - SBARDS.ComprehensiveResponse - INFO - Normal access policies applied: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\suspicious_script.bat
2025-05-30 21:02:39,608 - SBARDS.ComprehensiveResponse - INFO - Light monitoring setup for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\suspicious_script.bat
2025-05-30 21:02:39,608 - SBARDS.ComprehensiveResponse - INFO - Safe file notification: suspicious_script.bat
2025-05-30 21:02:39,608 - SBARDS.ComprehensiveResponse - INFO - Safe file strategy completed successfully for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\suspicious_script.bat
2025-05-30 21:02:39,608 - SBARDS.EnhancedResponse - ERROR - Error in enhanced monitoring: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,608 - SBARDS.EnhancedResponse - ERROR - Error updating models with feedback: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,608 - SBARDS.EnhancedResponse - INFO - Enhanced analysis processing completed successfully
2025-05-30 21:02:39,608 - SBARDS.SystemIntegration - INFO - Comprehensive analysis completed for C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\suspicious_script.bat in 0.02s
2025-05-30 21:02:39,608 - SBARDS.SystemIntegration - INFO - Starting comprehensive analysis for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\malware.exe
2025-05-30 21:02:39,608 - SBARDS.EnhancedResponse - INFO - Processing enhanced analysis results with ML integration
2025-05-30 21:02:39,608 - SBARDS.MLModelsManager - ERROR - Error in threat level prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,608 - SBARDS.MLModelsManager - ERROR - Error in malware family detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,608 - SBARDS.MLModelsManager - ERROR - Error in evasion detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,608 - SBARDS.MLModelsManager - ERROR - Error in behavioral analysis: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,608 - SBARDS.MLModelsManager - ERROR - Error in anomaly detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,608 - SBARDS.EnhancedResponse - ERROR - Error in enhanced threat assessment: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,608 - SBARDS.ComprehensiveResponse - INFO - Executing safe file strategy for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\malware.exe
2025-05-30 21:02:39,622 - SBARDS.ComprehensiveResponse - INFO - Safe file database updated: 
2025-05-30 21:02:39,622 - SBARDS.ComprehensiveResponse - INFO - ML models updated with safe file data
2025-05-30 21:02:39,622 - SBARDS.ComprehensiveResponse - INFO - Normal access policies applied: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\malware.exe
2025-05-30 21:02:39,622 - SBARDS.ComprehensiveResponse - INFO - Light monitoring setup for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\malware.exe
2025-05-30 21:02:39,622 - SBARDS.ComprehensiveResponse - INFO - Safe file notification: malware.exe
2025-05-30 21:02:39,622 - SBARDS.ComprehensiveResponse - INFO - Safe file strategy completed successfully for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\malware.exe
2025-05-30 21:02:39,622 - SBARDS.EnhancedResponse - ERROR - Error in enhanced monitoring: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,622 - SBARDS.EnhancedResponse - ERROR - Error updating models with feedback: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,622 - SBARDS.EnhancedResponse - INFO - Enhanced analysis processing completed successfully
2025-05-30 21:02:39,622 - SBARDS.SystemIntegration - INFO - Comprehensive analysis completed for C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\malware.exe in 0.01s
2025-05-30 21:02:39,622 - SBARDS.SystemIntegration - INFO - Starting comprehensive analysis for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\archive.zip
2025-05-30 21:02:39,622 - SBARDS.EnhancedResponse - INFO - Processing enhanced analysis results with ML integration
2025-05-30 21:02:39,622 - SBARDS.MLModelsManager - ERROR - Error in threat level prediction: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,628 - SBARDS.MLModelsManager - ERROR - Error in malware family detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,628 - SBARDS.MLModelsManager - ERROR - Error in evasion detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,629 - SBARDS.MLModelsManager - ERROR - Error in behavioral analysis: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,629 - SBARDS.MLModelsManager - ERROR - Error in anomaly detection: This StandardScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-05-30 21:02:39,629 - SBARDS.EnhancedResponse - ERROR - Error in enhanced threat assessment: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,629 - SBARDS.ComprehensiveResponse - INFO - Executing safe file strategy for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\archive.zip
2025-05-30 21:02:39,642 - SBARDS.ComprehensiveResponse - INFO - Safe file database updated: 
2025-05-30 21:02:39,642 - SBARDS.ComprehensiveResponse - INFO - ML models updated with safe file data
2025-05-30 21:02:39,642 - SBARDS.ComprehensiveResponse - INFO - Normal access policies applied: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\archive.zip
2025-05-30 21:02:39,642 - SBARDS.ComprehensiveResponse - INFO - Light monitoring setup for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\archive.zip
2025-05-30 21:02:39,642 - SBARDS.ComprehensiveResponse - INFO - Safe file notification: archive.zip
2025-05-30 21:02:39,642 - SBARDS.ComprehensiveResponse - INFO - Safe file strategy completed successfully for: C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\archive.zip
2025-05-30 21:02:39,642 - SBARDS.EnhancedResponse - ERROR - Error in enhanced monitoring: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,642 - SBARDS.EnhancedResponse - ERROR - Error updating models with feedback: argument 1 (impossible<bad format char>)
2025-05-30 21:02:39,642 - SBARDS.EnhancedResponse - INFO - Enhanced analysis processing completed successfully
2025-05-30 21:02:39,642 - SBARDS.SystemIntegration - INFO - Comprehensive analysis completed for C:\Users\<USER>\AppData\Local\Temp\sbards_demo_4hsm1rds\archive.zip in 0.02s
2025-05-30 21:02:39,642 - SBARDS.SystemIntegration - INFO - Batch processing completed: 4 files processed
2025-05-30 21:02:39,642 - SBARDS.EnhancedResponse - ERROR - Error getting enhanced statistics: 'ComprehensiveResponseSystem' object has no attribute 'get_response_statistics'
2025-05-30 21:02:39,799 - SBARDS.MLModelsManager - INFO - ML Models Manager shutdown complete
2025-05-30 21:02:39,799 - SBARDS.EnhancedResponse - INFO - Enhanced Response System shutdown complete
2025-05-30 21:02:39,799 - SBARDS.SystemIntegration - INFO - SBARDS System Integration shutdown complete
