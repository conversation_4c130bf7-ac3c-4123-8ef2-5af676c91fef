/**
 * SBARDS Configuration Monitor Implementation
 * Registry monitoring, configuration file tracking, persistence detection
 */

#include "api_hooking.hpp"
#include <iostream>
#include <sstream>
#include <fstream>
#include <algorithm>
#include <regex>
#include <thread>

#ifdef _WIN32
    #include <windows.h>
    #include <winreg.h>
    #include <shlwapi.h>
    #pragma comment(lib, "shlwapi.lib")
    #pragma comment(lib, "advapi32.lib")
#else
    #include <sys/inotify.h>
    #include <unistd.h>
    #include <dirent.h>
    #include <sys/stat.h>
#endif

namespace sbards {
namespace hooking {

// ConfigurationMonitor Implementation
ConfigurationMonitor::ConfigurationMonitor(const HookConfig& config)
    : config_(config), monitoring_(false) {
    
    // Initialize critical registry keys
    for (const auto& key : config_.critical_registry_keys) {
        critical_keys_.insert(key);
    }
    
    // Add default critical registry keys
    critical_keys_.insert("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run");
    critical_keys_.insert("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run");
    critical_keys_.insert("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce");
    critical_keys_.insert("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services");
    critical_keys_.insert("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon");
    critical_keys_.insert("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies");
    critical_keys_.insert("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\SafeBoot");
    
    // Initialize baseline
    if (config_.registry_monitoring) {
        // Create baseline of critical registry keys
        // This would be done during initialization
    }
}

ConfigurationMonitor::~ConfigurationMonitor() {
    stop_monitoring();
}

bool ConfigurationMonitor::start_monitoring() {
    if (monitoring_) {
        return true;
    }
    
    try {
#ifdef _WIN32
        monitor_windows_registry();
#else
        monitor_linux_config_files();
#endif
        monitoring_ = true;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to start configuration monitoring: " << e.what() << std::endl;
        return false;
    }
}

void ConfigurationMonitor::stop_monitoring() {
    monitoring_ = false;
}

bool ConfigurationMonitor::is_monitoring() const {
    return monitoring_;
}

std::vector<ConfigChangeInfo> ConfigurationMonitor::get_config_events() const {
    std::lock_guard<std::mutex> lock(events_mutex_);
    return events_;
}

void ConfigurationMonitor::set_event_callback(std::function<void(const ConfigChangeInfo&)> callback) {
    callback_ = callback;
}

bool ConfigurationMonitor::detect_persistence_mechanisms(const std::vector<ConfigChangeInfo>& changes) {
    for (const auto& change : changes) {
        if (is_persistence_mechanism(change)) {
            return true;
        }
    }
    return false;
}

bool ConfigurationMonitor::detect_security_modifications(const std::vector<ConfigChangeInfo>& changes) {
    for (const auto& change : changes) {
        if (is_security_related_change(change)) {
            return true;
        }
    }
    return false;
}

std::vector<std::string> ConfigurationMonitor::analyze_startup_modifications(const std::vector<ConfigChangeInfo>& changes) {
    std::vector<std::string> startup_changes;
    
    for (const auto& change : changes) {
        if (change.change_type == "registry" || change.change_type == "startup") {
            // Check for startup-related registry keys
            std::vector<std::string> startup_keys = {
                "Run", "RunOnce", "RunServices", "RunServicesOnce",
                "Winlogon", "Shell", "Userinit", "TaskMan"
            };
            
            for (const auto& key : startup_keys) {
                if (change.key_path.find(key) != std::string::npos) {
                    startup_changes.push_back("Startup modification: " + change.key_path + " = " + change.new_value);
                    break;
                }
            }
        }
    }
    
    return startup_changes;
}

bool ConfigurationMonitor::is_critical_registry_key(const std::string& key_path) {
    return critical_keys_.find(key_path) != critical_keys_.end();
}

bool ConfigurationMonitor::is_security_related_change(const ConfigChangeInfo& change) {
    // Check for security-related registry modifications
    std::vector<std::string> security_patterns = {
        "DisableAntiSpyware",
        "DisableRealtimeMonitoring", 
        "DisableBehaviorMonitoring",
        "DisableOnAccessProtection",
        "DisableIOAVProtection",
        "DisableScriptScanning",
        "DisableArchiveScanning",
        "DisableIntrusionPreventionSystem",
        "DisableEmailScanning",
        "TamperProtection",
        "EnableControlledFolderAccess",
        "ExclusionPath",
        "ExclusionProcess",
        "ExclusionExtension",
        "Windows Defender",
        "Windows Security",
        "Firewall",
        "UAC",
        "SEHOP",
        "DEP",
        "ASLR"
    };
    
    for (const auto& pattern : security_patterns) {
        if (change.key_path.find(pattern) != std::string::npos ||
            change.value_name.find(pattern) != std::string::npos ||
            change.new_value.find(pattern) != std::string::npos) {
            return true;
        }
    }
    
    return false;
}

bool ConfigurationMonitor::is_persistence_mechanism(const ConfigChangeInfo& change) {
    // Check for common persistence mechanisms
    std::vector<std::string> persistence_patterns = {
        "\\Run\\",
        "\\RunOnce\\",
        "\\RunServices\\",
        "\\RunServicesOnce\\",
        "\\Winlogon\\Shell",
        "\\Winlogon\\Userinit",
        "\\Winlogon\\TaskMan",
        "\\Services\\",
        "\\CurrentVersion\\Windows\\Load",
        "\\CurrentVersion\\Windows\\Run",
        "\\Explorer\\Browser Helper Objects",
        "\\Explorer\\ShellExecuteHooks",
        "\\SharedTaskScheduler",
        "\\SessionManager\\BootExecute",
        "\\Image File Execution Options",
        "\\AppInit_DLLs",
        "\\Notify",
        "\\ServiceDll"
    };
    
    for (const auto& pattern : persistence_patterns) {
        if (change.key_path.find(pattern) != std::string::npos) {
            return true;
        }
    }
    
    // Check for service installations
    if (change.change_type == "service" && change.key_path.find("Services") != std::string::npos) {
        return true;
    }
    
    // Check for scheduled task creation
    if (change.change_type == "startup" && change.key_path.find("Task") != std::string::npos) {
        return true;
    }
    
    return false;
}

#ifdef _WIN32
void ConfigurationMonitor::monitor_windows_registry() {
    setup_registry_hooks();
}

void ConfigurationMonitor::setup_registry_hooks() {
    std::thread monitor_thread([this]() {
        // Monitor registry changes using RegNotifyChangeKeyValue
        std::vector<HKEY> monitored_keys;
        std::vector<HANDLE> events;
        
        // Open and monitor critical registry keys
        std::vector<std::pair<HKEY, std::string>> key_paths = {
            {HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"},
            {HKEY_CURRENT_USER, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"},
            {HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce"},
            {HKEY_LOCAL_MACHINE, "SYSTEM\\CurrentControlSet\\Services"},
            {HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon"}
        };
        
        for (const auto& key_pair : key_paths) {
            HKEY hKey;
            if (RegOpenKeyExA(key_pair.first, key_pair.second.c_str(), 0, KEY_NOTIFY, &hKey) == ERROR_SUCCESS) {
                HANDLE hEvent = CreateEvent(nullptr, TRUE, FALSE, nullptr);
                if (hEvent) {
                    if (RegNotifyChangeKeyValue(hKey, TRUE, REG_NOTIFY_CHANGE_NAME | REG_NOTIFY_CHANGE_LAST_SET,
                                              hEvent, TRUE) == ERROR_SUCCESS) {
                        monitored_keys.push_back(hKey);
                        events.push_back(hEvent);
                    }
                }
            }
        }
        
        // Monitor for changes
        while (monitoring_ && !events.empty()) {
            DWORD wait_result = WaitForMultipleObjects(events.size(), events.data(), FALSE, 1000);
            
            if (wait_result >= WAIT_OBJECT_0 && wait_result < WAIT_OBJECT_0 + events.size()) {
                size_t index = wait_result - WAIT_OBJECT_0;
                
                // Registry change detected
                ConfigChangeInfo change_info;
                change_info.change_type = "registry";
                change_info.key_path = key_paths[index].second;
                change_info.process_name = "unknown"; // Would need additional tracking
                change_info.is_security_related = is_security_related_change(change_info);
                change_info.is_persistence_mechanism = is_persistence_mechanism(change_info);
                
                {
                    std::lock_guard<std::mutex> lock(events_mutex_);
                    events_.push_back(change_info);
                    
                    if (events_.size() > 10000) {
                        events_.erase(events_.begin(), events_.begin() + 1000);
                    }
                }
                
                if (callback_) {
                    callback_(change_info);
                }
                
                // Reset the event and continue monitoring
                ResetEvent(events[index]);
                RegNotifyChangeKeyValue(monitored_keys[index], TRUE, 
                                      REG_NOTIFY_CHANGE_NAME | REG_NOTIFY_CHANGE_LAST_SET,
                                      events[index], TRUE);
            }
        }
        
        // Cleanup
        for (HKEY hKey : monitored_keys) {
            RegCloseKey(hKey);
        }
        for (HANDLE hEvent : events) {
            CloseHandle(hEvent);
        }
    });
    
    monitor_thread.detach();
}

// Static hook functions for registry operations
LONG WINAPI ConfigurationMonitor::hooked_RegSetValueExW(HKEY hKey, LPCWSTR lpValueName, DWORD Reserved,
                                                        DWORD dwType, const BYTE* lpData, DWORD cbData) {
    // Log registry modification
    std::wcout << L"Registry modification: " << (lpValueName ? lpValueName : L"(Default)") << std::endl;
    
    // Call original function
    return RegSetValueExW(hKey, lpValueName, Reserved, dwType, lpData, cbData);
}

LONG WINAPI ConfigurationMonitor::hooked_RegCreateKeyExW(HKEY hKey, LPCWSTR lpSubKey, DWORD Reserved,
                                                         LPWSTR lpClass, DWORD dwOptions, REGSAM samDesired,
                                                         LPSECURITY_ATTRIBUTES lpSecurityAttributes,
                                                         PHKEY phkResult, LPDWORD lpdwDisposition) {
    // Log registry key creation
    std::wcout << L"Registry key creation: " << (lpSubKey ? lpSubKey : L"(Unknown)") << std::endl;
    
    // Call original function
    return RegCreateKeyExW(hKey, lpSubKey, Reserved, lpClass, dwOptions, samDesired,
                          lpSecurityAttributes, phkResult, lpdwDisposition);
}

#else
void ConfigurationMonitor::monitor_linux_config_files() {
    setup_config_file_monitoring();
}

void ConfigurationMonitor::setup_config_file_monitoring() {
    std::thread monitor_thread([this]() {
        int inotify_fd = inotify_init();
        if (inotify_fd < 0) {
            std::cerr << "Failed to initialize inotify for config monitoring" << std::endl;
            return;
        }
        
        // Monitor critical configuration directories
        std::vector<std::string> config_dirs = {
            "/etc",
            "/etc/systemd/system",
            "/etc/init.d",
            "/etc/cron.d",
            "/etc/crontab",
            "/var/spool/cron",
            "/home"  // For user-specific configs
        };
        
        std::vector<int> watch_descriptors;
        
        for (const auto& dir : config_dirs) {
            int wd = inotify_add_watch(inotify_fd, dir.c_str(),
                                      IN_CREATE | IN_DELETE | IN_MODIFY | IN_MOVED_FROM | IN_MOVED_TO);
            if (wd >= 0) {
                watch_descriptors.push_back(wd);
            }
        }
        
        char buffer[4096];
        while (monitoring_) {
            ssize_t length = read(inotify_fd, buffer, sizeof(buffer));
            if (length < 0) {
                continue;
            }
            
            int i = 0;
            while (i < length) {
                struct inotify_event* event = (struct inotify_event*)&buffer[i];
                
                ConfigChangeInfo change_info;
                change_info.key_path = event->name ? event->name : "unknown";
                change_info.process_name = "unknown"; // Would need additional tracking
                
                if (event->mask & IN_CREATE) {
                    change_info.change_type = "config_file_create";
                } else if (event->mask & IN_DELETE) {
                    change_info.change_type = "config_file_delete";
                } else if (event->mask & IN_MODIFY) {
                    change_info.change_type = "config_file_modify";
                } else if (event->mask & (IN_MOVED_FROM | IN_MOVED_TO)) {
                    change_info.change_type = "config_file_move";
                }
                
                // Check if this is a critical configuration file
                std::vector<std::string> critical_files = {
                    "passwd", "shadow", "sudoers", "hosts", "resolv.conf",
                    "crontab", "fstab", "inittab", "profile", "bashrc",
                    ".ssh/authorized_keys", ".bashrc", ".profile"
                };
                
                bool is_critical = false;
                for (const auto& file : critical_files) {
                    if (change_info.key_path.find(file) != std::string::npos) {
                        is_critical = true;
                        break;
                    }
                }
                
                change_info.is_security_related = is_critical;
                change_info.is_persistence_mechanism = is_persistence_mechanism(change_info);
                
                {
                    std::lock_guard<std::mutex> lock(events_mutex_);
                    events_.push_back(change_info);
                    
                    if (events_.size() > 10000) {
                        events_.erase(events_.begin(), events_.begin() + 1000);
                    }
                }
                
                if (callback_) {
                    callback_(change_info);
                }
                
                i += sizeof(struct inotify_event) + event->len;
            }
        }
        
        // Cleanup
        for (int wd : watch_descriptors) {
            inotify_rm_watch(inotify_fd, wd);
        }
        close(inotify_fd);
    });
    
    monitor_thread.detach();
}
#endif

} // namespace hooking
} // namespace sbards
