#include "response_engine.hpp"
#include "isolation_manager.hpp"
#include "notification_system.hpp"
#include "quarantine_manager.hpp"
#include "permission_manager.hpp"
#include "honeypot_manager.hpp"
#include "recovery_system.hpp"

#include <iostream>
#include <fstream>
#include <sstream>
#include <random>
#include <iomanip>
#include <filesystem>
#include <algorithm>

#ifdef _WIN32
    #include <shlobj.h>
    #include <lmcons.h>
#else
    #include <pwd.h>
    #include <grp.h>
    #include <sys/utsname.h>
#endif

namespace fs = std::filesystem;

ResponseEngine::ResponseEngine(const ResponseConfig& config)
    : config_(config), running_(false) {

    // Initialize statistics
    response_statistics_["total_responses"] = 0;
    response_statistics_["safe_files"] = 0;
    response_statistics_["suspicious_files"] = 0;
    response_statistics_["malicious_files"] = 0;
    response_statistics_["critical_threats"] = 0;
    response_statistics_["advanced_threats"] = 0;
    response_statistics_["quarantined_files"] = 0;
    response_statistics_["isolated_processes"] = 0;
    response_statistics_["notifications_sent"] = 0;
    response_statistics_["errors"] = 0;
}

ResponseEngine::~ResponseEngine() {
    shutdown();
}

bool ResponseEngine::initialize() {
    try {
        std::cout << "[ResponseEngine] Initializing SBARDS Response Engine..." << std::endl;

        // Create base directories
        if (!fs::exists(config_.base_directory)) {
            fs::create_directories(config_.base_directory);
        }

        // Initialize platform-specific security
#ifdef _WIN32
        if (!initialize_windows_security()) {
            std::cerr << "[ResponseEngine] Failed to initialize Windows security" << std::endl;
            return false;
        }
#else
        if (!initialize_linux_security()) {
            std::cerr << "[ResponseEngine] Failed to initialize Linux security" << std::endl;
            return false;
        }
#endif

        // Initialize component managers
        if (!initialize_components()) {
            std::cerr << "[ResponseEngine] Failed to initialize components" << std::endl;
            return false;
        }

        // Start worker thread
        running_ = true;
        response_worker_thread_ = std::thread(&ResponseEngine::response_worker_loop, this);

        std::cout << "[ResponseEngine] Response Engine initialized successfully" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[ResponseEngine] Initialization error: " << e.what() << std::endl;
        return false;
    }
}

void ResponseEngine::shutdown() {
    if (running_) {
        std::cout << "[ResponseEngine] Shutting down Response Engine..." << std::endl;

        // Stop worker thread
        running_ = false;
        response_queue_cv_.notify_all();

        if (response_worker_thread_.joinable()) {
            response_worker_thread_.join();
        }

        // Cleanup components
        cleanup_components();

        // Cleanup platform-specific resources
#ifdef _WIN32
        cleanup_windows_security();
#else
        cleanup_linux_security();
#endif

        std::cout << "[ResponseEngine] Response Engine shutdown complete" << std::endl;
    }
}

bool ResponseEngine::is_running() const {
    return running_;
}

ResponseResult ResponseEngine::process_analysis_results(const AnalysisResults& results) {
    if (!running_) {
        ResponseResult error_result;
        error_result.success = false;
        error_result.error_message = "Response Engine is not running";
        return error_result;
    }

    try {
        // Create promise for async execution
        std::promise<ResponseResult> result_promise;
        auto result_future = result_promise.get_future();

        // Add to processing queue
        {
            std::lock_guard<std::mutex> lock(response_queue_mutex_);
            response_queue_.emplace(results, std::move(result_promise));
        }
        response_queue_cv_.notify_one();

        // Wait for result with timeout
        auto timeout = std::chrono::seconds(config_.response_timeout_seconds);
        if (result_future.wait_for(timeout) == std::future_status::timeout) {
            ResponseResult timeout_result;
            timeout_result.success = false;
            timeout_result.error_message = "Response processing timeout";
            return timeout_result;
        }

        return result_future.get();

    } catch (const std::exception& e) {
        ResponseResult error_result;
        error_result.success = false;
        error_result.error_message = std::string("Processing error: ") + e.what();
        return error_result;
    }
}

ResponseResult ResponseEngine::execute_response_action(ResponseAction action,
                                                     const std::string& file_path,
                                                     const std::map<std::string, std::string>& metadata) {
    auto start_time = std::chrono::high_resolution_clock::now();
    ResponseResult result;
    result.execution_timestamp = std::chrono::system_clock::now();

    try {
        switch (action) {
            case ResponseAction::ALLOW_ACCESS:
                result = permission_manager_->allow_access(file_path, metadata);
                break;

            case ResponseAction::RESTRICT_ACCESS:
                result = permission_manager_->restrict_access(file_path, metadata);
                break;

            case ResponseAction::QUARANTINE:
                result = quarantine_manager_->quarantine_file(file_path, metadata);
                break;

            case ResponseAction::ISOLATE:
                result = isolation_manager_->isolate_process(file_path, metadata);
                break;

            case ResponseAction::TERMINATE:
                result = isolation_manager_->terminate_process(file_path, metadata);
                break;

            case ResponseAction::HONEYPOT_REDIRECT:
                result = honeypot_manager_->redirect_to_honeypot(file_path, metadata);
                break;

            case ResponseAction::DEEP_ANALYSIS:
                // Trigger additional analysis
                result.success = true;
                result.actions_taken.push_back(action);
                break;

            case ResponseAction::NETWORK_ISOLATION:
                result = isolation_manager_->isolate_network(file_path, metadata);
                break;

            case ResponseAction::PROCESS_TERMINATION:
                result = isolation_manager_->terminate_all_related_processes(file_path, metadata);
                break;

            case ResponseAction::SYSTEM_LOCKDOWN:
                result = isolation_manager_->system_lockdown(metadata);
                break;

            default:
                result.success = false;
                result.error_message = "Unknown response action";
                break;
        }

        // Calculate execution time
        auto end_time = std::chrono::high_resolution_clock::now();
        result.execution_time_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();

        // Update statistics
        if (result.success) {
            update_statistics("successful_actions");
        } else {
            update_statistics("failed_actions");
        }

        return result;

    } catch (const std::exception& e) {
        auto end_time = std::chrono::high_resolution_clock::now();
        result.success = false;
        result.error_message = std::string("Action execution error: ") + e.what();
        result.execution_time_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
        update_statistics("errors");
        return result;
    }
}

std::map<std::string, uint64_t> ResponseEngine::get_response_statistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return response_statistics_;
}

std::vector<std::string> ResponseEngine::get_active_sessions() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    std::vector<std::string> sessions;
    for (const auto& session : active_sessions_) {
        sessions.push_back(session.first);
    }
    return sessions;
}

bool ResponseEngine::cancel_response_session(const std::string& session_id) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    auto it = active_sessions_.find(session_id);
    if (it != active_sessions_.end()) {
        active_sessions_.erase(it);
        return true;
    }
    return false;
}

bool ResponseEngine::update_configuration(const ResponseConfig& new_config) {
    try {
        // Validate new configuration
        if (new_config.base_directory.empty()) {
            return false;
        }

        // Update configuration
        config_ = new_config;

        // Reinitialize components if needed
        if (running_) {
            // Note: In a production system, you might want to implement
            // hot-reloading of configuration without stopping the engine
            std::cout << "[ResponseEngine] Configuration updated (restart required for full effect)" << std::endl;
        }

        return true;

    } catch (const std::exception& e) {
        std::cerr << "[ResponseEngine] Configuration update error: " << e.what() << std::endl;
        return false;
    }
}

ResponseConfig ResponseEngine::get_configuration() const {
    return config_;
}

void ResponseEngine::response_worker_loop() {
    std::cout << "[ResponseEngine] Response worker thread started" << std::endl;

    while (running_) {
        std::unique_lock<std::mutex> lock(response_queue_mutex_);

        // Wait for work or shutdown signal
        response_queue_cv_.wait(lock, [this] {
            return !response_queue_.empty() || !running_;
        });

        if (!running_) {
            break;
        }

        // Process all queued responses
        while (!response_queue_.empty() && running_) {
            auto work_item = std::move(response_queue_.front());
            response_queue_.pop();
            lock.unlock();

            // Process the analysis results
            AnalysisResults results = work_item.first;
            std::promise<ResponseResult> promise = std::move(work_item.second);

            try {
                ResponseResult response_result;

                // Execute appropriate strategy based on threat level
                switch (results.threat_level) {
                    case ThreatLevel::SAFE:
                        response_result = execute_safe_file_strategy(results);
                        break;
                    case ThreatLevel::SUSPICIOUS:
                        response_result = execute_suspicious_file_strategy(results);
                        break;
                    case ThreatLevel::MALICIOUS:
                        response_result = execute_malicious_file_strategy(results);
                        break;
                    case ThreatLevel::CRITICAL:
                        response_result = execute_critical_threat_strategy(results);
                        break;
                    case ThreatLevel::ADVANCED_PERSISTENT:
                        response_result = execute_advanced_persistent_threat_strategy(results);
                        break;
                    default:
                        response_result.success = false;
                        response_result.error_message = "Unknown threat level";
                        break;
                }

                // Log the response action
                log_response_action(response_result, results);

                // Update statistics
                update_statistics("total_responses");

                // Fulfill the promise
                promise.set_value(response_result);

            } catch (const std::exception& e) {
                ResponseResult error_result;
                error_result.success = false;
                error_result.error_message = std::string("Worker error: ") + e.what();
                promise.set_value(error_result);
                update_statistics("errors");
            }

            lock.lock();
        }
    }

    std::cout << "[ResponseEngine] Response worker thread stopped" << std::endl;
}

ResponseResult ResponseEngine::execute_safe_file_strategy(const AnalysisResults& results) {
    ResponseResult result;
    result.success = true;
    result.execution_timestamp = std::chrono::system_clock::now();

    try {
        std::cout << "[ResponseEngine] Executing safe file strategy for: " << results.file_path << std::endl;

        // 1. Allow normal access
        auto access_result = permission_manager_->allow_access(results.file_path, results.metadata);
        if (access_result.success) {
            result.actions_taken.push_back(ResponseAction::ALLOW_ACCESS);
        }

        // 2. Setup light monitoring
        auto monitor_result = isolation_manager_->setup_light_monitoring(results.file_path, results.metadata);
        if (monitor_result.success) {
            result.actions_taken.push_back(ResponseAction::DEEP_ANALYSIS);
        }

        // 3. Send user notification
        auto notification_result = notification_system_->send_safe_file_notification(results);
        if (notification_result.success) {
            result.notifications_sent = notification_result.notifications_sent;
        }

        // 4. Update database records
        // This would typically involve updating a safe files database

        update_statistics("safe_files");
        std::cout << "[ResponseEngine] Safe file strategy completed successfully" << std::endl;

    } catch (const std::exception& e) {
        result.success = false;
        result.error_message = std::string("Safe file strategy error: ") + e.what();
        update_statistics("errors");
    }

    return result;
}

ResponseResult ResponseEngine::execute_suspicious_file_strategy(const AnalysisResults& results) {
    ResponseResult result;
    result.success = true;
    result.execution_timestamp = std::chrono::system_clock::now();

    try {
        std::cout << "[ResponseEngine] Executing suspicious file strategy for: " << results.file_path << std::endl;

        // 1. Quarantine the file
        auto quarantine_result = quarantine_manager_->quarantine_file(results.file_path, results.metadata);
        if (quarantine_result.success) {
            result.actions_taken.push_back(ResponseAction::QUARANTINE);
            result.quarantine_location = quarantine_result.quarantine_location;
        }

        // 2. Setup honeypot environment
        auto honeypot_result = honeypot_manager_->create_honeypot_environment(results);
        if (honeypot_result.success) {
            result.actions_taken.push_back(ResponseAction::HONEYPOT_REDIRECT);
            result.honeypot_location = honeypot_result.honeypot_location;
        }

        // 3. Restrict access permissions
        auto permission_result = permission_manager_->restrict_access(results.file_path, results.metadata);
        if (permission_result.success) {
            result.actions_taken.push_back(ResponseAction::RESTRICT_ACCESS);
        }

        // 4. Setup deep monitoring
        auto monitor_result = isolation_manager_->setup_deep_monitoring(results.file_path, results.metadata);
        if (monitor_result.success) {
            result.actions_taken.push_back(ResponseAction::DEEP_ANALYSIS);
        }

        // 5. Send notifications
        auto notification_result = notification_system_->send_suspicious_file_alert(results);
        if (notification_result.success) {
            result.notifications_sent = notification_result.notifications_sent;
        }

        update_statistics("suspicious_files");
        std::cout << "[ResponseEngine] Suspicious file strategy completed successfully" << std::endl;

    } catch (const std::exception& e) {
        result.success = false;
        result.error_message = std::string("Suspicious file strategy error: ") + e.what();
        update_statistics("errors");
    }

    return result;
}

ResponseResult ResponseEngine::execute_malicious_file_strategy(const AnalysisResults& results) {
    ResponseResult result;
    result.success = true;
    result.execution_timestamp = std::chrono::system_clock::now();

    try {
        std::cout << "[ResponseEngine] Executing malicious file strategy for: " << results.file_path << std::endl;

        // 1. Immediate quarantine
        auto quarantine_result = quarantine_manager_->immediate_quarantine(results.file_path, results.metadata);
        if (quarantine_result.success) {
            result.actions_taken.push_back(ResponseAction::QUARANTINE);
            result.quarantine_location = quarantine_result.quarantine_location;
        }

        // 2. Isolate related processes
        auto isolation_result = isolation_manager_->isolate_process(results.file_path, results.metadata);
        if (isolation_result.success) {
            result.actions_taken.push_back(ResponseAction::ISOLATE);
            result.isolation_id = isolation_result.isolation_id;
        }

        // 3. Network isolation
        auto network_result = isolation_manager_->isolate_network(results.file_path, results.metadata);
        if (network_result.success) {
            result.actions_taken.push_back(ResponseAction::NETWORK_ISOLATION);
        }

        // 4. Create forensic backup
        auto backup_result = recovery_system_->create_forensic_backup(results);
        if (backup_result.success) {
            result.forensic_data = backup_result.forensic_data;
        }

        // 5. Send critical alerts
        auto notification_result = notification_system_->send_malicious_file_alert(results);
        if (notification_result.success) {
            result.notifications_sent = notification_result.notifications_sent;
        }

        update_statistics("malicious_files");
        std::cout << "[ResponseEngine] Malicious file strategy completed successfully" << std::endl;

    } catch (const std::exception& e) {
        result.success = false;
        result.error_message = std::string("Malicious file strategy error: ") + e.what();
        update_statistics("errors");
    }

    return result;
}

ResponseResult ResponseEngine::execute_critical_threat_strategy(const AnalysisResults& results) {
    ResponseResult result;
    result.success = true;
    result.execution_timestamp = std::chrono::system_clock::now();

    try {
        std::cout << "[ResponseEngine] Executing critical threat strategy for: " << results.file_path << std::endl;

        // 1. Immediate containment
        auto containment_result = isolation_manager_->immediate_containment(results.file_path, results.metadata);
        if (containment_result.success) {
            result.actions_taken.push_back(ResponseAction::ISOLATE);
            result.isolation_id = containment_result.isolation_id;
        }

        // 2. Terminate all related processes
        auto termination_result = isolation_manager_->terminate_all_related_processes(results.file_path, results.metadata);
        if (termination_result.success) {
            result.actions_taken.push_back(ResponseAction::PROCESS_TERMINATION);
        }

        // 3. Complete network isolation
        auto network_result = isolation_manager_->complete_network_isolation(results.metadata);
        if (network_result.success) {
            result.actions_taken.push_back(ResponseAction::NETWORK_ISOLATION);
        }

        // 4. Secure quarantine with encryption
        auto quarantine_result = quarantine_manager_->secure_quarantine(results.file_path, results.metadata);
        if (quarantine_result.success) {
            result.actions_taken.push_back(ResponseAction::QUARANTINE);
            result.quarantine_location = quarantine_result.quarantine_location;
        }

        // 5. Emergency notifications
        auto notification_result = notification_system_->send_critical_threat_alert(results);
        if (notification_result.success) {
            result.notifications_sent = notification_result.notifications_sent;
        }

        // 6. Initiate recovery procedures
        auto recovery_result = recovery_system_->initiate_emergency_recovery(results);
        if (recovery_result.success) {
            result.forensic_data = recovery_result.forensic_data;
        }

        update_statistics("critical_threats");
        std::cout << "[ResponseEngine] Critical threat strategy completed successfully" << std::endl;

    } catch (const std::exception& e) {
        result.success = false;
        result.error_message = std::string("Critical threat strategy error: ") + e.what();
        update_statistics("errors");
    }

    return result;
}

ResponseResult ResponseEngine::execute_advanced_persistent_threat_strategy(const AnalysisResults& results) {
    ResponseResult result;
    result.success = true;
    result.execution_timestamp = std::chrono::system_clock::now();

    try {
        std::cout << "[ResponseEngine] Executing APT strategy for: " << results.file_path << std::endl;

        // 1. System-wide lockdown
        auto lockdown_result = isolation_manager_->system_lockdown(results.metadata);
        if (lockdown_result.success) {
            result.actions_taken.push_back(ResponseAction::SYSTEM_LOCKDOWN);
        }

        // 2. Complete network disconnection
        auto network_result = isolation_manager_->complete_network_disconnection(results.metadata);
        if (network_result.success) {
            result.actions_taken.push_back(ResponseAction::NETWORK_ISOLATION);
        }

        // 3. Comprehensive forensic collection
        auto forensic_result = recovery_system_->comprehensive_forensic_collection(results);
        if (forensic_result.success) {
            result.forensic_data = forensic_result.forensic_data;
        }

        // 4. Emergency response team notification
        auto emergency_result = notification_system_->send_emergency_response_alert(results);
        if (emergency_result.success) {
            result.notifications_sent = emergency_result.notifications_sent;
        }

        // 5. Initiate incident response procedures
        auto incident_result = recovery_system_->initiate_incident_response(results);
        if (incident_result.success) {
            result.forensic_data.insert(incident_result.forensic_data.begin(),
                                      incident_result.forensic_data.end());
        }

        update_statistics("advanced_threats");
        std::cout << "[ResponseEngine] APT strategy completed successfully" << std::endl;

    } catch (const std::exception& e) {
        result.success = false;
        result.error_message = std::string("APT strategy error: ") + e.what();
        update_statistics("errors");
    }

    return result;
}

std::string ResponseEngine::generate_session_id() const {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 15);

    std::stringstream ss;
    ss << "RESP_";
    for (int i = 0; i < 16; ++i) {
        ss << std::hex << dis(gen);
    }

    return ss.str();
}

void ResponseEngine::update_statistics(const std::string& metric, uint64_t value) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    response_statistics_[metric] += value;
}

void ResponseEngine::log_response_action(const ResponseResult& result, const AnalysisResults& analysis) {
    try {
        std::string log_file = config_.base_directory + "/logs/response_actions.log";
        std::ofstream log(log_file, std::ios::app);

        if (log.is_open()) {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);

            log << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "] ";
            log << "File: " << analysis.file_path << " | ";
            log << "Threat: " << static_cast<int>(analysis.threat_level) << " | ";
            log << "Actions: ";

            for (size_t i = 0; i < result.actions_taken.size(); ++i) {
                if (i > 0) log << ",";
                log << static_cast<int>(result.actions_taken[i]);
            }

            log << " | Success: " << (result.success ? "true" : "false");
            if (!result.error_message.empty()) {
                log << " | Error: " << result.error_message;
            }
            log << std::endl;
        }

    } catch (const std::exception& e) {
        std::cerr << "[ResponseEngine] Logging error: " << e.what() << std::endl;
    }
}

bool ResponseEngine::initialize_components() {
    try {
        // Initialize Isolation Manager
        isolation_manager_ = std::make_unique<IsolationManager>(config_);
        if (!isolation_manager_->initialize()) {
            std::cerr << "[ResponseEngine] Failed to initialize Isolation Manager" << std::endl;
            return false;
        }

        // Initialize Notification System
        notification_system_ = std::make_unique<NotificationSystem>(config_);
        if (!notification_system_->initialize()) {
            std::cerr << "[ResponseEngine] Failed to initialize Notification System" << std::endl;
            return false;
        }

        // Initialize Quarantine Manager
        quarantine_manager_ = std::make_unique<QuarantineManager>(config_);
        if (!quarantine_manager_->initialize()) {
            std::cerr << "[ResponseEngine] Failed to initialize Quarantine Manager" << std::endl;
            return false;
        }

        // Initialize Permission Manager
        permission_manager_ = std::make_unique<PermissionManager>(config_);
        if (!permission_manager_->initialize()) {
            std::cerr << "[ResponseEngine] Failed to initialize Permission Manager" << std::endl;
            return false;
        }

        // Initialize Honeypot Manager
        honeypot_manager_ = std::make_unique<HoneypotManager>(config_);
        if (!honeypot_manager_->initialize()) {
            std::cerr << "[ResponseEngine] Failed to initialize Honeypot Manager" << std::endl;
            return false;
        }

        // Initialize Recovery System
        recovery_system_ = std::make_unique<RecoverySystem>(config_);
        if (!recovery_system_->initialize()) {
            std::cerr << "[ResponseEngine] Failed to initialize Recovery System" << std::endl;
            return false;
        }

        std::cout << "[ResponseEngine] All components initialized successfully" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[ResponseEngine] Component initialization error: " << e.what() << std::endl;
        return false;
    }
}

void ResponseEngine::cleanup_components() {
    try {
        if (recovery_system_) {
            recovery_system_->shutdown();
            recovery_system_.reset();
        }

        if (honeypot_manager_) {
            honeypot_manager_->shutdown();
            honeypot_manager_.reset();
        }

        if (permission_manager_) {
            permission_manager_->shutdown();
            permission_manager_.reset();
        }

        if (quarantine_manager_) {
            quarantine_manager_->shutdown();
            quarantine_manager_.reset();
        }

        if (notification_system_) {
            notification_system_->shutdown();
            notification_system_.reset();
        }

        if (isolation_manager_) {
            isolation_manager_->shutdown();
            isolation_manager_.reset();
        }

        std::cout << "[ResponseEngine] All components cleaned up" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[ResponseEngine] Component cleanup error: " << e.what() << std::endl;
    }
}

#ifdef _WIN32
bool ResponseEngine::initialize_windows_security() {
    try {
        // Initialize Windows-specific security features
        std::cout << "[ResponseEngine] Initializing Windows security features..." << std::endl;

        // Enable required privileges
        HANDLE token;
        if (OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &token)) {
            TOKEN_PRIVILEGES tp;
            tp.PrivilegeCount = 1;
            tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

            // Enable debug privilege for process monitoring
            if (LookupPrivilegeValue(NULL, SE_DEBUG_NAME, &tp.Privileges[0].Luid)) {
                AdjustTokenPrivileges(token, FALSE, &tp, sizeof(tp), NULL, NULL);
            }

            // Enable security privilege for access control
            if (LookupPrivilegeValue(NULL, SE_SECURITY_NAME, &tp.Privileges[0].Luid)) {
                AdjustTokenPrivileges(token, FALSE, &tp, sizeof(tp), NULL, NULL);
            }

            CloseHandle(token);
        }

        std::cout << "[ResponseEngine] Windows security features initialized" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[ResponseEngine] Windows security initialization error: " << e.what() << std::endl;
        return false;
    }
}

void ResponseEngine::cleanup_windows_security() {
    try {
        std::cout << "[ResponseEngine] Cleaning up Windows security features..." << std::endl;
        // Cleanup Windows-specific resources
        std::cout << "[ResponseEngine] Windows security cleanup complete" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[ResponseEngine] Windows security cleanup error: " << e.what() << std::endl;
    }
}

#else

bool ResponseEngine::initialize_linux_security() {
    try {
        std::cout << "[ResponseEngine] Initializing Linux security features..." << std::endl;

        // Check for required capabilities
        // This would typically involve checking for CAP_SYS_ADMIN, CAP_NET_ADMIN, etc.

        // Initialize SELinux integration if enabled
        if (config_.selinux_integration_enabled) {
            // SELinux initialization code would go here
            std::cout << "[ResponseEngine] SELinux integration enabled" << std::endl;
        }

        std::cout << "[ResponseEngine] Linux security features initialized" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[ResponseEngine] Linux security initialization error: " << e.what() << std::endl;
        return false;
    }
}

void ResponseEngine::cleanup_linux_security() {
    try {
        std::cout << "[ResponseEngine] Cleaning up Linux security features..." << std::endl;
        // Cleanup Linux-specific resources
        std::cout << "[ResponseEngine] Linux security cleanup complete" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[ResponseEngine] Linux security cleanup error: " << e.what() << std::endl;
    }
}

#endif

// ResponseEngineFactory implementation
std::unique_ptr<ResponseEngine> ResponseEngineFactory::create(const ResponseConfig& config) {
    try {
        auto engine = std::make_unique<ResponseEngine>(config);
        if (engine->initialize()) {
            return engine;
        }
        return nullptr;

    } catch (const std::exception& e) {
        std::cerr << "[ResponseEngineFactory] Creation error: " << e.what() << std::endl;
        return nullptr;
    }
}

ResponseConfig ResponseEngineFactory::create_default_config() {
    ResponseConfig config;

    // General settings
    config.enabled = true;
    config.base_directory = "response_data";
    config.log_level = "INFO";

    // Isolation settings
    config.network_isolation_enabled = true;
    config.process_isolation_enabled = true;
    config.file_system_isolation_enabled = true;

    // Notification settings
    config.email_notifications_enabled = false;
    config.slack_notifications_enabled = false;
    config.sms_notifications_enabled = false;
    config.webhook_notifications_enabled = false;

    // Quarantine settings
    config.quarantine_directory = "response_data/quarantine";
    config.encryption_enabled = true;
    config.encryption_key = "default_key_change_in_production";

    // Honeypot settings
    config.honeypot_directory = "response_data/honeypot";
    config.honeypot_enabled = true;
    config.honeypot_environments = {"generic", "windows", "linux"};

    // Permission settings
    config.dynamic_permissions_enabled = true;
    config.apploader_integration_enabled = false;
    config.selinux_integration_enabled = false;

    // Recovery settings
    config.auto_recovery_enabled = true;
    config.backup_directory = "response_data/backup";
    config.backup_retention_days = 30;

    // Advanced settings
    config.max_concurrent_responses = 10;
    config.response_timeout_seconds = 300;
    config.blockchain_integration_enabled = false;
    config.ml_model_updates_enabled = true;

    return config;
}

ResponseConfig ResponseEngineFactory::load_config_from_file(const std::string& config_file) {
    ResponseConfig config = create_default_config();

    try {
        std::ifstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "[ResponseEngineFactory] Could not open config file: " << config_file << std::endl;
            return config;
        }

        // Simple JSON-like parsing (in production, use a proper JSON library)
        std::string line;
        while (std::getline(file, line)) {
            // Remove whitespace
            line.erase(std::remove_if(line.begin(), line.end(), ::isspace), line.end());

            if (line.find("\"enabled\":true") != std::string::npos) {
                config.enabled = true;
            } else if (line.find("\"enabled\":false") != std::string::npos) {
                config.enabled = false;
            }
            // Add more parsing logic as needed
        }

        std::cout << "[ResponseEngineFactory] Configuration loaded from: " << config_file << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[ResponseEngineFactory] Config loading error: " << e.what() << std::endl;
    }

    return config;
}

bool ResponseEngineFactory::save_config_to_file(const ResponseConfig& config, const std::string& config_file) {
    try {
        std::ofstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "[ResponseEngineFactory] Could not create config file: " << config_file << std::endl;
            return false;
        }

        // Simple JSON-like output (in production, use a proper JSON library)
        file << "{\n";
        file << "  \"enabled\": " << (config.enabled ? "true" : "false") << ",\n";
        file << "  \"base_directory\": \"" << config.base_directory << "\",\n";
        file << "  \"log_level\": \"" << config.log_level << "\",\n";
        file << "  \"network_isolation_enabled\": " << (config.network_isolation_enabled ? "true" : "false") << ",\n";
        file << "  \"process_isolation_enabled\": " << (config.process_isolation_enabled ? "true" : "false") << ",\n";
        file << "  \"quarantine_directory\": \"" << config.quarantine_directory << "\",\n";
        file << "  \"honeypot_directory\": \"" << config.honeypot_directory << "\",\n";
        file << "  \"max_concurrent_responses\": " << config.max_concurrent_responses << ",\n";
        file << "  \"response_timeout_seconds\": " << config.response_timeout_seconds << "\n";
        file << "}\n";

        std::cout << "[ResponseEngineFactory] Configuration saved to: " << config_file << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[ResponseEngineFactory] Config saving error: " << e.what() << std::endl;
        return false;
    }
}