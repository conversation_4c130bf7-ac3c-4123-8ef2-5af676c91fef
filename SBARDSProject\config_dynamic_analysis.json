{"scanner": {"target_directory": "samples", "recursive": true, "max_depth": 5, "exclude_dirs": [], "exclude_extensions": [], "max_file_size_mb": 100}, "rules": {"rule_files": ["rules/custom_rules.yar"], "enable_categories": ["all"]}, "output": {"log_directory": "logs", "output_directory": "output", "json_output": true, "csv_output": false, "html_report": false, "log_level": "info"}, "performance": {"threads": 1, "batch_size": 10, "timeout_seconds": 30}, "dynamic_analysis": {"enabled": true, "analysis_timeout_seconds": 300, "startup_timeout_seconds": 30, "sandbox": {"type": "hybrid", "docker_enabled": true, "vm_enabled": true, "container_image": "sbards/analysis:latest", "vm_snapshots": {"windows10": "win10_clean", "ubuntu20": "ubuntu20_clean", "macos": "macos_clean"}, "network_isolation": true, "file_isolation": true, "memory_isolation": true, "escape_prevention": true}, "monitoring": {"api_hooking": {"enabled": true, "hook_level": "kernel", "monitor_syscalls": true, "monitor_winapi": true, "log_parameters": true, "log_return_values": true}, "file_system": {"enabled": true, "monitor_creation": true, "monitor_modification": true, "monitor_deletion": true, "monitor_access_patterns": true, "detect_encryption": true}, "network": {"enabled": true, "packet_capture": true, "protocol_analysis": true, "detect_c2_communication": true, "dns_monitoring": true, "tls_analysis": true}, "process": {"enabled": true, "monitor_creation": true, "monitor_injection": true, "monitor_hollowing": true, "track_parent_child": true}, "registry": {"enabled": true, "monitor_modifications": true, "track_persistence": true, "detect_security_bypass": true}, "memory": {"enabled": true, "periodic_dumps": true, "dump_interval_seconds": 60, "analyze_heap": true, "analyze_stack": true, "detect_injection": true, "volatility_analysis": true}}, "behavioral_analysis": {"enabled": true, "ml_enabled": true, "ml_model_path": "models/behavioral_analysis.pkl", "ransomware_detection": {"enabled": true, "file_encryption_threshold": 10, "shadow_copy_monitoring": true, "backup_deletion_detection": true}, "resource_analysis": {"cpu_monitoring": true, "memory_monitoring": true, "io_monitoring": true, "network_monitoring": true, "anomaly_detection": true}, "pattern_detection": {"persistence_mechanisms": true, "evasion_techniques": true, "communication_patterns": true, "data_exfiltration": true}}, "user_simulation": {"enabled": true, "install_applications": true, "simulate_interactions": true, "time_delays": true, "realistic_data": true, "applications": ["office_suite", "web_browser", "pdf_reader", "media_player"]}, "cpp_components": {"enabled": true, "sandbox_engine": "scanner_core/cpp/sandbox_engine", "api_hooking": "scanner_core/cpp/api_hooking", "memory_analyzer": "scanner_core/cpp/memory_analyzer", "performance_monitor": "scanner_core/cpp/performance_monitor"}, "security": {"encrypted_communication": true, "secure_storage": true, "access_control": true, "audit_logging": true, "sandbox_hardening": true}, "performance": {"parallel_analysis": true, "max_concurrent_analyses": 4, "resource_limits": {"max_memory_mb": 4096, "max_cpu_percent": 80, "max_disk_io_mbps": 100}, "caching": {"enabled": true, "cache_results": true, "cache_duration_hours": 24}}}}