#!/usr/bin/env python3
"""
SBARDS Unified Response System - Simple Test
"""

print("🚀 SBARDS Unified Response System - Simple Test")
print("=" * 55)

try:
    print("\n📋 Test 1: Import Response System")
    from response import ResponseSystem
    print("✅ ResponseSystem imported successfully")
    
    print("\n📋 Test 2: Initialize System")
    config = {
        "response": {
            "base_directory": "simple_test_unified",
            "quarantine_directory": "simple_test_unified/quarantine",
            "honeypot_directory": "simple_test_unified/honeypot",
            "encryption_enabled": True
        }
    }
    
    system = ResponseSystem(config)
    print("✅ ResponseSystem initialized successfully")
    
    print("\n📋 Test 3: Process Analysis Results")
    analysis_results = {
        "workflow_id": "simple_unified_test_001",
        "file_path": "test_malware.exe",
        "threat_assessment": {
            "overall_threat_level": "high",
            "threat_score": 0.9
        },
        "detected_threats": ["trojan", "malware"],
        "final_decision": {
            "decision": "QUARANTINED",
            "reason": "High threat level detected"
        },
        "phases": {
            "capture": {
                "file_info": {
                    "original_filename": "test_malware.exe",
                    "file_size": 2048,
                    "file_hash": "abc123def456"
                }
            }
        }
    }
    
    result = system.process_analysis_results(analysis_results)
    print(f"✅ Analysis processed successfully: {result.get('success', False)}")
    
    if result.get("success", False):
        print(f"🎯 Action taken: {result.get('action_taken', 'N/A')}")
        print(f"🔧 Legacy system: {result.get('legacy_system', False)}")
        print(f"📁 Operation ID: {result.get('operation_id', 'N/A')}")
    
    print("\n📋 Test 4: Performance Metrics")
    metrics = system.get_performance_metrics()
    print(f"📊 Metrics available: {len(metrics)} metrics")
    print(f"📈 Operation count: {metrics.get('operation_count', 0)}")
    print(f"🔧 Unified system available: {metrics.get('unified_system_available', False)}")
    print(f"🔄 Legacy fallback available: {metrics.get('legacy_fallback_available', False)}")
    
    print("\n📋 Test 5: Response History")
    history = system.get_response_history()
    print(f"📚 Response history: {len(history)} entries")
    
    print("\n" + "=" * 55)
    print("🎉 SBARDS Unified Response System Test Completed!")
    print("✅ All tests passed successfully")
    print("🛡️ Response system is operational")
    print("📊 Performance monitoring active")
    print("🔧 System ready for threat response")
    print("=" * 55)
    
except Exception as e:
    print(f"\n❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()
    print("\n💡 This may be normal if C++ core is not built yet")
    print("🛠️  Run: python build_unified_system.py")
